{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/history.vue?9c0a", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/history.vue?5373", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/history.vue?cfdb", "uni-app:///pagesB/coze/history.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/history.vue?8567", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/history.vue?c893"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "userInfo", "conversationList", "currentMessages", "currentConversation", "deleteConversationId", "pagenum", "nomore", "nodata", "onLoad", "onReachBottom", "onPullDownRefresh", "methods", "getConversationList", "that", "app", "pagesize", "uni", "item", "title", "icon", "duration", "openConversation", "getConversationMessages", "conversation_id", "closeConversationDetail", "continueConversation", "url", "deleteConversation", "confirmDelete", "closeConfirmDelete", "clearAllHistory", "confirmClearAll", "resolve", "Promise", "closeClearConfirm", "formatTime", "date"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,yOAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzEA;AAAA;AAAA;AAAA;AAAomB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkGxnB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;MACA;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;QACA;MACA;MAEA;MACAC;MACAA;MACAA;MAEAC;QACAT;QACAU;MACA;QACAF;QACAG;QAEA;UACA;;UAEA;UACAnB;YACAoB;YACAA;UACA;UAEA;YACAJ;YACA;cACAA;YACA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;QACA;UACAG;YACAE;YACAC;YACAC;UACA;UACA;YACAP;UACA;QACA;QACAA;MACA;IACA;IAEA;IACAQ;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAT;MAEAC;QACAS;QACAlB;QACAU;MACA;QACAF;QACA;UACAA;UACAA;QACA;UACAG;YACAE;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAI;MACA;IACA;IAEA;IACAC;MACA;MACAT;QACAU;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAf;MAEAC;QACAS;MACA;QACAV;QACA;UACAG;YACAE;YACAC;YACAC;UACA;UACAP;QACA;UACAG;YACAE;YACAC;YACAC;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAS;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACAlB;;MAEA;MACA;QACA;UACAC;YACAS;UACA;YACAS;UACA;QACA;MACA;MAEAC;QACApB;QACAG;UACAE;UACAC;UACAC;QACA;QACAP;MACA;MAEA;IACA;IAEA;IACAqB;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MAEA;QAAA;QACA;MACA;QAAA;QACA;MACA;QAAA;QACA;MACA;QAAA;QACA;MACA;QACA,kCACA,0DACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1UA;AAAA;AAAA;AAAA;AAAu3B,CAAgB,s2BAAG,EAAC,C;;;;;;;;;;;ACA34B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesB/coze/history.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesB/coze/history.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./history.vue?vue&type=template&id=8786f822&\"\nvar renderjs\nimport script from \"./history.vue?vue&type=script&lang=js&\"\nexport * from \"./history.vue?vue&type=script&lang=js&\"\nimport style0 from \"./history.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesB/coze/history.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./history.vue?vue&type=template&id=8786f822&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup-dialog/uni-popup-dialog\" */ \"@/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.conversationList, function (conversation, index) {\n        var $orig = _vm.__get_orig(conversation)\n        var g0 = JSON.stringify(conversation)\n        return {\n          $orig: $orig,\n          g0: g0,\n        }\n      })\n    : null\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./history.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./history.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<!-- 顶部导航 -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header-title\">对话历史</view>\n\t\t\t<view class=\"header-actions\">\n\t\t\t\t<view class=\"action-btn\" @tap=\"clearAllHistory\">\n\t\t\t\t\t<text class=\"iconfont iconqingchu\"></text>\n\t\t\t\t\t<text>清空</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 对话列表 -->\n\t\t<view class=\"conversation-list\">\n\t\t\t<block v-for=\"(conversation, index) in conversationList\" :key=\"index\">\n\t\t\t\t<view class=\"conversation-item\" @tap=\"openConversation\" :data-conversation=\"JSON.stringify(conversation)\">\n\t\t\t\t\t<view class=\"conversation-header\">\n\t\t\t\t\t\t<view class=\"conversation-title\">\n\t\t\t\t\t\t\t{{conversation.title || '对话 ' + (index + 1)}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"conversation-time\">{{conversation.update_time_text}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"conversation-preview\" v-if=\"conversation.last_message\">\n\t\t\t\t\t\t{{conversation.last_message}}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"conversation-actions\">\n\t\t\t\t\t\t<view class=\"action-item\" @tap.stop=\"deleteConversation\" :data-conversation-id=\"conversation.conversation_id\">\n\t\t\t\t\t\t\t<text class=\"iconfont iconshanchu\"></text>\n\t\t\t\t\t\t\t<text>删除</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t\t<nodata v-if=\"nodata\"></nodata>\n\t\t</view>\n\n\t\t<!-- 对话详情弹窗 -->\n\t\t<uni-popup ref=\"conversationDetailPopup\" type=\"bottom\">\n\t\t\t<view class=\"detail-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<view class=\"popup-title\">对话详情</view>\n\t\t\t\t\t<view class=\"popup-close\" @tap=\"closeConversationDetail\">\n\t\t\t\t\t\t<text class=\"iconfont iconguanbi\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<scroll-view class=\"detail-content\" scroll-y=\"true\">\n\t\t\t\t\t<view class=\"message-list\">\n\t\t\t\t\t\t<block v-for=\"(message, index) in currentMessages\" :key=\"index\">\n\t\t\t\t\t\t\t<view class=\"message-item\" :class=\"message.role === 'user' ? 'user-message' : 'bot-message'\">\n\t\t\t\t\t\t\t\t<view class=\"message-avatar\">\n\t\t\t\t\t\t\t\t\t<image v-if=\"message.role === 'user'\" :src=\"userInfo.avatar || '/static/img/default-user.png'\"></image>\n\t\t\t\t\t\t\t\t\t<image v-else src=\"/static/img/default-bot.png\"></image>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"message-content\">\n\t\t\t\t\t\t\t\t\t<view class=\"message-text\">{{message.content}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"message-time\">{{message.create_time_text}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t\t<view class=\"popup-footer\">\n\t\t\t\t\t<view class=\"btn-cancel\" @tap=\"closeConversationDetail\">关闭</view>\n\t\t\t\t\t<view class=\"btn-confirm\" :style=\"{background: t('color1')}\" @tap=\"continueConversation\">继续对话</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\n\t\t<!-- 确认删除弹窗 -->\n\t\t<uni-popup ref=\"confirmDeletePopup\" type=\"dialog\">\n\t\t\t<uni-popup-dialog \n\t\t\t\ttype=\"confirm\" \n\t\t\t\ttitle=\"确认删除\" \n\t\t\t\tcontent=\"确定要删除这个对话吗？删除后无法恢复。\"\n\t\t\t\t@confirm=\"confirmDelete\"\n\t\t\t\t@close=\"closeConfirmDelete\"\n\t\t\t></uni-popup-dialog>\n\t\t</uni-popup>\n\n\t\t<!-- 清空历史确认弹窗 -->\n\t\t<uni-popup ref=\"confirmClearPopup\" type=\"dialog\">\n\t\t\t<uni-popup-dialog \n\t\t\t\ttype=\"confirm\" \n\t\t\t\ttitle=\"确认清空\" \n\t\t\t\tcontent=\"确定要清空所有对话历史吗？清空后无法恢复。\"\n\t\t\t\t@confirm=\"confirmClearAll\"\n\t\t\t\t@close=\"closeClearConfirm\"\n\t\t\t></uni-popup-dialog>\n\t\t</uni-popup>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tisload: false,\n\t\t\tloading: false,\n\t\t\tuserInfo: {},\n\t\t\tconversationList: [],\n\t\t\tcurrentMessages: [],\n\t\t\tcurrentConversation: {},\n\t\t\tdeleteConversationId: '',\n\t\t\tpagenum: 1,\n\t\t\tnomore: false,\n\t\t\tnodata: false\n\t\t};\n\t},\n\n\tonLoad: function(opt) {\n\t\tthis.userInfo = app.getUserInfo();\n\t\tthis.getConversationList();\n\t},\n\n\tonReachBottom: function() {\n\t\tif (!this.nodata && !this.nomore) {\n\t\t\tthis.pagenum = this.pagenum + 1;\n\t\t\tthis.getConversationList(true);\n\t\t}\n\t},\n\n\tonPullDownRefresh: function() {\n\t\tthis.getConversationList();\n\t},\n\n\tmethods: {\n\t\t// 获取对话列表\n\t\tgetConversationList: function(loadmore) {\n\t\t\tif (!loadmore) {\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.conversationList = [];\n\t\t\t}\n\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n\n\t\t\tapp.post('ApiCoze/getconversations', {\n\t\t\t\tpagenum: that.pagenum,\n\t\t\t\tpagesize: 20\n\t\t\t}, function(res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tuni.stopPullDownRefresh();\n\n\t\t\t\tif (res.code === 1) {\n\t\t\t\t\tvar data = res.data || [];\n\t\t\t\t\t\n\t\t\t\t\t// 处理数据\n\t\t\t\t\tdata.forEach(function(item) {\n\t\t\t\t\t\titem.update_time_text = that.formatTime(item.update_time);\n\t\t\t\t\t\titem.create_time_text = that.formatTime(item.create_time);\n\t\t\t\t\t});\n\n\t\t\t\t\tif (that.pagenum === 1) {\n\t\t\t\t\t\tthat.conversationList = data;\n\t\t\t\t\t\tif (data.length === 0) {\n\t\t\t\t\t\t\tthat.nodata = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (data.length === 0) {\n\t\t\t\t\t\t\tthat.nomore = true;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthat.conversationList = that.conversationList.concat(data);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\tif (that.pagenum === 1) {\n\t\t\t\t\t\tthat.nodata = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\n\t\t// 打开对话详情\n\t\topenConversation: function(e) {\n\t\t\tvar conversation = JSON.parse(e.currentTarget.dataset.conversation);\n\t\t\tthis.currentConversation = conversation;\n\t\t\tthis.getConversationMessages(conversation.conversation_id);\n\t\t},\n\n\t\t// 获取对话消息\n\t\tgetConversationMessages: function(conversationId) {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\n\t\t\tapp.post('ApiCoze/getmessages', {\n\t\t\t\tconversation_id: conversationId,\n\t\t\t\tpagenum: 1,\n\t\t\t\tpagesize: 100\n\t\t\t}, function(res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.code === 1) {\n\t\t\t\t\tthat.currentMessages = res.data || [];\n\t\t\t\t\tthat.$refs.conversationDetailPopup.open();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 关闭对话详情\n\t\tcloseConversationDetail: function() {\n\t\t\tthis.$refs.conversationDetailPopup.close();\n\t\t},\n\n\t\t// 继续对话\n\t\tcontinueConversation: function() {\n\t\t\tthis.closeConversationDetail();\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pagesB/coze/chat?conversation_id=' + this.currentConversation.conversation_id\n\t\t\t});\n\t\t},\n\n\t\t// 删除对话\n\t\tdeleteConversation: function(e) {\n\t\t\tthis.deleteConversationId = e.currentTarget.dataset.conversationId;\n\t\t\tthis.$refs.confirmDeletePopup.open();\n\t\t},\n\n\t\t// 确认删除\n\t\tconfirmDelete: function() {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\n\t\t\tapp.post('ApiCoze/deleteconversation', {\n\t\t\t\tconversation_id: that.deleteConversationId\n\t\t\t}, function(res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.code === 1) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\tthat.getConversationList();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tthis.closeConfirmDelete();\n\t\t},\n\n\t\t// 关闭删除确认\n\t\tcloseConfirmDelete: function() {\n\t\t\tthis.$refs.confirmDeletePopup.close();\n\t\t},\n\n\t\t// 清空所有历史\n\t\tclearAllHistory: function() {\n\t\t\tthis.$refs.confirmClearPopup.open();\n\t\t},\n\n\t\t// 确认清空所有\n\t\tconfirmClearAll: function() {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\n\t\t\t// 批量删除所有对话\n\t\t\tvar deletePromises = that.conversationList.map(function(conversation) {\n\t\t\t\treturn new Promise(function(resolve) {\n\t\t\t\t\tapp.post('ApiCoze/deleteconversation', {\n\t\t\t\t\t\tconversation_id: conversation.conversation_id\n\t\t\t\t\t}, function(res) {\n\t\t\t\t\t\tresolve(res);\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t});\n\n\t\t\tPromise.all(deletePromises).then(function() {\n\t\t\t\tthat.loading = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '清空成功',\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t\tthat.getConversationList();\n\t\t\t});\n\n\t\t\tthis.closeClearConfirm();\n\t\t},\n\n\t\t// 关闭清空确认\n\t\tcloseClearConfirm: function() {\n\t\t\tthis.$refs.confirmClearPopup.close();\n\t\t},\n\n\t\t// 格式化时间\n\t\tformatTime: function(timestamp) {\n\t\t\tvar date = new Date(timestamp * 1000);\n\t\t\tvar now = new Date();\n\t\t\tvar diff = now - date;\n\t\t\t\n\t\t\tif (diff < 60000) { // 1分钟内\n\t\t\t\treturn '刚刚';\n\t\t\t} else if (diff < 3600000) { // 1小时内\n\t\t\t\treturn Math.floor(diff / 60000) + '分钟前';\n\t\t\t} else if (diff < 86400000) { // 24小时内\n\t\t\t\treturn Math.floor(diff / 3600000) + '小时前';\n\t\t\t} else if (diff < 604800000) { // 7天内\n\t\t\t\treturn Math.floor(diff / 86400000) + '天前';\n\t\t\t} else {\n\t\t\t\treturn date.getFullYear() + '-' + \n\t\t\t\t\t   (date.getMonth() + 1).toString().padStart(2, '0') + '-' + \n\t\t\t\t\t   date.getDate().toString().padStart(2, '0');\n\t\t\t}\n\t\t}\n\t}\n};\n</script>\n\n<style>\n.container {\n\tbackground: #f5f5f5;\n\tmin-height: 100vh;\n}\n\n.header {\n\tbackground: #fff;\n\tpadding: 20rpx 30rpx;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tborder-bottom: 1rpx solid #eee;\n\tposition: fixed;\n\ttop: var(--window-top);\n\tleft: 0;\n\tright: 0;\n\tz-index: 100;\n}\n\n.header-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.header-actions {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.action-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 10rpx 20rpx;\n\tbackground: #f0f0f0;\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n.action-btn .iconfont {\n\tmargin-right: 10rpx;\n\tfont-size: 28rpx;\n}\n\n.conversation-list {\n\tpadding: 30rpx;\n\tmargin-top: 120rpx;\n}\n\n.conversation-item {\n\tbackground: #fff;\n\tborder-radius: 16rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.conversation-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.conversation-title {\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tflex: 1;\n}\n\n.conversation-time {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.conversation-preview {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.4;\n\tmargin-bottom: 20rpx;\n\tdisplay: -webkit-box;\n\t-webkit-box-orient: vertical;\n\t-webkit-line-clamp: 2;\n\toverflow: hidden;\n}\n\n.conversation-actions {\n\tdisplay: flex;\n\tjustify-content: flex-end;\n}\n\n.action-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 10rpx 20rpx;\n\tbackground: #f5f5f5;\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n\tcolor: #ff4757;\n}\n\n.action-item .iconfont {\n\tmargin-right: 8rpx;\n\tfont-size: 24rpx;\n}\n\n.detail-popup {\n\tbackground: #fff;\n\tborder-radius: 20rpx 20rpx 0 0;\n\tmax-height: 80vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.popup-header {\n\tpadding: 30rpx;\n\tborder-bottom: 1rpx solid #eee;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.popup-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.popup-close {\n\tfont-size: 32rpx;\n\tcolor: #999;\n}\n\n.detail-content {\n\tflex: 1;\n\tpadding: 30rpx;\n\tmax-height: 50vh;\n}\n\n.message-list {\n\tpadding-bottom: 20rpx;\n}\n\n.message-item {\n\tdisplay: flex;\n\tmargin-bottom: 30rpx;\n}\n\n.user-message {\n\tflex-direction: row-reverse;\n}\n\n.message-avatar {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tmargin: 0 20rpx;\n}\n\n.message-avatar image {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: 30rpx;\n}\n\n.message-content {\n\tmax-width: 70%;\n}\n\n.user-message .message-content {\n\ttext-align: right;\n}\n\n.message-text {\n\tbackground: #fff;\n\tpadding: 20rpx;\n\tborder-radius: 20rpx;\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tword-wrap: break-word;\n\tborder: 1rpx solid #eee;\n}\n\n.user-message .message-text {\n\tbackground: #007aff;\n\tcolor: #fff;\n\tborder: none;\n}\n\n.message-time {\n\tfont-size: 20rpx;\n\tcolor: #999;\n\tmargin-top: 10rpx;\n}\n\n.popup-footer {\n\tpadding: 30rpx;\n\tborder-top: 1rpx solid #eee;\n\tdisplay: flex;\n\tjustify-content: space-between;\n}\n\n.btn-cancel {\n\tflex: 1;\n\theight: 80rpx;\n\tline-height: 80rpx;\n\ttext-align: center;\n\tbackground: #f5f5f5;\n\tborder-radius: 12rpx;\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tmargin-right: 20rpx;\n}\n\n.btn-confirm {\n\tflex: 1;\n\theight: 80rpx;\n\tline-height: 80rpx;\n\ttext-align: center;\n\tborder-radius: 12rpx;\n\tfont-size: 28rpx;\n\tcolor: #fff;\n}\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./history.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./history.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753981160894\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}