<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

// +----------------------------------------------------------------------
// | 梦想启蒙前端接口
// +----------------------------------------------------------------------
namespace app\controller;
use think\facade\Db;

class ApiDreamInspiration extends ApiCommon
{
    //获取设置信息
    public function getSetting(){
        $setting = Db::name('dream_inspiration_settings')->where(['aid'=>aid])->find();
        if(!$setting || !$setting['is_enabled']){
            return json(['code'=>0,'msg'=>'梦想启蒙功能未开启']);
        }
        
        $data = [
            'is_enabled' => $setting['is_enabled'],
            'welcome_text' => $setting['welcome_text'],
            'workflow_id' => $setting['workflow_id'],
            'workflow_name' => $setting['workflow_name']
        ];
        
        return json(['code'=>1,'msg'=>'获取成功','data'=>$data]);
    }
    
    //提交梦想并生成图片
    public function generateImage(){
        // 验证用户登录
        if(!$this->mid){
            return json(['code'=>0,'msg'=>'请先登录']);
        }
        
        $gender = input('gender', 0, 'intval'); // 0女 1男
        $dreamContent = input('dream_content', '', 'trim');
        $imageUrl = input('image_url', '', 'trim'); // 用户上传的图片URL

        // 验证参数
        if(empty($dreamContent)){
            return json(['code'=>0,'msg'=>'请输入您的梦想']);
        }

        if(!in_array($gender, [0, 1])){
            return json(['code'=>0,'msg'=>'请选择性别']);
        }

        if(empty($imageUrl)){
            return json(['code'=>0,'msg'=>'请上传您的照片']);
        }
        
        // 获取设置
        $setting = Db::name('dream_inspiration_settings')->where(['aid'=>aid])->find();
        if(!$setting || !$setting['is_enabled']){
            return json(['code'=>0,'msg'=>'梦想启蒙功能未开启']);
        }
        
        if(empty($setting['workflow_id'])){
            return json(['code'=>0,'msg'=>'系统配置错误，请联系管理员']);
        }
        
        // 获取用户信息
        $userInfo = $this->member;
        if(!$userInfo){
            return json(['code'=>0,'msg'=>'用户信息不存在']);
        }
        
        // 生成提示词
        $genderText = $gender == 1 ? '男' : '女';
        $promptText = str_replace(['{gender}', '{dream}'], [$genderText, $dreamContent], $setting['prompt_template']);
        
        // 保存记录
        $recordData = [
            'aid' => aid,
            'mid' => $this->mid,
            'nickname' => $userInfo['nickname'] ?? '',
            'avatar' => $userInfo['avatar'] ?? '',
            'gender' => $gender,
            'dream_content' => $dreamContent,
            'user_image_url' => $imageUrl, // 用户上传的图片URL
            'workflow_id' => $setting['workflow_id'],
            'workflow_name' => $setting['workflow_name'],
            'execute_id' => '', // 工作流执行ID，稍后更新
            'prompt_text' => $promptText,
            'status' => 0, // 生成中
            'create_time' => time()
        ];

        $recordId = Db::name('dream_inspiration_records')->insertGetId($recordData);

        // 调用工作流生成图片（异步处理）
        $this->executeWorkflow($recordId, $setting['workflow_id'], $dreamContent, $imageUrl, $gender);
        
        return json(['code'=>1,'msg'=>'梦想已提交，正在为您生成专属图片...','data'=>['record_id'=>$recordId]]);
    }
    
    //获取用户的梦想记录列表
    public function getMyRecords(){
        // 验证用户登录
        if(!$this->mid){
            return json(['code'=>0,'msg'=>'请先登录']);
        }
        
        $page = input('page', 1, 'intval');
        $limit = input('limit', 10, 'intval');
        
        $where = [
            ['aid', '=', aid],
            ['mid', '=', $this->mid]
        ];
        
        $count = Db::name('dream_inspiration_records')->where($where)->count();
        $list = Db::name('dream_inspiration_records')
            ->where($where)
            ->order('id desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
        
        foreach($list as $k => $v){
            $list[$k]['gender_text'] = $v['gender'] == 1 ? '男' : '女';
            $list[$k]['status_text'] = $v['status'] == 1 ? '已生成' : '生成中';
            $list[$k]['create_time_text'] = date('Y-m-d H:i:s', $v['create_time']);
        }
        
        return json(['code'=>1,'msg'=>'获取成功','data'=>$list,'count'=>$count]);
    }

    //查询工作流生成状态
    public function checkGenerationStatus(){
        // 验证用户登录
        if(!$this->mid){
            return json(['code'=>0,'msg'=>'请先登录']);
        }

        $recordId = input('record_id', 0, 'intval');
        if(!$recordId){
            return json(['code'=>0,'msg'=>'记录ID不能为空']);
        }

        $record = Db::name('dream_inspiration_records')
            ->where(['id'=>$recordId, 'aid'=>aid, 'mid'=>$this->mid])
            ->find();

        if(!$record){
            return json(['code'=>0,'msg'=>'记录不存在']);
        }

        // 如果状态是生成中且有执行ID，尝试查询工作流状态
        if($record['status'] == 0 && !empty($record['execute_id'])){
            $this->queryAndUpdateWorkflowResult($record);
            // 重新查询记录获取最新状态
            $record = Db::name('dream_inspiration_records')
                ->where(['id'=>$recordId, 'aid'=>aid, 'mid'=>$this->mid])
                ->find();
        }

        // 状态文本映射
        $statusText = [
            0 => '生成中',
            1 => '已生成',
            2 => '生成失败'
        ];

        $data = [
            'record_id' => $record['id'],
            'status' => $record['status'],
            'status_text' => $statusText[$record['status']] ?? '未知状态',
            'result_image' => $record['result_image'] ?? '',
            'error_msg' => $record['error_msg'] ?? '',
            'create_time' => $record['create_time'],
            'create_time_text' => date('Y-m-d H:i:s', $record['create_time']),
            'dream_content' => $record['dream_content'],
            'user_image_url' => $record['user_image_url'] ?? '',
            'gender' => $record['gender'],
            'gender_text' => $record['gender'] == 1 ? '男' : '女',
            'execute_id' => $record['execute_id'] ?? ''
        ];

        // 记录查询日志
        $timestamp = date('Y-m-d H:i:s');
        \think\facade\Log::write($timestamp.'-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=' . $recordId . ', 状态=' . $record['status'] . ', 用户ID=' . $this->mid, 'info');

        return json(['code'=>1,'msg'=>'查询成功','data'=>$data]);
    }

    //获取记录详情
    public function getRecordDetail(){
        // 记录接口调用日志，用于排查重复调用问题
        $timestamp = date('Y-m-d H:i:s');
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        $httpHost = $_SERVER['HTTP_HOST'] ?? '';
        $recordId = input('record_id', 0, 'intval');

        \think\facade\Log::write($timestamp.'-INFO-[ApiDreamInspiration][getRecordDetail] 接口被调用 - 用户ID:'.$this->mid.' - 记录ID:'.$recordId.' - Host:'.$httpHost.' - URI:'.$requestUri.' - Referer:'.$referer, 'info');

        // 临时禁用此接口，防止重复调用（调试用）
        // return json(['code'=>0,'msg'=>'接口暂时禁用，请使用 checkGenerationStatus 接口']);

        // 验证用户登录
        if(!$this->mid){
            return json(['code'=>0,'msg'=>'请先登录']);
        }

        $recordId = input('record_id', 0, 'intval');
        if(!$recordId){
            return json(['code'=>0,'msg'=>'参数错误']);
        }
        
        $record = Db::name('dream_inspiration_records')
            ->where(['id'=>$recordId, 'aid'=>aid, 'mid'=>$this->mid])
            ->find();
        
        if(!$record){
            return json(['code'=>0,'msg'=>'记录不存在']);
        }
        
        $record['gender_text'] = $record['gender'] == 1 ? '男' : '女';
        $record['status_text'] = $record['status'] == 1 ? '已生成' : '生成中';
        $record['create_time_text'] = date('Y-m-d H:i:s', $record['create_time']);
        $record['update_time_text'] = $record['update_time'] ? date('Y-m-d H:i:s', $record['update_time']) : '';
        
        return json(['code'=>1,'msg'=>'获取成功','data'=>$record]);
    }

    //查询并更新工作流结果（私有方法）
    private function queryAndUpdateWorkflowResult($record){
        try {
            $timestamp = date('Y-m-d H:i:s');
            \think\facade\Log::write($timestamp.'-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=' . $record['execute_id'], 'info');

            // 调用Coze工作流查询接口
            $result = \app\common\Coze::queryWorkflowResult(aid, $record['workflow_id'], $record['execute_id']);

            \think\facade\Log::write($timestamp.'-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: ' . json_encode($result, JSON_UNESCAPED_UNICODE), 'info');

            if($result['code'] == 1 && isset($result['data'])){
                $responseData = $result['data'];

                // 检查返回数据格式
                if(isset($responseData['data']) && is_array($responseData['data']) && !empty($responseData['data'])){
                    $workflowData = $responseData['data'][0]; // 取第一个工作流记录

                    // 检查工作流是否完成
                    if(isset($workflowData['execute_status']) && $workflowData['execute_status'] === 'Success'){
                        // 工作流执行成功，解析结果
                        $imageUrl = '';

                        // 解析输出结果
                        if(isset($workflowData['output'])){
                            $output = $workflowData['output'];
                            if(is_string($output)){
                                $outputData = json_decode($output, true);

                                // 格式1: 直接在output中查找image
                                if($outputData && isset($outputData['image'])){
                                    $imageUrl = $outputData['image'];
                                } elseif($outputData && isset($outputData['image_url'])){
                                    $imageUrl = $outputData['image_url'];
                                }

                                // 格式2: 在Output字段中查找（注意大写O）
                                if(empty($imageUrl) && isset($outputData['Output'])){
                                    $innerOutput = $outputData['Output'];
                                    if(is_string($innerOutput)){
                                        $innerData = json_decode($innerOutput, true);
                                        if($innerData && isset($innerData['image'])){
                                            $imageUrl = $innerData['image'];
                                        } elseif($innerData && isset($innerData['image_url'])){
                                            $imageUrl = $innerData['image_url'];
                                        }
                                    }
                                }
                            }
                        }

                        // 更新记录状态
                        $updateData = [
                            'result_image' => $imageUrl,
                            'workflow_result' => json_encode($responseData, JSON_UNESCAPED_UNICODE),
                            'status' => !empty($imageUrl) ? 1 : 2, // 有图片URL则成功，否则失败
                            'update_time' => time()
                        ];

                        if(empty($imageUrl)){
                            $updateData['error_msg'] = '工作流执行成功但未找到图片URL';
                        } else {
                            $updateData['error_msg'] = ''; // 清空错误信息
                        }

                        Db::name('dream_inspiration_records')->where(['id'=>$record['id']])->update($updateData);

                        \think\facade\Log::write($timestamp.'-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流完成，更新状态: 图片URL=' . $imageUrl . ', 记录ID=' . $record['id'], 'info');

                    } elseif(isset($workflowData['execute_status']) && $workflowData['execute_status'] === 'Failed'){
                        // 工作流执行失败
                        $updateData = [
                            'status' => 2, // 生成失败
                            'error_msg' => '工作流执行失败',
                            'workflow_result' => json_encode($responseData, JSON_UNESCAPED_UNICODE),
                            'update_time' => time()
                        ];

                        Db::name('dream_inspiration_records')->where(['id'=>$record['id']])->update($updateData);

                        \think\facade\Log::write($timestamp.'-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行失败', 'info');
                    } else {
                        // 工作流还在执行中，记录当前状态
                        $currentStatus = $workflowData['execute_status'] ?? 'Unknown';
                        \think\facade\Log::write($timestamp.'-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: ' . $currentStatus, 'info');
                    }
                } else {
                    \think\facade\Log::write($timestamp.'-ERROR-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 返回数据格式错误: ' . json_encode($responseData, JSON_UNESCAPED_UNICODE), 'error');
                }
            }

        } catch (\Exception $e) {
            $timestamp = date('Y-m-d H:i:s');
            \think\facade\Log::write($timestamp.'-ERROR-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询工作流结果异常: ' . $e->getMessage(), 'error');
        }
    }

    //执行工作流（私有方法）
    private function executeWorkflow($recordId, $workflowId, $dreamContent, $imageUrl = '', $gender = 1){
        try {
            // 获取用户职业信息
            $userInfo = $this->member;
            $profession = $userInfo['zhiye'] ?? '医生'; // 默认职业为医生

            // 记录调试日志
            $timestamp = date('Y-m-d H:i:s');
            \think\facade\Log::write($timestamp.'-DEBUG-[ApiDreamInspiration][executeWorkflow] 开始调用工作流: 记录ID=' . $recordId . ', 工作流ID=' . $workflowId . ', 性别=' . $gender . ', 职业=' . $profession, 'info');

            // 获取Coze配置
            $cozeConfig = Db::name('coze_config')->where(['aid'=>aid,'status'=>1])->find();
            if(!$cozeConfig){
                throw new \Exception('Coze配置不存在');
            }

            // 按照您提供的格式准备工作流参数
            $parameters = [
                'BOT_USER_INPUT' => '', // 空字符串
                'gender' => $gender == 1 ? '男' : '女', // 转换为中文
                'image_url' => $imageUrl,
                'zhiye' => $profession // 职业信息
            ];

            // 记录参数日志
            $timestamp = date('Y-m-d H:i:s');
            \think\facade\Log::write($timestamp.'-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流参数: ' . json_encode($parameters, JSON_UNESCAPED_UNICODE), 'info');

            // 调用Coze工作流API
            $result = \app\common\Coze::runWorkflow(aid, $workflowId, $parameters, $this->mid);

            // 记录调用结果
            $timestamp = date('Y-m-d H:i:s');
            \think\facade\Log::write($timestamp.'-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流调用结果: code=' . ($result['code'] ?? 'unknown') . ', msg=' . ($result['msg'] ?? 'no message') . ', has_data=' . (isset($result['data']) ? 'yes' : 'no'), 'info');
            
            if($result['code'] == 1 && isset($result['data'])){
                $data = $result['data'];

                // 记录原始返回数据用于调试
                $timestamp = date('Y-m-d H:i:s');
                \think\facade\Log::write($timestamp.'-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流原始返回数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');

                // 检查是否有执行ID（异步执行）
                if(isset($data['execute_id']) && !empty($data['execute_id'])){
                    // 异步执行，保存执行ID，状态设为生成中
                    $updateData = [
                        'execute_id' => $data['execute_id'],
                        'workflow_result' => json_encode($result['data'], JSON_UNESCAPED_UNICODE),
                        'status' => 0, // 生成中
                        'update_time' => time()
                    ];

                    \think\facade\Log::write($timestamp.'-DEBUG-[ApiDreamInspiration][executeWorkflow] 异步执行，保存执行ID: ' . $data['execute_id'], 'info');

                } else {
                    // 同步执行，立即解析结果
                    $imageUrl = '';

                    // 根据实际返回格式解析图片URL
                    // 格式1: data.output 字段
                    if(isset($data['output'])){
                        $output = $data['output'];
                        if(is_string($output)){
                            // 如果是字符串，尝试解析JSON
                            $outputData = json_decode($output, true);
                            if($outputData && isset($outputData['image'])){
                                $imageUrl = $outputData['image'];
                            } elseif($outputData && isset($outputData['image_url'])){
                                $imageUrl = $outputData['image_url'];
                            }
                        }elseif(is_array($output)){
                            if(isset($output['image'])){
                                $imageUrl = $output['image'];
                            } elseif(isset($output['image_url'])){
                                $imageUrl = $output['image_url'];
                            }
                        }
                    }

                    // 格式2: data.Output 字段（注意大写O）
                    if(empty($imageUrl) && isset($data['Output'])){
                        $output = $data['Output'];
                        if(is_string($output)){
                            $outputData = json_decode($output, true);
                            if($outputData && isset($outputData['image'])){
                                $imageUrl = $outputData['image'];
                            } elseif($outputData && isset($outputData['image_url'])){
                                $imageUrl = $outputData['image_url'];
                            }
                        }
                    }

                    // 格式3: 直接在data中
                    if(empty($imageUrl)){
                        if(isset($data['image'])){
                            $imageUrl = $data['image'];
                        } elseif(isset($data['image_url'])){
                            $imageUrl = $data['image_url'];
                        }
                    }

                    // 记录解析结果
                    \think\facade\Log::write($timestamp.'-DEBUG-[ApiDreamInspiration][executeWorkflow] 同步执行，图片URL解析结果: extracted_image_url=' . $imageUrl . ', has_image_url=' . (!empty($imageUrl) ? 'yes' : 'no'), 'info');

                    // 更新记录状态
                    $updateData = [
                        'result_image' => $imageUrl,
                        'workflow_result' => json_encode($result['data'], JSON_UNESCAPED_UNICODE),
                        'status' => !empty($imageUrl) ? 1 : 2, // 有图片URL则成功，否则失败
                        'update_time' => time()
                    ];

                    if(empty($imageUrl)){
                        $updateData['error_msg'] = '工作流执行成功但未找到图片URL';
                    }
                }

                Db::name('dream_inspiration_records')->where(['id'=>$recordId])->update($updateData);
                
            }else{
                // 工作流执行失败
                $errorMsg = $result['msg'] ?? '工作流执行失败';
                Db::name('dream_inspiration_records')->where(['id'=>$recordId])->update([
                    'error_msg' => $errorMsg,
                    'status' => 0,
                    'update_time' => time()
                ]);
            }
            
        } catch (\Exception $e) {
            // 异常处理
            Db::name('dream_inspiration_records')->where(['id'=>$recordId])->update([
                'error_msg' => $e->getMessage(),
                'status' => 0,
                'update_time' => time()
            ]);
        }
    }
}
