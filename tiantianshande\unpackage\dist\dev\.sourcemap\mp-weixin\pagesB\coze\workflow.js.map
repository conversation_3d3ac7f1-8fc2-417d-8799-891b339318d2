{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/workflow.vue?160e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/workflow.vue?aaac", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/workflow.vue?684f", "uni-app:///pagesB/coze/workflow.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/workflow.vue?e341", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/workflow.vue?4082"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "workflowList", "selectedWorkflow", "paramMode", "paramList", "paramV<PERSON><PERSON>", "jsonParams", "isAsync", "currentSelectParam", "workflowResult", "nodata", "onLoad", "methods", "getWorkflowList", "that", "app", "uni", "title", "icon", "duration", "selectWorkflow", "getWorkflowParams", "workflow_id", "closeParamPopup", "showSelectOptions", "closeSelectPopup", "selectOption", "toggleAsync", "chooseFile", "count", "success", "name", "path", "size", "runWorkflow", "params", "is_async", "closeResultPopup"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpGA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsLznB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAC;MACAC;QACAD;QACA;UACAA;UACA;YACAA;UACA;QACA;UACAE;YACAC;YACAC;YACAC;UACA;UACAL;QACA;QACAA;MACA;IACA;IAEA;IACAM;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAP;MACAC;QACAO;MACA;QACAR;QACA;UACAA;UACA;YACAA;YACAA;YACA;YACAA;cACA;gBACAA;cACA;YACA;UACA;YACAA;UACA;UACAA;QACA;UACAE;YACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAI;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEAZ;QACAa;QACAC;UACA;UACAhB;YACAiB;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;QACA;QAAA,2CACA;UAAA;QAAA;UAAA;YAAA;YACA;cACAlB;gBACAC;gBACAC;gBACAC;cACA;cACA;YACA;UACA;QAAA;UAAA;QAAA;UAAA;QAAA;QACAgB;MACA;QACA;UACAA;QACA;UACAnB;YACAC;YACAC;YACAC;UACA;UACA;QACA;MACA;MAEA;MACAL;MACAC;QACAO;QACAa;QACAC;MACA;QACAtB;QACA;UACA;YACAE;cACAC;cACAC;cACAC;YACA;YACAL;UACA;YACAA;YACAA;YACAA;UACA;QACA;UACAE;YACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAkB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjYA;AAAA;AAAA;AAAA;AAAw3B,CAAgB,u2BAAG,EAAC,C;;;;;;;;;;;ACA54B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesB/coze/workflow.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesB/coze/workflow.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./workflow.vue?vue&type=template&id=c029d718&\"\nvar renderjs\nimport script from \"./workflow.vue?vue&type=script&lang=js&\"\nexport * from \"./workflow.vue?vue&type=script&lang=js&\"\nimport style0 from \"./workflow.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesB/coze/workflow.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./workflow.vue?vue&type=template&id=c029d718&\"", "var components\ntry {\n  components = {\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.workflowList, function (workflow, index) {\n        var $orig = _vm.__get_orig(workflow)\n        var g0 = JSON.stringify(workflow)\n        var m0 = _vm.t(\"color1\")\n        return {\n          $orig: $orig,\n          g0: g0,\n          m0: m0,\n        }\n      })\n    : null\n  var l1 =\n    _vm.isload && _vm.paramMode === \"custom\"\n      ? _vm.__map(_vm.paramList, function (param, index) {\n          var $orig = _vm.__get_orig(param)\n          var g1 =\n            !(param.param_type === \"text\") &&\n            !(param.param_type === \"number\") &&\n            param.param_type === \"select\"\n              ? JSON.stringify(param)\n              : null\n          return {\n            $orig: $orig,\n            g1: g1,\n          }\n        })\n      : null\n  var m1 = _vm.isload && _vm.isAsync ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload ? _vm.t(\"color1\") : null\n  var l2 = _vm.isload\n    ? _vm.__map(_vm.currentSelectParam.param_options, function (option, index) {\n        var $orig = _vm.__get_orig(option)\n        var m3 =\n          _vm.paramValues[_vm.currentSelectParam.param_key] === option.value\n            ? _vm.t(\"color1\")\n            : null\n        return {\n          $orig: $orig,\n          m3: m3,\n        }\n      })\n    : null\n  var m4 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n        m1: m1,\n        m2: m2,\n        l2: l2,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./workflow.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./workflow.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<!-- 顶部导航 -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header-title\">扣子工作流</view>\n\t\t\t<view class=\"header-actions\">\n\t\t\t\t<view class=\"action-btn\" @tap=\"goto\" data-url=\"/pagesB/coze/workflow-logs\">\n\t\t\t\t\t<text class=\"iconfont iconlishi\"></text>\n\t\t\t\t\t<text>执行记录</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 工作流列表 -->\n\t\t<view class=\"workflow-list\">\n\t\t\t<block v-for=\"(workflow, index) in workflowList\" :key=\"index\">\n\t\t\t\t<view class=\"workflow-item\" @tap=\"selectWorkflow\" :data-workflow=\"JSON.stringify(workflow)\">\n\t\t\t\t\t<view class=\"workflow-header\">\n\t\t\t\t\t\t<view class=\"workflow-name\">{{workflow.name}}</view>\n\t\t\t\t\t\t<view class=\"workflow-status\" :style=\"{color: t('color1')}\">\n\t\t\t\t\t\t\t<text class=\"iconfont iconzhengchang\"></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"workflow-desc\" v-if=\"workflow.description\">{{workflow.description}}</view>\n\t\t\t\t\t<view class=\"workflow-id\">ID: {{workflow.workflow_id}}</view>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t\t<nodata v-if=\"nodata\"></nodata>\n\t\t</view>\n\n\t\t<!-- 工作流参数配置弹窗 -->\n\t\t<uni-popup ref=\"paramPopup\" type=\"bottom\">\n\t\t\t<view class=\"param-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<view class=\"popup-title\">{{selectedWorkflow.name}}</view>\n\t\t\t\t\t<view class=\"popup-close\" @tap=\"closeParamPopup\">\n\t\t\t\t\t\t<text class=\"iconfont iconguanbi\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<scroll-view class=\"param-content\" scroll-y=\"true\">\n\t\t\t\t\t<view class=\"param-form\">\n\t\t\t\t\t\t<!-- 自定义参数模式 -->\n\t\t\t\t\t\t<block v-if=\"paramMode === 'custom'\">\n\t\t\t\t\t\t\t<block v-for=\"(param, index) in paramList\" :key=\"index\">\n\t\t\t\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t\t\t\t<view class=\"form-label\">\n\t\t\t\t\t\t\t\t\t\t{{param.param_name}}\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"param.is_required\" class=\"required\">*</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t<!-- 文本输入 -->\n\t\t\t\t\t\t\t\t\t<view v-if=\"param.param_type === 'text'\" class=\"form-input\">\n\t\t\t\t\t\t\t\t\t\t<input \n\t\t\t\t\t\t\t\t\t\t\tv-model=\"paramValues[param.param_key]\" \n\t\t\t\t\t\t\t\t\t\t\t:placeholder=\"param.placeholder || '请输入' + param.param_name\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999\"\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t<!-- 数字输入 -->\n\t\t\t\t\t\t\t\t\t<view v-else-if=\"param.param_type === 'number'\" class=\"form-input\">\n\t\t\t\t\t\t\t\t\t\t<input \n\t\t\t\t\t\t\t\t\t\t\tv-model=\"paramValues[param.param_key]\" \n\t\t\t\t\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t\t\t\t\t\t:placeholder=\"param.placeholder || '请输入' + param.param_name\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999\"\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t<!-- 选择器 -->\n\t\t\t\t\t\t\t\t\t<view v-else-if=\"param.param_type === 'select'\" class=\"form-select\" @tap=\"showSelectOptions\" :data-param=\"JSON.stringify(param)\">\n\t\t\t\t\t\t\t\t\t\t<text :class=\"paramValues[param.param_key] ? '' : 'placeholder'\">\n\t\t\t\t\t\t\t\t\t\t\t{{paramValues[param.param_key] || '请选择' + param.param_name}}\n\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"iconfont iconxiala\"></text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t<!-- 文本域 -->\n\t\t\t\t\t\t\t\t\t<view v-else-if=\"param.param_type === 'textarea'\" class=\"form-textarea\">\n\t\t\t\t\t\t\t\t\t\t<textarea \n\t\t\t\t\t\t\t\t\t\t\tv-model=\"paramValues[param.param_key]\" \n\t\t\t\t\t\t\t\t\t\t\t:placeholder=\"param.placeholder || '请输入' + param.param_name\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999\"\n\t\t\t\t\t\t\t\t\t\t\tauto-height\n\t\t\t\t\t\t\t\t\t\t></textarea>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t<!-- 文件上传 -->\n\t\t\t\t\t\t\t\t\t<view v-else-if=\"param.param_type === 'file'\" class=\"form-file\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"file-upload-btn\" @tap=\"chooseFile\" :data-param-key=\"param.param_key\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"iconfont iconshangchuan\"></text>\n\t\t\t\t\t\t\t\t\t\t\t<text>选择文件</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"paramValues[param.param_key]\" class=\"file-name\">\n\t\t\t\t\t\t\t\t\t\t\t{{paramValues[param.param_key].name}}\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t<view v-if=\"param.param_desc\" class=\"form-desc\">{{param.param_desc}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- JSON模式 -->\n\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t\t\t<view class=\"form-label\">参数配置</view>\n\t\t\t\t\t\t\t\t<view class=\"form-textarea\">\n\t\t\t\t\t\t\t\t\t<textarea \n\t\t\t\t\t\t\t\t\t\tv-model=\"jsonParams\" \n\t\t\t\t\t\t\t\t\t\tplaceholder=\"请输入JSON格式的参数\"\n\t\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999\"\n\t\t\t\t\t\t\t\t\t\tauto-height\n\t\t\t\t\t\t\t\t\t></textarea>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"form-desc\">请输入有效的JSON格式参数</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 异步执行选项 -->\n\t\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t\t<view class=\"form-checkbox\" @tap=\"toggleAsync\">\n\t\t\t\t\t\t\t\t<text :class=\"'iconfont ' + (isAsync ? 'iconduihao' : 'iconweixuanzhong')\" :style=\"isAsync ? {color: t('color1')} : {}\"></text>\n\t\t\t\t\t\t\t\t<text>异步执行</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"form-desc\">异步执行不会立即返回结果，需要通过执行记录查看结果</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t\t\n\t\t\t\t<view class=\"popup-footer\">\n\t\t\t\t\t<view class=\"btn-cancel\" @tap=\"closeParamPopup\">取消</view>\n\t\t\t\t\t<view class=\"btn-confirm\" :style=\"{background: t('color1')}\" @tap=\"runWorkflow\">运行工作流</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\n\t\t<!-- 选择器弹窗 -->\n\t\t<uni-popup ref=\"selectPopup\" type=\"bottom\">\n\t\t\t<view class=\"select-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<view class=\"popup-title\">{{currentSelectParam.param_name}}</view>\n\t\t\t\t\t<view class=\"popup-close\" @tap=\"closeSelectPopup\">\n\t\t\t\t\t\t<text class=\"iconfont iconguanbi\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<scroll-view class=\"select-options\" scroll-y=\"true\">\n\t\t\t\t\t<block v-for=\"(option, index) in currentSelectParam.param_options\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"select-option\" @tap=\"selectOption\" :data-value=\"option.value\" :data-param-key=\"currentSelectParam.param_key\">\n\t\t\t\t\t\t\t<text>{{option.label}}</text>\n\t\t\t\t\t\t\t<text v-if=\"paramValues[currentSelectParam.param_key] === option.value\" class=\"iconfont iconduihao\" :style=\"{color: t('color1')}\"></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</uni-popup>\n\n\t\t<!-- 结果显示弹窗 -->\n\t\t<uni-popup ref=\"resultPopup\" type=\"center\">\n\t\t\t<view class=\"result-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<view class=\"popup-title\">执行结果</view>\n\t\t\t\t\t<view class=\"popup-close\" @tap=\"closeResultPopup\">\n\t\t\t\t\t\t<text class=\"iconfont iconguanbi\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<scroll-view class=\"result-content\" scroll-y=\"true\">\n\t\t\t\t\t<view class=\"result-text\">{{workflowResult}}</view>\n\t\t\t\t</scroll-view>\n\t\t\t\t<view class=\"popup-footer\">\n\t\t\t\t\t<view class=\"btn-confirm\" :style=\"{background: t('color1')}\" @tap=\"closeResultPopup\">确定</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tisload: false,\n\t\t\tloading: false,\n\t\t\tworkflowList: [],\n\t\t\tselectedWorkflow: {},\n\t\t\tparamMode: 'custom', // custom 或 json\n\t\t\tparamList: [],\n\t\t\tparamValues: {},\n\t\t\tjsonParams: '',\n\t\t\tisAsync: false,\n\t\t\tcurrentSelectParam: {},\n\t\t\tworkflowResult: '',\n\t\t\tnodata: false\n\t\t};\n\t},\n\n\tonLoad: function(opt) {\n\t\tthis.getWorkflowList();\n\t},\n\n\tmethods: {\n\t\t// 获取工作流列表\n\t\tgetWorkflowList: function() {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiCoze/getWorkflowList', {}, function(res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.code === 1) {\n\t\t\t\t\tthat.workflowList = res.data || [];\n\t\t\t\t\tif (that.workflowList.length === 0) {\n\t\t\t\t\t\tthat.nodata = true;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\tthat.nodata = true;\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\n\t\t// 选择工作流\n\t\tselectWorkflow: function(e) {\n\t\t\tvar workflow = JSON.parse(e.currentTarget.dataset.workflow);\n\t\t\tthis.selectedWorkflow = workflow;\n\t\t\tthis.getWorkflowParams(workflow.workflow_id);\n\t\t},\n\n\t\t// 获取工作流参数\n\t\tgetWorkflowParams: function(workflowId) {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiCoze/getWorkflowParams', {\n\t\t\t\tworkflow_id: workflowId\n\t\t\t}, function(res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.code === 1) {\n\t\t\t\t\tthat.paramMode = res.data.mode;\n\t\t\t\t\tif (res.data.mode === 'custom') {\n\t\t\t\t\t\tthat.paramList = res.data.params || [];\n\t\t\t\t\t\tthat.paramValues = {};\n\t\t\t\t\t\t// 设置默认值\n\t\t\t\t\t\tthat.paramList.forEach(param => {\n\t\t\t\t\t\t\tif (param.default_value) {\n\t\t\t\t\t\t\t\tthat.paramValues[param.param_key] = param.default_value;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthat.jsonParams = JSON.stringify(res.data.params || {}, null, 2);\n\t\t\t\t\t}\n\t\t\t\t\tthat.$refs.paramPopup.open();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 关闭参数弹窗\n\t\tcloseParamPopup: function() {\n\t\t\tthis.$refs.paramPopup.close();\n\t\t},\n\n\t\t// 显示选择器选项\n\t\tshowSelectOptions: function(e) {\n\t\t\tvar param = JSON.parse(e.currentTarget.dataset.param);\n\t\t\tthis.currentSelectParam = param;\n\t\t\tthis.$refs.selectPopup.open();\n\t\t},\n\n\t\t// 关闭选择器弹窗\n\t\tcloseSelectPopup: function() {\n\t\t\tthis.$refs.selectPopup.close();\n\t\t},\n\n\t\t// 选择选项\n\t\tselectOption: function(e) {\n\t\t\tvar value = e.currentTarget.dataset.value;\n\t\t\tvar paramKey = e.currentTarget.dataset.paramKey;\n\t\t\tthis.paramValues[paramKey] = value;\n\t\t\tthis.closeSelectPopup();\n\t\t},\n\n\t\t// 切换异步执行\n\t\ttoggleAsync: function() {\n\t\t\tthis.isAsync = !this.isAsync;\n\t\t},\n\n\t\t// 选择文件\n\t\tchooseFile: function(e) {\n\t\t\tvar paramKey = e.currentTarget.dataset.paramKey;\n\t\t\tvar that = this;\n\t\t\t\n\t\t\tuni.chooseFile({\n\t\t\t\tcount: 1,\n\t\t\t\tsuccess: function(res) {\n\t\t\t\t\tvar file = res.tempFiles[0];\n\t\t\t\t\tthat.paramValues[paramKey] = {\n\t\t\t\t\t\tname: file.name,\n\t\t\t\t\t\tpath: file.path,\n\t\t\t\t\t\tsize: file.size\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 运行工作流\n\t\trunWorkflow: function() {\n\t\t\tif (!this.selectedWorkflow.workflow_id) return;\n\n\t\t\tvar params = {};\n\t\t\tif (this.paramMode === 'custom') {\n\t\t\t\t// 验证必填参数\n\t\t\t\tfor (let param of this.paramList) {\n\t\t\t\t\tif (param.is_required && !this.paramValues[param.param_key]) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: param.param_name + '不能为空',\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tparams = this.paramValues;\n\t\t\t} else {\n\t\t\t\ttry {\n\t\t\t\t\tparams = JSON.parse(this.jsonParams);\n\t\t\t\t} catch (e) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '参数格式错误，请输入有效的JSON',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiCoze/runWorkflowWithParams', {\n\t\t\t\tworkflow_id: that.selectedWorkflow.workflow_id,\n\t\t\t\tparams: params,\n\t\t\t\tis_async: that.isAsync\n\t\t\t}, function(res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.code === 1) {\n\t\t\t\t\tif (that.isAsync) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '工作流已开始异步执行，请在执行记录中查看结果',\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\tduration: 3000\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthat.closeParamPopup();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthat.workflowResult = JSON.stringify(res.data, null, 2);\n\t\t\t\t\t\tthat.closeParamPopup();\n\t\t\t\t\t\tthat.$refs.resultPopup.open();\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 关闭结果弹窗\n\t\tcloseResultPopup: function() {\n\t\t\tthis.$refs.resultPopup.close();\n\t\t}\n\t}\n};\n</script>\n\n<style>\n.container {\n\tbackground: #f5f5f5;\n\tmin-height: 100vh;\n}\n\n.header {\n\tbackground: #fff;\n\tpadding: 20rpx 30rpx;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tborder-bottom: 1rpx solid #eee;\n\tposition: fixed;\n\ttop: var(--window-top);\n\tleft: 0;\n\tright: 0;\n\tz-index: 100;\n}\n\n.header-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.header-actions {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.action-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 10rpx 20rpx;\n\tbackground: #f0f0f0;\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n.action-btn .iconfont {\n\tmargin-right: 10rpx;\n\tfont-size: 28rpx;\n}\n\n.workflow-list {\n\tpadding: 30rpx;\n\tmargin-top: 120rpx;\n}\n\n.workflow-item {\n\tbackground: #fff;\n\tborder-radius: 16rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.workflow-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.workflow-name {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.workflow-status {\n\tfont-size: 24rpx;\n}\n\n.workflow-desc {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tmargin-bottom: 15rpx;\n\tline-height: 1.5;\n}\n\n.workflow-id {\n\tfont-size: 22rpx;\n\tcolor: #999;\n}\n\n.param-popup {\n\tbackground: #fff;\n\tborder-radius: 20rpx 20rpx 0 0;\n\tmax-height: 80vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.popup-header {\n\tpadding: 30rpx;\n\tborder-bottom: 1rpx solid #eee;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.popup-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.popup-close {\n\tfont-size: 32rpx;\n\tcolor: #999;\n}\n\n.param-content {\n\tflex: 1;\n\tpadding: 0 30rpx;\n}\n\n.param-form {\n\tpadding: 20rpx 0;\n}\n\n.form-item {\n\tmargin-bottom: 40rpx;\n}\n\n.form-label {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n}\n\n.required {\n\tcolor: #ff4757;\n\tmargin-left: 5rpx;\n}\n\n.form-input {\n\tbackground: #f5f5f5;\n\tborder-radius: 12rpx;\n\tpadding: 0 20rpx;\n}\n\n.form-input input {\n\theight: 80rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.form-select {\n\tbackground: #f5f5f5;\n\tborder-radius: 12rpx;\n\tpadding: 0 20rpx;\n\theight: 80rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.form-select .placeholder {\n\tcolor: #999;\n}\n\n.form-textarea {\n\tbackground: #f5f5f5;\n\tborder-radius: 12rpx;\n\tpadding: 20rpx;\n}\n\n.form-textarea textarea {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmin-height: 120rpx;\n}\n\n.form-file {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.file-upload-btn {\n\tbackground: #f5f5f5;\n\tborder-radius: 12rpx;\n\tpadding: 20rpx 30rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\n.file-upload-btn .iconfont {\n\tmargin-right: 10rpx;\n}\n\n.file-name {\n\tmargin-left: 20rpx;\n\tfont-size: 24rpx;\n\tcolor: #333;\n}\n\n.form-checkbox {\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.form-checkbox .iconfont {\n\tmargin-right: 15rpx;\n\tfont-size: 32rpx;\n}\n\n.form-desc {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tmargin-top: 10rpx;\n\tline-height: 1.4;\n}\n\n.popup-footer {\n\tpadding: 30rpx;\n\tborder-top: 1rpx solid #eee;\n\tdisplay: flex;\n\tjustify-content: space-between;\n}\n\n.btn-cancel {\n\tflex: 1;\n\theight: 80rpx;\n\tline-height: 80rpx;\n\ttext-align: center;\n\tbackground: #f5f5f5;\n\tborder-radius: 12rpx;\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tmargin-right: 20rpx;\n}\n\n.btn-confirm {\n\tflex: 1;\n\theight: 80rpx;\n\tline-height: 80rpx;\n\ttext-align: center;\n\tborder-radius: 12rpx;\n\tfont-size: 28rpx;\n\tcolor: #fff;\n}\n\n.select-popup {\n\tbackground: #fff;\n\tborder-radius: 20rpx 20rpx 0 0;\n\tmax-height: 60vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.select-options {\n\tflex: 1;\n\tpadding: 0 30rpx;\n}\n\n.select-option {\n\tpadding: 30rpx 0;\n\tborder-bottom: 1rpx solid #f5f5f5;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.result-popup {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\twidth: 80vw;\n\tmax-height: 70vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.result-content {\n\tflex: 1;\n\tpadding: 30rpx;\n\tmax-height: 50vh;\n}\n\n.result-text {\n\tfont-size: 24rpx;\n\tcolor: #333;\n\tline-height: 1.5;\n\twhite-space: pre-wrap;\n\tword-break: break-all;\n}\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./workflow.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./workflow.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980990398\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}