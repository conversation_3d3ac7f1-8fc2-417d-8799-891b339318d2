<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

// +----------------------------------------------------------------------
// | 自动执行  每分钟执行一次 crontab -e 加入 */1 * * * * curl https://域名/?s=/ApiAuto/index/key/配置文件中的authtoken
// +----------------------------------------------------------------------
namespace app\controller;
use app\BaseController;
use think\facade\Db;
use think\facade\Log;
class ApiAuto extends BaseController
{
    public function initialize(){

	}
	
	/**
	 * 带货团定时
	 * */
	 public function senddaihuotuan()
	 {
	      $aid = input('param.aid');
	      if(!$aid)
	      {
	         die('请传入aid');
	      }
	      $set = Db::name('tuanzhang_sysset')->where('aid',$aid)->find();
	      if(empty($set) && $set['status'] == 0)
	      {
	          die('请先开启带货团');
	      }
	      $where = [];
	      $where[] = ['aid','=',$aid];
	      $where[] = ['t_status','=',0];
    	   if($set['jiesuan'] == 1)
    	   {
    	       //支付后
    	       $where[] = ['status','=','1,2,3'];
    	   }else{
    	       //确认收货
    	       $where[] = ['status','=',3];
    	   }
	      $list  = Db::name('shop_order_goods')->field('id,mid,bid,tid,daiticheng')->where($where)->where('daiticheng','>',0)->select()->toArray();
	      foreach($list as $v)
	      {
	          \app\common\Business::addtuanmoney($aid,$v['tid'],$v['daiticheng'],'带货团提成');
	          Db::name('shop_order_goods')->where('id',$v['id'])->update(['t_status'=>1]);
	      }
	      die('已完成');
	 }
	
	/**
	 * 拓展费
	 * */
	 public function sendtuozhanfei()
	 {
	      $aid = input('param.aid');
	      if(!$aid)
	      {
	         die('请传入aid');
	      }  
	      $set = Db::name('admin_set')->where('aid',$aid)->find();
	      if($set['yihuo_status'] == 1 && $set['yihuo_baifenbi'] >0)
	      {
	          \think\facade\Log::write('【拓展费发放】系统设置检查通过，aid='.$aid.', yihuo_status='.$set['yihuo_status'].', yihuo_baifenbi='.$set['yihuo_baifenbi'], 'info');
	          $where = [];
	          $where[] = ['aid','=',$aid];
	          $where[] = ['tuozhanid','>',0];
	          $where[] = ['tuozhanfei','>',0];
	          $where[] = ['tuozhanfei_status','=',0];
	          if($set['yihuo_jiesuan_status'] == 1)//确认收货
	          {
	              $where2 = $where; 
	              $where[] = ['status','=',3];
	              $where2[] = ['status','=',1];
	              \think\facade\Log::write('【拓展费发放】结算条件: 确认收货后', 'info');
	          }elseif($set['yihuo_jiesuan_status'] == 0){//付款后
	              $where[] = ['status','in','1,2,3'];
	              $where2 = $where;
	              \think\facade\Log::write('【拓展费发放】结算条件: 付款后', 'info');
	          }
	          
	          // 获取结算方式
	          $jiesuan_type = isset($set['yihuo_jiesuan_type']) ? $set['yihuo_jiesuan_type'] : 1; // 默认按商品价格
	          $jiesuan_msg = '按商品价格';
	          if ($jiesuan_type == 2) {
	              $jiesuan_msg = '按成交价格';
	          } elseif ($jiesuan_type == 3) {
	              $jiesuan_msg = '按抽成比例返现';
	          }
	          \think\facade\Log::write('【拓展费发放】结算方式: '. $jiesuan_msg, 'info');
	          
	          // 查询商城订单拓展费
	          $list = Db::name('shop_order_goods')->field('id,mid,bid,tuozhanid,tuozhanfei,tuozhanfei_status,tuozhanid_jian,tuozhanfei_jian,tuozhanid_jianjian,tuozhanfei_jianjian')->where($where)->select()->toArray();
	          \think\facade\Log::write('【拓展费发放】商城订单查询: '.json_encode($where, JSON_UNESCAPED_UNICODE).'，找到 '.count($list).' 条记录', 'info');
	          
	          foreach($list as $v)
	          {
	              \think\facade\Log::write('【拓展费发放】处理商城订单: id='.$v['id'].', mid='.$v['mid'].', bid='.$v['bid'].', tuozhanid='.$v['tuozhanid'].', tuozhanfei='.$v['tuozhanfei'], 'info');
	              \app\common\Member::addtuozhanfei($aid,$v['tuozhanid'],$v['tuozhanfei'],'用户id:'.$v['mid'].'下单直推商户id:'.$v['bid'].'得到拓展费:'.$v['tuozhanfei']);
	              if($v['tuozhanid_jian'] >0 && $v['tuozhanfei_jian'] >0 )
	              {
	                  \think\facade\Log::write('【拓展费发放】处理间推: tuozhanid_jian='.$v['tuozhanid_jian'].', tuozhanfei_jian='.$v['tuozhanfei_jian'], 'info');
	                  \app\common\Member::addtuozhanfei($aid,$v['tuozhanid_jian'],$v['tuozhanfei_jian'],'用户id:'.$v['mid'].'下单间推商户id:'.$v['bid'].'得到拓展费:'.$v['tuozhanfei_jian']);
	              }
	              if($v['tuozhanid_jianjian'] && $v['tuozhanfei_jianjian'])
	              {
	                  \think\facade\Log::write('【拓展费发放】处理间间推: tuozhanid_jianjian='.$v['tuozhanid_jianjian'].', tuozhanfei_jianjian='.$v['tuozhanfei_jianjian'], 'info');
	                  \app\common\Member::addtuozhanfei($aid,$v['tuozhanid_jianjian'],$v['tuozhanfei_jianjian'],'用户id:'.$v['mid'].'下单间间推商户id:'.$v['bid'].'得到拓展费:'.$v['tuozhanfei_jianjian']);
	              }
	               Db::name('shop_order_goods')->where('id',$v['id'])->update(['tuozhanfei_status'=>1]);
	          }
	          
	          // 查询买单拓展费
	          $list2 = Db::name('maidan_order')->field('id,mid,bid,tuozhanid,tuozhanfei,tuozhanfei_status,tuozhanid_jian,tuozhanfei_jian,tuozhanid_jianjian,tuozhanfei_jianjian')->where($where2)->select()->toArray();
	          \think\facade\Log::write('【拓展费发放】买单查询: '.json_encode($where2, JSON_UNESCAPED_UNICODE).'，找到 '.count($list2).' 条记录', 'info');
	          
	          foreach($list2 as $v)
	          {
	              \think\facade\Log::write('【拓展费发放】处理买单: id='.$v['id'].', mid='.$v['mid'].', bid='.$v['bid'].', tuozhanid='.$v['tuozhanid'].', tuozhanfei='.$v['tuozhanfei'], 'info');
	              \app\common\Member::addtuozhanfei($aid,$v['tuozhanid'],$v['tuozhanfei'],'买单-用户id:'.$v['mid'].'下单直推商户id:'.$v['bid'].'得到拓展费:'.$v['tuozhanfei']);
	              if($v['tuozhanid_jian'] >0 && $v['tuozhanfei_jian'] >0 )
	              {
	                  \think\facade\Log::write('【拓展费发放】处理间推: tuozhanid_jian='.$v['tuozhanid_jian'].', tuozhanfei_jian='.$v['tuozhanfei_jian'], 'info');
	                  \app\common\Member::addtuozhanfei($aid,$v['tuozhanid_jian'],$v['tuozhanfei_jian'],'买单-用户id:'.$v['mid'].'下单间推商户id:'.$v['bid'].'得到拓展费:'.$v['tuozhanfei_jian']);
	              }
	              if($v['tuozhanid_jianjian'] && $v['tuozhanfei_jianjian'])
	              {
	                  \think\facade\Log::write('【拓展费发放】处理间间推: tuozhanid_jianjian='.$v['tuozhanid_jianjian'].', tuozhanfei_jianjian='.$v['tuozhanfei_jianjian'], 'info');
	                  \app\common\Member::addtuozhanfei($aid,$v['tuozhanid_jianjian'],$v['tuozhanfei_jianjian'],'买单-用户id:'.$v['mid'].'下单间间推商户id:'.$v['bid'].'得到拓展费:'.$v['tuozhanfei_jianjian']);
	              }
	              Db::name('maidan_order')->where('id',$v['id'])->update(['tuozhanfei_status'=>1]);
	               
	          }
	          die('已完成,请查看');
	      }else{
	           \think\facade\Log::write('【拓展费发放】系统设置检查不通过，aid='.$aid.', yihuo_status='.($set['yihuo_status'] ?? '未设置').', yihuo_baifenbi='.($set['yihuo_baifenbi'] ?? '未设置'), 'info');
	           die('未开启或未设置,请查看');
	      }
	 }
	
	/**
	 * 分红点自动计算
	 * 计划任务链接：/?s=/apiAuto/calcFenhongDian/aid/你的id
	 * 推荐设置为每小时执行一次
	 */
	public function calcFenhongDian()
	{
	    $aid = input('param.aid');
	    if (!$aid) {
	        die('请传入aid');
	    }
	    
	    // 检查并创建fenhong_dian_status字段
	    try {
	        $prefix = config('database.connections.mysql.prefix');
	        $hasField = Db::query("SHOW COLUMNS FROM `{$prefix}shop_order` LIKE 'fenhong_dian_status'");
	        if (empty($hasField)) {
	            \think\facade\Log::write('【分红点计算】开始创建fenhong_dian_status字段', 'info');
	            Db::execute("ALTER TABLE `{$prefix}shop_order` ADD COLUMN `fenhong_dian_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '分红点处理状态 0未处理 1已处理'");
	            \think\facade\Log::write('【分红点计算】成功创建fenhong_dian_status字段', 'info');
	        }
	    } catch (\Exception $e) {
	        \think\facade\Log::write('【分红点计算】检查/创建字段异常：' . $e->getMessage(), 'error');
	        // 如果字段创建失败，尝试使用其他方式标记处理状态
	    }
	    
	    \think\facade\Log::write('【分红点计算】开始执行分红点自动计算，aid=' . $aid, 'info');
	    
	    // 添加调试：检查aid对应的订单总数
	    $totalOrders = Db::name('shop_order')->where('aid', $aid)->count();
	    \think\facade\Log::write('【分红点计算】调试信息 - aid=' . $aid . ' 的订单总数：' . $totalOrders, 'info');
	    
	    // 添加调试：检查不同状态的订单数量
	    $statusCounts = [];
	    for ($i = 0; $i <= 5; $i++) {
	        $count = Db::name('shop_order')->where('aid', $aid)->where('status', $i)->count();
	        if ($count > 0) {
	            $statusCounts[$i] = $count;
	        }
	    }
	    \think\facade\Log::write('【分红点计算】调试信息 - 各状态订单数量：' . json_encode($statusCounts, JSON_UNESCAPED_UNICODE), 'info');
	    
	    // 添加调试：检查fenhong_dian_status字段的分布
	    $fenhongStatusCounts = [];
	    $fenhongStatusCounts[0] = Db::name('shop_order')->where('aid', $aid)->where('fenhong_dian_status', 0)->count();
	    $fenhongStatusCounts[1] = Db::name('shop_order')->where('aid', $aid)->where('fenhong_dian_status', 1)->count();
	    \think\facade\Log::write('【分红点计算】调试信息 - 分红点状态分布：未处理=' . $fenhongStatusCounts[0] . ', 已处理=' . $fenhongStatusCounts[1], 'info');
	    
	    // 获取分红点设置
	    $settings = \app\common\FenhongDian::getSettings($aid);
	    if ($settings['status'] != 1) {
	        \think\facade\Log::write('【分红点计算】分红点功能未开启，aid=' . $aid, 'info');
	        die('分红点功能未开启');
	    }
	    
	    \think\facade\Log::write('【分红点计算】分红点设置检查通过，aid=' . $aid . ', trigger_type=' . $settings['trigger_type'] . ', calc_type=' . $settings['calc_type'], 'info');
	    
	    // 构建查询条件
	    $where = [];
	    $where[] = ['aid', '=', $aid];
	    $where[] = ['fenhong_dian_status', '=', 0]; // 未处理分红点的订单
	    
	    // 根据触发时机设置订单状态条件
	    if ($settings['trigger_type'] == 1) {
	        // 下单后触发：已支付状态
	        $where[] = ['status', 'in', [1, 2, 3]];
	        \think\facade\Log::write('【分红点计算】触发条件：下单后', 'info');
	        
	        // 添加调试：检查每个状态的未处理订单数量
	        for ($status = 1; $status <= 3; $status++) {
	            $count = Db::name('shop_order')
	                ->where('aid', $aid)
	                ->where('fenhong_dian_status', 0)
	                ->where('status', $status)
	                ->count();
	            \think\facade\Log::write('【分红点计算】调试信息 - 状态' . $status . '的未处理订单数：' . $count, 'info');
	        }
	        
	    } elseif ($settings['trigger_type'] == 2) {
	        // 确认收货后触发：已完成状态
	        $where[] = ['status', '=', 3];
	        \think\facade\Log::write('【分红点计算】触发条件：确认收货后', 'info');
	        
	        // 添加调试：检查状态3的未处理订单数量
	        $count = Db::name('shop_order')
	            ->where('aid', $aid)
	            ->where('fenhong_dian_status', 0)
	            ->where('status', 3)
	            ->count();
	        \think\facade\Log::write('【分红点计算】调试信息 - 状态3的未处理订单数：' . $count, 'info');
	    }
	    
	    // 添加调试：查看最近的10个订单详情
	    $recentOrders = Db::name('shop_order')
	        ->where('aid', $aid)
	        ->field('id,status,fenhong_dian_status,createtime,totalprice')
	        ->order('id desc')
	        ->limit(10)
	        ->select()
	        ->toArray();
	    \think\facade\Log::write('【分红点计算】调试信息 - 最近10个订单详情：' . json_encode($recentOrders, JSON_UNESCAPED_UNICODE), 'info');
	    
	    // 查询符合条件的订单
	    $orders = Db::name('shop_order')
	        ->field('id,mid,totalprice,createtime')
	        ->where($where)
	        ->order('id asc')
	        ->limit(100) // 每次处理100个订单，避免超时
	        ->select()
	        ->toArray();
	        
	    \think\facade\Log::write('【分红点计算】查询订单条件：' . json_encode($where, JSON_UNESCAPED_UNICODE) . '，找到 ' . count($orders) . ' 条记录', 'info');
	    
	    // 如果没有找到符合条件的订单，添加更详细的调试信息
	    if (empty($orders)) {
	        \think\facade\Log::write('【分红点计算】未找到符合条件的订单，开始详细检查...', 'info');
	        
	        // 检查是否有aid=122的订单
	        $aidOrderCount = Db::name('shop_order')->where('aid', $aid)->count();
	        \think\facade\Log::write('【分红点计算】aid=' . $aid . ' 的订单总数：' . $aidOrderCount, 'info');
	        
	        // 检查是否有未处理分红点的订单
	        $unprocessedCount = Db::name('shop_order')->where('aid', $aid)->where('fenhong_dian_status', 0)->count();
	        \think\facade\Log::write('【分红点计算】aid=' . $aid . ' 未处理分红点的订单数：' . $unprocessedCount, 'info');
	        
	        // 检查符合状态条件的订单数
	        if ($settings['trigger_type'] == 1) {
	            $statusOrderCount = Db::name('shop_order')->where('aid', $aid)->where('status', 'in', [1, 2, 3])->count();
	            \think\facade\Log::write('【分红点计算】aid=' . $aid . ' 状态在[1,2,3]的订单数：' . $statusOrderCount, 'info');
	        }
	        
	        // 检查是否存在fenhong_dian_status字段
	        try {
	            $prefix = config('database.connections.mysql.prefix');
	            $fieldInfo = Db::query("SHOW COLUMNS FROM `{$prefix}shop_order` LIKE 'fenhong_dian_status'");
	            if (empty($fieldInfo)) {
	                \think\facade\Log::write('【分红点计算】错误 - fenhong_dian_status字段不存在', 'error');
	            } else {
	                \think\facade\Log::write('【分红点计算】fenhong_dian_status字段存在：' . json_encode($fieldInfo, JSON_UNESCAPED_UNICODE), 'info');
	            }
	        } catch (\Exception $e) {
	            \think\facade\Log::write('【分红点计算】检查字段时出错：' . $e->getMessage(), 'error');
	        }
	    }
	    
	    $processedCount = 0;
	    $errorCount = 0;
	    
	    foreach ($orders as $order) {
	        try {
	            \think\facade\Log::write('【分红点计算】处理订单：id=' . $order['id'] . ', mid=' . $order['mid'] . ', totalprice=' . $order['totalprice'], 'info');
	            
	            // 调用分红点计算方法
	            $triggerType = $settings['trigger_type'] == 1 ? 'order_pay' : 'order_receive';
	            $result = \app\common\FenhongDian::calculateAndReward($aid, $order['mid'], $order['id'], $triggerType);
	            
	            if ($result) {
	                // 标记订单已处理分红点
	                Db::name('shop_order')->where('id', $order['id'])->update(['fenhong_dian_status' => 1]);
	                $processedCount++;
	                \think\facade\Log::write('【分红点计算】订单处理成功：id=' . $order['id'], 'info');
	            } else {
	                $errorCount++;
	                \think\facade\Log::write('【分红点计算】订单处理失败：id=' . $order['id'], 'error');
	            }
	            
	        } catch (\Exception $e) {
	            $errorCount++;
	            \think\facade\Log::write('【分红点计算】订单处理异常：id=' . $order['id'] . ', 错误信息：' . $e->getMessage(), 'error');
	        }
	    }
	    
	    \think\facade\Log::write('【分红点计算】执行完成，aid=' . $aid . ', 处理成功：' . $processedCount . ' 个订单，失败：' . $errorCount . ' 个订单', 'info');
	    
	    die('分红点计算完成，处理成功：' . $processedCount . ' 个订单，失败：' . $errorCount . ' 个订单');
	}
	
	/**
	 * 公排定时
	 * */
	 public function sendgongpaijiangli()
	 {
	    $aid = input('param.aid');
	    if(!$aid)
	    {
	         die('请传入aid');
	    }  
	     $set = Db::name('admin_set')->where('aid',$aid)->find();
	     if($set['gongpaizhi_shi'] > 0 && $set['gongpaizhi_ren'] >0 && $set['gongpaizhi_xiaofei'] >0)
	     {
	          $jiangliArr = json_decode($set['gongpaizhi_jiangli'],1);
	          $starttime = strtotime(date('Y-m-d 00:00:00'));
    	      $endtime = strtotime(date('Y-m-d 23:59:59'));
    	      $day = date('Y-m-d');
    	      for($i=1;$i<9;$i++)
    	      {
    	          if($i == 1)
        	     {
        	         $gongpainum = 'gongpainum';
        	         $gongpaipid = 'gongpaipid';
        	         $gongpaitime = 'gongpaitime';
        	         $gongpaistatus = 'gongpaistatus';
        	     }elseif($i == 2)
        	     {
        	         $gongpainum = 'gongpainum2';
        	         $gongpaipid = 'gongpaipid2';
        	         $gongpaitime = 'gongpaitime2';
        	         $gongpaistatus = 'gongpaistatus2';
        	     }elseif($i == 3)
        	     {
        	         $gongpainum = 'gongpainum3';
        	         $gongpaipid = 'gongpaipid3';
        	         $gongpaitime = 'gongpaitime3';
        	         $gongpaistatus = 'gongpaistatus3';
        	     }elseif($i == 4)
        	     {
        	         $gongpainum = 'gongpainum4';
        	         $gongpaipid = 'gongpaipid4';
        	         $gongpaitime = 'gongpaitime4';
        	         $gongpaistatus = 'gongpaistatus4';
        	     }elseif($i == 5)
        	     {
        	         $gongpainum = 'gongpainum5';
        	         $gongpaipid = 'gongpaipid5';
        	         $gongpaitime = 'gongpaitime5';
        	         $gongpaistatus = 'gongpaistatus5';
        	     }elseif($i == 6)
        	     {
        	         $gongpainum = 'gongpainum6';
        	         $gongpaipid = 'gongpaipid6';
        	         $gongpaitime = 'gongpaitime6';
        	         $gongpaistatus = 'gongpaistatus6';
        	     }elseif($i == 7)
        	     {
        	         $gongpainum = 'gongpainum7';
        	         $gongpaipid = 'gongpaipid7';
        	         $gongpaitime = 'gongpaitime7';
        	         $gongpaistatus = 'gongpaistatus7';
        	     }elseif($i == 8)
        	     {
        	         $gongpainum = 'gongpainum8';
        	         $gongpaipid = 'gongpaipid8';
        	         $gongpaitime = 'gongpaitime8';
        	         $gongpaistatus = 'gongpaistatus8';
        	     }
         	      $member = Db::name('member')->where('aid',$aid)->field('id,levelid,nickname')->where($gongpainum,1)->where($gongpaistatus,0)->select()->toArray();
         	      foreach($member as $v)
         	      {
         	          $jianglinum = $jiangliArr[$i]['min'];
         	          \app\common\Member::addcommission($aid,$v['id'],0,$jianglinum,$day.'用户id'.$v['id'].'公排,第'.$i.'轮奖励'.$jianglinum);
         	           Db::name('member')->where('aid',$aid)->where('id',$v['id'])->update([$gongpaistatus=>1]);
         	      }
    	      }
    	     die('已完成');
	     }else{
	         die('未开启或未设置,请查看');
	     }
	 }
	 
	 /**
	  * 销售业绩分红
	  * */
	public function sendxsyjfh()
	{
	    $aid = input('param.aid');
	    if(!$aid)
	    {
	         die('请传入aid');
	    }  
	    $day = date('Y-m-d');
	    $set = Db::name('admin_set')->where('aid',$aid)->find();
	    if($set['xsyj_shi'] ==1 && $set['xsyj_yeji'] > 0 && $set['xsyj_fhd'] >0)
        {
            $idstr = Db::name('shop_order')->where('aid',$aid)->where('xsyjfh','>',0)->where('xsyjfhstatus',0)->where('status','in',[1,2,3])->field('id')->select()->toArray();//奖金池金额
            $idstr = array_column($idstr,'id');
            $idstr = implode(',',$idstr);
            $jiangjinchi = Db::name('shop_order')->where('aid',$aid)->where('xsyjfh','>',0)->where('xsyjfhstatus',0)->where('status','in',[1,2,3])->sum('xsyjfh');//奖金池金额
            $zongdianshu = Db::name('shop_order')->where('aid',$aid)->where('xsyjfh','>',0)->where('status','in',[1,2,3])->sum('dianshu');//总点数
            $dandian = $jiangjinchi/$zongdianshu;
            $dandian = number_format($dandian,2);
            if($zongdianshu >0)
            {
                $count = 0;
                 //这一批
                $memberlist = Db::name('member')->field('id,levelid,pid,jiangcount,zijifh,fxfhc')->where('aid',$aid)->select()->toArray();
                // var_dump($memberlist);die;->where('xsyjfhstatus',0)->where('xsyjfhstatus',0)
                foreach($memberlist as $k=>$v)
                {
                    $dianshud = Db::name('shop_order')->where('aid',$aid)->where('mid',$v['id'])->where('dianshu','>',0)->where('status','in',[1,2,3])->sum('dianshu');
                    $jine = $dandian *$dianshud;//????????天数
                    if($jine >0)
                    {
                        $memberlevel = Db::name('member_level')->where('aid',$aid)->where('id',$v['levelid'])->find();
                        $s = 0;
                        if($memberlevel['zijifh'] == 1)
                        {
                            if($v['zijifh'] >= $jine)
    			            {
    			                $s = 1;
                                //判断自己分红池够不够
                                $count = $count+1;
                                \app\common\Member::addcommission($aid,$v['id'],0,$jine,$day.'用户销售金额点数'.$dianshud.'奖励'.$jine);
                                \app\common\Member::addzijifh($aid,$v['id'],'-'.$jine,'用户销售金额点数奖励,扣除'.$jine);
                                Db::name('shop_order')->where('aid',$aid)->where('xsyjfh','>',0)->where('xsyjfhstatus',0)->where('status','in',[1,2,3])->where('mid',$v['id'])->update(['xsyjfhstatus'=>1]);
    			            }
                        }else{
                             $s = 1;
                            //判断自己分红池够不够
                            $count = $count+1;
                            \app\common\Member::addcommission($aid,$v['id'],0,$jine,$day.'用户销售金额点数'.$dianshud.'奖励'.$jine);
                             Db::name('shop_order')->where('aid',$aid)->where('xsyjfh','>',0)->where('xsyjfhstatus',0)->where('status','in',[1,2,3])->where('mid',$v['id'])->update(['xsyjfhstatus'=>1]);
                        }
                        if($s == 1)
                        {
                       
                            $memberlevel = Db::name('member_level')->where('aid',$aid)->where('id',$v['levelid'])->find();
                            if(!empty($memberlevel))
                            {
                                if($memberlevel['tjmoney'] >0)
                                {
                                    $xiajiArr = Db::name('member')->where('aid',$aid)->where('pid',$v['id'])->field('id')->select()->toArray();
                                    $xiajiArr = array_column($xiajiArr,'id');
                                    $xiajiyejicount = Db::name('shop_order')->where('aid',$aid)->where('mid','in',$xiajiArr)->where('status','in',[1,2,3])->sum('totalprice');
                                    if($xiajiyejicount > $memberlevel['tjmoney'])
                                    {
                                        if($v['jiangcount'] < $memberlevel['tjmoney'])
                                        {
                                           $dedao = $memberlevel['tjmoney'] - $v['jiangcount']; 
                                           if($memberlevel['fxfhc'] == 1)
                                           {
                                               //分享分红池
                                              if($v['fxfhc'] >= $dedao){
                                                  $v['fxfhc'] = $v['fxfhc'] -$dedao;
                                                   //奖励余额
                                                   \app\common\Member::addhei($aid,$v['id'],$dedao,'推荐奖励金额累计'.$memberlevel['tjmoney']);
                                                   //更改拿到的钱数
                                                   Db::name('member')->where('aid',$aid)->where('id',$v['id'])->update(['jiangcount'=>$xiajiyejicount]);
                                                   app\common\Member::addfxfhc($aid,$v['id'],'-'.$dedao,'推荐奖励金额奖励,扣除'.$dedao);
                                              }
                                           }else{
                                              \app\common\Member::addhei($aid,$v['id'],$dedao,'推荐奖励金额累计'.$memberlevel['tjmoney']); 
                                               Db::name('member')->where('aid',$aid)->where('id',$v['id'])->update(['jiangcount'=>$xiajiyejicount]);
                                           }
                                        }
                                        //直接奖励
                                        //到佣金
                                        if($memberlevel['tjmoney_one'] > 0){
                                           $yidai = $jine*$memberlevel['tjmoney_one']*0.01;
                                           if($memberlevel['fxfhc'] == 1)
                                           {
                                                if($v['fxfhc'] >= $yidai){
                                                    $v['fxfhc'] = $v['fxfhc'] -$yidai;
                                                    \app\common\Member::addcommission($aid,$v['id'],$v['id'],$yidai,$day.'一代得到推荐奖励',$yidai);
                                                    \app\common\Member::addfxfhc($aid,$v['id'],'-'.$yidai,'一代得到推荐奖励,扣除'.$yidai);
                                                    if($v['pid'] >0 && $memberlevel['tjmoney_two'] >0)
                                                    {
                                                        $erdaiid = $v['pid'];
                                                        $erdai = $yidai*$memberlevel['tjmoney_two']*0.01;
                                                        if($v['fxfhc'] >= $erdai){
                                                            $v['fxfhc'] = $v['fxfhc'] -$erdai;
                                                            \app\common\Member::addcommission($aid,$erdaiid,$v['id'],$erdai,$day.'二代得到推荐奖励',$erdai);
                                                            \app\common\Member::addfxfhc($aid,$erdaiid,'-'.$erdai,'二代得到推荐奖励,扣除'.$erdai);
                                                        }
                                                    }
                                                }
                                           }else{
                                               \app\common\Member::addcommission($aid,$v['id'],$v['id'],$yidai,$day.'一代得到推荐奖励',$yidai);
                                                if($v['pid'] >0 && $memberlevel['tjmoney_two'] >0)
                                                {
                                                    $erdaiid = $v['pid'];
                                                    $erdaimember = Db::name('member')->field('id,levelid,pid,jiangcount,zijifh,fxfhc')->where('aid',$aid)->where('id',$erdaiid)->find();
                                                    $erdailevel = Db::name('member_level')->where('aid',$aid)->where('id',$erdaimember['levelid'])->find();
                                                    if($erdailevel['fxfhc'] == 1)
                                                    {
                                                       $erdai = $yidai*$memberlevel['tjmoney_two']*0.01;
                                                        if($v['fxfhc'] >= $erdai){
                                                            $v['fxfhc'] = $v['fxfhc'] -$erdai;
                                                            \app\common\Member::addcommission($aid,$erdaiid,$v['id'],$erdai,$day.'二代得到推荐奖励',$erdai);
                                                            \app\common\Member::addfxfhc($aid,$erdaiid,'-'.$erdai,'二代得到推荐奖励,扣除'.$erdai);
                                                        } 
                                                    }else{
                                                       \app\common\Member::addcommission($aid,$erdaiid,$v['id'],$erdai,$day.'二代得到推荐奖励',$erdai); 
                                                    }
                                                    
                                                }
                                           }
                                        }
                                    }else{
                                        $dedao = $xiajiyejicount - $v['jiangcount'];
                                        if($v['fxfhc'] >= $dedao){
                                             $v['fxfhc'] = $v['fxfhc'] -$dedao;
                                            //奖励余额
                                            \app\common\Member::addhei($aid,$v['id'],$dedao,'推荐奖励金额累计'.$xiajiyejicount);
                                            //更改拿到的钱数
                                            Db::name('member')->where('aid',$aid)->where('id',$v['id'])->update(['jiangcount'=>$xiajiyejicount]);
                                            app\common\Member::addfxfhc($aid,$v['id'],'-'.$dedao,'推荐奖励金额奖励,扣除'.$dedao);
                                        }
                                        
                                    }
                                }
                            }
                        }
			            
                    }
                }
                $starttimeArr = Db::name('xsyjfh_send')->where('aid',$aid)->order('id desc')->find();
                if(empty($starttimeArr))
                {
                    $starttime = 0;
                }else{
                    $starttime = $starttimeArr['endtime'];
                }
                
                //发放记录
                $insertdata = [
                    'aid'=>$aid,
                    'starttime'=>$starttime,
                    'endtime'=>time(),
                    'jiangjinchi'=>$jiangjinchi,
                    'zongdianshu'=>$zongdianshu,
                    'renshu'=>$count,
                    'zhixingtime'=>time(),
                    'idstr'=>$idstr,
                    ];
                 Db::name('xsyjfh_send')->insert($insertdata);
                die('已完成');
            }else{
                die('没有找到可以分配的,请查看!');
            }
        }else{
	         die('未开启或未设置,请查看!');
	    }
	}
	
	/***
	 * 月薪奖励
	 * */
	 public function sendyuexin()
	 {
	   $aid = input('param.aid');
	    if(!$aid)
	    {
	         die('请传入aid');
	    } 
	    $starttime = strtotime(date('Y-m-01 00:00:00'));
	    $endtime = strtotime(date('Y-m-31 23:59:59'));
	    $day = date('Y-m');
	    $memberlevel = Db::name('member_level')->where('aid',$aid)->field('id')->where('isyuexin',1)->select()->toArray();
	    $memberlevelids = array_column($memberlevel,'id');
	    $memberAll = Db::name('member')->where('aid',$aid)->field('id,levelid,nickname')->whereIn('levelid',$memberlevelids)->select()->toArray();
	    foreach ($memberAll as $kmember=>$vmember)
	    {
	       // var_dump('判断人'.$vmember['nickname']);
	        $curlevel = Db::name('member_level')->where('aid',$aid)->where('id',$vmember['levelid'])->find();
	       // var_dump('判断等级'.$curlevel['name']);
	        $levestr =  $curlevel['yexin_leveid_w'];
	        $bfb = $curlevel['yexin_bfb_w'];
	        if($curlevel['isyuexin'] == 1)
	        {
	            //1.判断是否完成月任务
	            //1.1自购
	            $zigouprice = Db::name('shop_order')->where('aid',$aid)->where('mid',$vmember['id'])->where('status','in','1,2,3')
	           ->where('paytime','>=',$starttime)->where('paytime','<=',$endtime)->sum('totalprice');
	            if($zigouprice >= $curlevel['yuexin_zigou'])
	            {
	                
	               // var_dump('满足自购');
	                $levestr = $curlevel['yexin_leveid_f'];
	                $bfb = $curlevel['yexin_bfb_f'];
	            }
	            //1.2直推下级业绩->where('paytime','>= ',$starttime)->where('paytime','<=',$endtime)
	            $zhituimember = Db::name('member')->where('aid',$aid)->field('id,levelid,nickname')->where('pid',$vmember['id'])->select()->toArray();
	            $zhituimember = array_column($zhituimember,'id');
	            $zhituiprice = Db::name('shop_order')->where('aid',$aid)->whereIn('mid',$zhituimember)->where('status','in','1,2,3')
	            ->where('paytime','>=',$starttime)->where('paytime','<=',$endtime)->sum('totalprice');
	            if($zhituiprice >= $curlevel['yuexin_zhitui'])
	            {
	               // var_dump('满足直推');
	                $levestr = $curlevel['yexin_leveid_f'];
	                $bfb = $curlevel['yexin_bfb_f'];
	            }
	            $jiangmid = 0;
	            //1.3找到下级
	            $myxiaji = \app\common\Member::getdownmids($aid,$vmember['id']);
	            foreach($myxiaji as $kxiamember=>$vxiamember)
	            {
	                $xiajimember = Db::name('member')->where('aid',$aid)->where('id',$vxiamember)->find();
	                  if(strpos($levestr,(string)$xiajimember['levelid']) !== false)
	                  {
	                     $jiangmid =  $vxiamember;
	                     break;
	                  }
	            }
	           // var_dump('下级用户'.$jiangmid);
	            if($jiangmid >0)
	            {
	                //1.4找到下级的直推奖
	                $zhituti = 0 + Db::name('member_commissionlog')->where('aid',$aid)->where('mid',$jiangmid)->where('remark','like','%下级用户:%')
	                 ->where('createtime','>=',$starttime)->where('createtime','<=',$endtime)->sum('commission');
	                 $zhituti = 100;
	               // var_dump('下级拿到的直推奖'.$zhituti);
	                //1.5奖励
	                if($zhituti >0)
	                {
	                    $jiangli = $zhituti*$bfb*0.01;
	                    \app\common\Member::addcommission($aid,$vmember['id'],$jiangmid,$jiangli,$day.'月薪奖励');
	                }
	            }
	        }
	    }
	 }
	 
	 	// 动态积分定制消贡献值释放计划任务7.5
public function sendgongxianzhi()
{
    $aid = input('param.aid');
    if(!$aid)
    {
        die('请传入aid');
    }
    $day = date('Y-m-d');
    $jifen2info = Db::name('jifechi')->where('aid',$aid)->find();
    if($jifen2info){
        if($jifen2info['jfc_status'] > 0)
        { 
            $memberAll = Db::name('member')->field('id,contribution_num')->where('aid',$aid)->select()->toArray();
            foreach($memberAll as $k=>$v)
            {
                if($v['contribution_num'] > 0)
                {
                    $shifnagmoney = round($v['contribution_num'] * 7.5, 2);
                    $money = 0;
                    $hei = 0;
                    if($jifen2info['jingtaiA_money'] > 0)
                    {
                        $money = round($shifnagmoney * $jifen2info['jingtaiA_money'] * 0.01, 2);
                    }
                    if($jifen2info['jingtaiA_hei'] > 0)
                    {
                        $hei =  round($shifnagmoney * $jifen2info['jingtaiA_hei'] * 0.01, 2);
                    }
                    \app\common\Member::addscoreB($aid, $v['id'], '-'.$shifnagmoney, $day.' 消费释放减去积分: '.$shifnagmoney);
                     \app\common\Member::addshengjiscore($aid, $v['id'], '-'.$shifnagmoney, $day.' 消费释放减去升级积分: '.$shifnagmoney);
                    if($money > 0 ){
                        \app\common\Member::addmoney($aid, $v['id'], $money, $day.' 消费释放加入金额 '.$money);
                    }
                    if($hei > 0 ){
                        \app\common\Member::addhei($aid, $v['id'], $hei, $day.' 消费释放加入 '.$hei);
                    }
                }
            }
            die('已完成,请核对');
        }else{
            die('请配置');
        }
    }else{
        die('请配置');
    }
}

//加速卡每日返还
public function senddongtaiA()
{
    $aid = input('param.aid');
    if(!$aid) {
        die('请传入aid');
    }
    
    $day = date('Y-m-d');
    $now = time();
    
    // 获取积分池配置
    $jifen2info = Db::name('jifechi')->where('aid', $aid)->find();
    if(!$jifen2info) {
        die('请配置积分池参数');
    }
    
    // 获取所有有效的加速卡
    $cards = Db::name('acceleration_card')
        ->where('aid', $aid)
        ->where('status', 1)
        ->where('start_time', '<=', $now)
        ->where('end_time', '>', $now)
        ->select()
        ->toArray();
        
    if(empty($cards)) {
        die('没有有效的加速卡');
    }
    
    foreach($cards as $card) {
        // 获取用户当前静态A积分
        $member = Db::name('member')
            ->where('aid', $aid)
            ->where('id', $card['mid'])
            ->field('id,scoreA')
            ->find();
            
        if($member && $member['scoreA'] > 0) {
            // 从静态A中扣除的加速积分
            $speed_amount = $card['speed_ratio'];  // 直接使用加速卡设定的每日返还值
            
            if($speed_amount > 0 && $speed_amount <= $member['scoreA']) {
                // 扣除静态A积分
                \app\common\Member::addscoreA(
                    $aid, 
                    $card['mid'], 
                    '-'.$speed_amount, 
                    $day.'加速卡释放减去积分:'.$speed_amount
                );
                
                // 计算各种返还
                $money = 0;
                $hei = 0;
                $huang = 0;
                
                // 计算余额返还
                if($jifen2info['jingtaiA_money'] > 0) {
                    $money = round($speed_amount * $jifen2info['jingtaiA_money'] * 0.01, 2);
                    if($money > 0) {
                        \app\common\Member::addmoney($aid, $card['mid'], $money, $day.'加速卡释放加入金额'.$money);
                    }
                }
                
                // 计算黑积分返还
                if($jifen2info['jingtaiA_hei'] > 0) {
                    $hei = round($speed_amount * $jifen2info['jingtaiA_hei'] * 0.01, 2);
                    if($hei > 0) {
                        \app\common\Member::addhei($aid, $card['mid'], $hei, $day.'加速卡释放加入黑积分'.$hei);
                    }
                }
                
                // 计算黄积分返还
                if($jifen2info['jingtaiA_huang'] > 0) {
                    $huang = round($speed_amount * $jifen2info['jingtaiA_huang'] * 0.01, 2);
                    if($huang > 0) {
                        \app\common\Member::addhuang($aid, $card['mid'], $huang, $day.'加速卡释放加入黄积分'.$huang);
                    }
                }
                
                // 记录加速卡使用记录
                Db::name('acceleration_card_log')->insert([
                    'aid' => $aid,
                    'card_id' => $card['id'],
                    'mid' => $card['mid'],
                    'release_amount' => $speed_amount,
                    'money_amount' => $money,
                    'hei_amount' => $hei,
                    'huang_amount' => $huang,
                    'create_time' => $now,
                    'date' => $day
                ]);
            }
        }
    }
    
    die('加速卡收益发放完成,请核对');
}


//静态积分释放
	public function sendA()
	{
	    $aid = input('param.aid');
	    if(!$aid)
	    {
	         die('请传入aid');
	    }
	    $day = date('Y-m-d');
	    $jifen2info = Db::name('jifechi')->where('aid',$aid)->find();
		if($jifen2info){
		   	if($jifen2info['fanbeishu'] >0)
		    { 
		        $memberAll = Db::name('member')->field('id,scoreA')->where('aid',$aid)->select()->toArray();
		        foreach($memberAll as $k=>$v)
		        {
		            if($v['scoreA']>0)
		            {
		                // 判断是百分比还是固定值
		                if ($jifen2info['shifangA_type'] == 'percent') {
		                    $shifnagmoney = round($v['scoreA'] * $jifen2info['shifangA'] / 100, 2);
		                } else {
		                    $shifnagmoney = $jifen2info['shifangA'];
		                }

		                $money = 0;
		                $hei = 0;
		                $huang = 0; // 新增黄积分变量

		                if($jifen2info['jingtaiA_money'] >0)
		                {
		                    $money = round($shifnagmoney * $jifen2info['jingtaiA_money'] * 0.01, 2);
		                }
		                if($jifen2info['jingtaiA_hei'] >0)
		                {
		                    $hei =  round($shifnagmoney * $jifen2info['jingtaiA_hei'] * 0.01, 2);
		                }
		                if($jifen2info['jingtaiA_huang'] >0) // 新增黄积分计算
		                {
		                    $huang =  round($shifnagmoney * $jifen2info['jingtaiA_huang'] * 0.01, 2);
		                }

		                \app\common\Member::addscoreA($aid, $v['id'], '-'.$shifnagmoney, $day.'消费释放减去积分:'.$shifnagmoney);
		                if($money >0 ){
		                    \app\common\Member::addmoney($aid, $v['id'], $money, $day.'消费释放加入金额'.$money);
		                }
		                if($hei >0 ){
		                    \app\common\Member::addhei($aid, $v['id'], $hei, $day.'消费释放加入'.$hei);
		                }
		                if($huang >0 ){ // 新增黄积分处理
		                    \app\common\Member::addhuang($aid, $v['id'], $huang, $day.'消费释放加入黄积分'.$huang);
		                }
		            }
		        }
		        die('已完成,请核对');
		    }else{
		      die('请配置');
		    }
		}else{
		  die('请配置');
		}
	}
	
	/**
	 * 动态积分-全网
	 * */
	 public function sendB2()
	 {
	    $aid = input('param.aid');
	    if(!$aid)
	    {
	         die('请传入aid');
	    }
	    $day = date('Y-m-d');
	    $jifen2info = Db::name('jifechi')->where('aid',$aid)->find();
		if($jifen2info){
		    if($jifen2info['quanwnag'] >0)
		    {
		        $start = strtotime(date('Y-m-d 00:00:00',strtotime('-1 days')));
		        $end = strtotime(date('Y-m-d 23:59:59',strtotime('-1 days')));
		        $shoporderAll = Db::name('shop_order')->where('aid',$aid)->where('status','>=',1)->where('status','<>',4)
		                ->where('paytime','>=',$start)->where('paytime','<=',$end)->sum('totalprice');
		        if($shoporderAll  >0){
    		         $membercount = Db::name('member')->field('id,nickname,scoreB')->where('aid',$aid)->where('scoreB','>',0)->count();
    		         $shifang = round($shoporderAll*$jifen2info['quanwnag']*0.01/$membercount,2);
    		         $money = 0;$hei = 0;
                    
	                 $memberlist = Db::name('member')->field('id,nickname,scoreB')->where('aid',$aid)->where('scoreB','>',0)->select()->toArray();
	                 foreach ($memberlist as $k=>$v)
	                 {
	                     if($v['scoreB'] >= $shifang)
		                 {
		                     $shifang = $shifang;
		                 }else{
		                     $shifang = $v['scoreB'];
		                 }
    		            if($jifen2info['dongtaiB_money'] >0)
    	                 {
    	                     $money = $shifang*$jifen2info['dongtaiB_money']*0.01;
    	                 }
    	                 if($jifen2info['dongtaiB_hei'] >0)
    	                 {
    	                    $hei = $shifang*$jifen2info['dongtaiB_hei']*0.01;
    	                 }
	                     \app\common\Member::addscoreB($aid,$v['id'],'-'.$shifang,$day.'全网昨日消费额释放:'.$shifang);
	                     if($money >0 ){
	                         \app\common\Member::addmoney($aid,$v['id'],$money,$day.'全网昨日消费额释放'.$money);
    	                 }
    	                 if($hei >0 ){
    	                     \app\common\Member::addhei($aid,$v['id'],$hei,$day.'全网昨日消费额释放'.$hei);
    	                 }
	                 }
	                  die('已完成,请核对');
		        }else{
		            die('平台没有消费');
		        }
		    }else{
		      die('请配置');
		    }
		}
	 }
	 
	//分销积分释放
	public function sendB()
	{
	    $aid = input('param.aid');
	    if(!$aid)
	    {
	         die('请传入aid');
	    }
	    $day = date('Y-m-d');
	    $jifen2info = Db::name('jifechi')->where('aid',$aid)->find();
		if($jifen2info){
		   	if($jifen2info['fanbeishu'] >0)
		    { 
		        $starttime = strtotime(date('Y-m-d 00:00:00'));
		        $endtime = strtotime(date('Y-m-d 23:59:59'));
		        $yejidata = json_decode($jifen2info['tuanB'],1);
		        $yejicount = count($yejidata);
		        $memberAll = Db::name('member')->field('id,nickname,scoreB')->where('aid',$aid)->select()->toArray();
		        foreach($memberAll as $k=>$v)
		        {
		            if($v['scoreB']>0)
		            {
		                //全部下级  
		                $dataArr = \app\common\Member::getdownmids($aid,$v['id']);
		                $idstr = implode(',',$dataArr);
		                //团队全部业绩
		                $shoporderAll = Db::name('shop_order')->where('aid',$aid)->whereIn('mid',$idstr)->where('status','>=',1)->where('status','<>',4)
		                ->where('paytime','>=',$starttime)->where('paytime','<=',$endtime)->sum('totalprice');
		                //直接下级
		                $yijiArr =  Db::name('member')->field('id')->where('aid',$aid)->where('pid',$v['id'])->select()->toArray();
		                //直接下级业绩
		                $yijiArr = array_column($yijiArr,'id');
		                $idstr = implode(',',$yijiArr);
		                $shoporderyiji = Db::name('shop_order')->where('aid',$aid)->whereIn('mid',$idstr)->where('status','>=',1)->where('status','<>',4)
		                ->where('paytime','>=',$starttime)->where('paytime','<=',$endtime)->sum('totalprice');
		                if($shoporderAll >=$yejidata[$yejicount]['tiaojian'])
		                {
		                    //5
		                    $shifangzong =  $yejidata[$yejicount]['shifangB'];
		                }elseif($shoporderAll >=$yejidata[$yejicount-1]['tiaojian'])
		                {
		                    //4
		                     $shifangzong = $yejidata[$yejicount-1]['shifangB'];
		                }elseif($shoporderAll >=$yejidata[$yejicount-2]['tiaojian'])
		                {
		                    //3
		                    $shifangzong = $yejidata[$yejicount-2]['shifangB'];
		                }elseif($shoporderAll >=$yejidata[$yejicount-3]['tiaojian'])
		                {
		                    //2
		                    $shifangzong =$yejidata[$yejicount-3]['shifangB'];
		                }elseif($shoporderAll >=$yejidata[$yejicount-4]['tiaojian'])
		                {
		                    //1
		                     $shifangzong = $yejidata[$yejicount-4]['shifangB'];
		                }else{
		                    $shifangzong =0;
		                }
		                foreach($yijiArr as $k1=>$v1)
		                {
		                    $dataArr1 = \app\common\Member::getdownmids($aid,$v1);
    		                $idstr1 = implode(',',$dataArr1);
    		                //下级的团队全部业绩
    		                $shoporderAll1 = Db::name('shop_order')->where('aid',$aid)->whereIn('mid',$idstr1)->where('status','>=',1)->where('status','<>',4)
    		                ->where('paytime','>=',$starttime)->where('paytime','<=',$endtime)->sum('totalprice');
    		                if($shoporderAll1 >=$yejidata[$yejicount]['tiaojian'])
    		                {
    		                    //5
    		                    $shifangcha=  $yejidata[$yejicount]['shifangB'];
    		                }elseif($shoporderAll1 >=$yejidata[$yejicount-1]['tiaojian'])
    		                {
    		                    //4
    		                     $shifangcha = $yejidata[$yejicount-1]['shifangB'];
    		                }elseif($shoporderAll1 >=$yejidata[$yejicount-2]['tiaojian'])
    		                {
    		                    //3
    		                    $shifangcha = $yejidata[$yejicount-2]['shifangB'];
    		                }elseif($shoporderAll1 >=$yejidata[$yejicount-3]['tiaojian'])
    		                {
    		                    //2
    		                    $shifangcha =$yejidata[$yejicount-3]['shifangB'];
    		                }elseif($shoporderAll1 >=$yejidata[$yejicount-4]['tiaojian'])
    		                {
    		                    //1
    		                     $shifangcha = $yejidata[$yejicount-4]['shifangB'];
    		                }else{
    		                    $shifangcha =0;
    		                }
    		                $jianjie = $shoporderAll1*($shifangzong-$shifangcha);
        		         }
        		         $zhijieyeji = $shifangzong*$shoporderyiji;
        		         $shifang = ($zhijieyeji+$jianjie)/100;
		                 $shifang = round($shifang,2);
		                 if($v['scoreB'] >= $shifang)
		                 {
		                    //我的积分b大于等级需要释放的 直接操积分变化
		                    \app\common\Member::addscoreB($aid,$v['id'],'-'.$shifang,$day.'团队业绩达标分销积分释放减去积分:'.$shifang);
		                    $money = 0;$hei = 0;
		                     if($jifen2info['dongtaiB_money'] >0)
    		                {
    		                    $money = round($shifang*$jifen2info['dongtaiB_money']*0.01,2);
    		                }
    		                if($jifen2info['dongtaiB_hei'] >0)
    		                {
    		                    $hei = round($shifang*$jifen2info['dongtaiB_hei']*0.01,2);
    		                }
    		                if($money >0 ){
    		                     \app\common\Member::addmoney($aid,$v['id'],$money,$day.'团队业绩达标分销积分释放加入金额'.$money);
    		                }
    		                if($hei >0 ){
    		                     \app\common\Member::addhei($aid,$v['id'],$hei,$day.'团队业绩达标分销积分释放加入'.$hei);
    		                }
		                
		                 }else{
		                    //我的积分b小于需要释放的 则直接减去积分b
		                    \app\common\Member::addscoreB($aid,$v['id'],'-'.$v['scoreB'],$day.'团队业绩达标分销积分释放减去积分:'.$v['scoreB']);
		                     $money = 0;$hei = 0;
		                     if($jifen2info['dongtaiB_money'] >0)
    		                {
    		                    $money = round($v['scoreB']*$jifen2info['dongtaiB_money']*0.01,2);
    		                }
    		                if($jifen2info['dongtaiB_hei'] >0)
    		                {
    		                    $hei = round($v['scoreB']*$jifen2info['dongtaiB_hei']*0.01,2);
    		                }
    		                if($money >0 ){
    		                    \app\common\Member::addmoney($aid,$v['id'],$money,$day.'团队业绩达标分销积分释放加入金额'.$money);
    		                }
    		                if($hei >0 ){
    		                     \app\common\Member::addhei($aid,$v['id'],$hei,$day.'团队业绩达标分销积分释放加入'.$hei);
    		                }
		                }
		            }
		        }
		        die('已完成,请核对');
		    }else{
		      die('请配置');
		    }
		}else{
		  die('请配置');
		}
	}
	
	//商品分红定时
	public function sendC()
	{
	    $aid = input('param.aid');
	    if(!$aid)
	    {
	         die('请传入aid');
	    }
	    $set = Db::name('admin_set')->where('aid',$aid)->find();
	    $day = date('Y-m-d');
	   	$where = [['order.aid','=',$aid]];
	   	$where[] = ['good.fenhongstatus','=',0];
	    if($set['fxjiesuantime'] == 1)
	    {
	        //1付款后结算
	        $where[] = ['order.status','>=',1];
	        $where[] = ['order.status','<>',4];
	    }else{
	        //0确认收货结算
	        $where[] = ['status','=',3];
	    }
	    //获取分红的订单
	    $ordercount = Db::name('shop_order')->alias('order')->where($where)->join('shop_order_goods good','order.id=good.orderid')->field('order.id,good.id as ordergoodsid,good.fenhongbili,good.totalprice')->select()->toArray();
	    $countstr = 0;
	    //计算分红的金额
	    foreach($ordercount as $v)
	    {
	        $countstr = $countstr+($v['totalprice']*$v['fenhongbili']/100);
	    }
	    //查找分红的等级
	    $levelArr =  Db::name('member_level')->field('id,name')->where('aid',$aid)->where('profenhong',1)->select()->toArray();
	    $levelidArr = array_column($levelArr,'id');
	    $levelstr = implode(',',$levelidArr);
	     //查找人
	    $memberAll = Db::name('member')->field('id,nickname')->where('aid',$aid)->where('levelid','in',$levelstr)->select()->toArray();
	    //平分
	    if(count($memberAll) >0)
	    {
	        $danmember = $countstr/count($memberAll);
	        foreach ($memberAll as $v)
	        {
	            //打钱
	           // \app\common\Member::addmoney($aid,$v['id'],$danmember,$day.'商品分红'.$danmember);
	           \app\common\Member::addcommission($aid,$v['id'],0,$danmember,$day.'商品分红'.$danmember);
	        }
	        foreach($ordercount as $v)
    	    {
    	        Db::name('shop_order_goods')->where('id',$v['ordergoodsid'])->update(['fenhongstatus'=>1]);
    	    }
    	    die('完成');
	    }else{
	         die('未找到可以平分的人');
	    }
	}
	
	
	//见点奖励
	public function sendD()
	{
	   $aid = input('param.aid');
	    if(!$aid)
	    {
	         die('请传入aid');
	    } 
	    $set = Db::name('admin_set')->where('aid',$aid)->find();
	    $day = date('Y-m-d');
	    $where = [['order.aid','=',$aid]];
	   	$where[] = ['good.jiandianstatus','=',0];
	   	$starttime = strtotime(date('Y-m-d 00:00:00'));
	   	$endtime = strtotime(date('Y-m-d 23:59:59'));
	    if($set['fxjiesuantime'] == 1)
	    {
	        //1付款后结算
	        $where[] = ['order.status','>=',1];
	        $where[] = ['order.status','<>',4];
	        $where[] = ['order.paytime','>=',$starttime];
	        $where[] = ['order.paytime','<=',$endtime];
	    }else{
	        //0确认收货结算
	        $where[] = ['status','=',3];
	        $where[] = ['order.collect_time','>=',$starttime];
	        $where[] = ['order.collect_time','<=',$endtime];
	    }
	    $memberAll = Db::name('member')->field('id,nickname,levelid')->where('aid',$aid)->select()->toArray();
	    foreach($memberAll as $key=>$value)
	    {
	        $jiandian = 0;
	        $res = Db::name('member')->field('id,nickname,levelid,createtime')->where('aid',$aid)->where('pid',$value['id'])->order('createtime','asc')->limit(2,9999999999)->select()->toArray();
	        if(!empty($res))
	        {
	            $selectArr = [];
	            foreach($res as $key1=>$value1){
	                 $value1membercount = Db::name('member')->field('id,nickname,levelid,createtime')->where('aid',$aid)->where('pid',$value1['id'])->order('createtime','asc')->count();
	                 if($value1membercount > 0)
	                 {
	                      $value1member = Db::name('member')->field('id,nickname,levelid,createtime')->where('aid',$aid)->where('pid',$value1['id'])->order('createtime','asc')->limit(2)->select()->toArray();
	                      foreach($value1member as $k=>$v)
	                      {
	                           $selectArr[] = $v['id'];
	                           $myxiaji = \app\common\Member::getdownmids($aid,$v['id']);
	                           $selectArr = array_merge($selectArr,$myxiaji);
	                      }
	                 }
                 }
                 $myidsstr = implode(',',$selectArr);
      	         $ordercount = Db::name('shop_order')->alias('order')->where($where)->where('order.mid','in',$myidsstr)->join('shop_order_goods good','order.id=good.orderid')->field('order.mid,order.id,good.id as ordergoodsid,good.jiandianjiangli,good.totalprice')->select()->toArray();
      	         $zhijiang = 0;
      	         foreach ($ordercount as $k1=>$v1)
     	         {
     	             $orderidArr[] = $v1['ordergoodsid'];
     	             if($v1['jiandianjiangli'] && $v1['jiandianjiangli'] != '0.00')
     	             {
     	                 $jiandianjiangli = json_decode($v1['jiandianjiangli'],1);
     	                 $zhijiang1 = $jiandianjiangli[$value['levelid']] * $v1['totalprice'];
     	                 $zhijiang = $zhijiang+$zhijiang1;
     	             }
     	         }
     	          $zhijiang  = $zhijiang/100;
     	          \app\common\Member::addcommission($aid,$value['id'],0,$zhijiang,$day.'见点奖励'.$zhijiang);
	        }
	       
	    }
	    $ordercount = Db::name('shop_order')->alias('order')->where($where)->join('shop_order_goods good','order.id=good.orderid')->field('order.mid,order.id,good.id as ordergoodsid,good.jiandianjiangli,good.totalprice')->select()->toArray();
	    foreach($ordercount as $v)
	    {
	        Db::name('shop_order_goods')->where('id',$v['ordergoodsid'])->update(['jiandianstatus'=>1]); 
	    }
    	die('完成');
	}
	
	//消费值释放
	public function sendW()
	{
	     $aid = input('param.aid');
	    if(!$aid)
	    {
	         die('请传入aid');
	    } 
	    $set = Db::name('admin_set')->where('aid',$aid)->find();
	    if($set['xiaofeizhijiazhi'])
	    {
    	    $day = date('Y-m-d');
    	    $memberAll = Db::name('member')->field('id,scorexiaofeizhi')->where('aid',$aid)->select()->toArray();
    	    $scorexiaofeizhicount = 0;
            foreach($memberAll as $k=>$v)
            {
                $scorexiaofeizhicount = $scorexiaofeizhicount+$v['scorexiaofeizhi'];
            }
            foreach($memberAll as $k=>$v)
            {
                if($v['scorexiaofeizhi'] >0 ){
                    $fanlvjifen = $v['scorexiaofeizhi']/$scorexiaofeizhicount*$set['xiaofeizhijiazhi'];
                    $fanlvjifen = round($fanlvjifen,2);
                    //打绿积分
                     \app\common\Member::addscorelv($aid,$v['id'],$fanlvjifen,$day.'消费值获取绿积分'.$fanlvjifen);
                }
            }
            die('完成');
	    }else{
	         die('请先设置消费值价值');
	    }
	}
	
	//积分价值定时更新
	public function sendZ()
	{
	    $aid = input('param.aid');
	    if(!$aid)
	    {
	         die('请传入aid');
	    } 
	    $set = Db::name('admin_set')->where('aid',$aid)->find();
	   // 前一日（单日）增加的总消费值×10%  ÷ 前一日（单日）销毁的绿色积分数量 ＝ 当日绿色积分价格。
	   $starttime = strtotime(date('Y-m-d 00:00:00').' -1 days');
	   $endtime = strtotime(date('Y-m-d  23:59:59',$starttime));
	   //$
	   $xiaofeizhicount = Db::name('member_scorelogxiaofeizhi')->where('aid',$aid)->where('createtime','>=',$starttime)->where('createtime','<=',$endtime)->sum('score');
	   $xiaohuilv = Db::name('zhuanxiaofeizhi_log')->where('aid',$aid)->where('createtime','>=',$starttime)->where('createtime','<=',$endtime)->where('money','like','%-%')->sum('money');
	   $xiaohuilv = abs($xiaohuilv);
	   $jiazhi = $xiaofeizhicount*$set['xiaofeizhizhuan']*0.01/$xiaohuilv;
	   $data = ['lvjifenjiazhi'=>$jiazhi];
	   $set = Db::name('admin_set')->where('aid',$aid)->update($data);
	   die('操作成功');
	}
	
	//创业基金
	public function sendZZ()
	{
	    $aid = input('param.aid');
	    if(!$aid)
	    {
	         die('请传入aid');
	    } 
	    $set = Db::name('admin_set')->where('aid',$aid)->find();
	    $day = date('Y-m-d');
	    $memberAll = Db::name('member')->where('aid',$aid)->select()->toArray();
	    foreach ($memberAll as $k=>$v)
	    {
	        //团队业绩每累积到 200箱 且不包含等级id 1 的业绩,达到就奖励 佣金,往上找第一个等级id 0奖励0.00元
	    } 
	    
	}
	
	/**
	 * 静态积分释放2
	 * */
	public function sendZZZ()
	{
	    $aid = input('param.aid');
	    if(!$aid)
	    {
	         die('请传入aid');
	    } 
	    $where = [];
		$where[] = ['aid','=',$aid];
	    $ress = Db::name('member_commission_record')->where($where)->where('jl_type',3)->where('status',0)->select()->toArray();
	    foreach ($ress as $v)
	    {
	        \app\common\Member::addcommission($aid,$v['mid'],$v['frommid'],$v['commission'],'购买产品静态分佣佣金');
	        Db::name('member_commission_record')->where('aid',$aid)->where('id',$v['id'])->update(['status'=>1,'endtime'=>time()]);
	        
	    }
	    die('已完成');
	}
	
	/**
	 * 佣金转换
	 * */
	public function sendZhuan()
	{
	    $aid = input('param.aid');
	    if(!$aid)
	    {
	         die('请传入aid');
	    } 
	    $day = date('Y-m-d');
	    $rs = Db::name('admin_set')->where('aid',$aid)->find();
	    
	    // 检查是否设置了新的佣金转换比例，如果没有则使用旧的比例设置
	    $useNewRatios = isset($rs['yongzhuan_jifen']) && isset($rs['yongzhuan_yue']) && isset($rs['yongzhuan_xianjinquan']);
	    
	    if($useNewRatios || $rs['yongzhuan'] > 0)
	    {
	        $where = [];
    		$where[] = ['aid','=',$aid];
    		$memberAll = Db::name('member')->where($where)->field('id,commission')->select()->toArray();
    		
    		// 检查积分池是否开启
    		$jifenchiStatus = Db::name('jifechi')->where('aid',$aid)->value('jfc_status');
    		
    		foreach($memberAll as $v)
    		{
    		    // 检查积分池是否开启且佣金转换积分池模式是否开启
    		    if ($jifenchiStatus == 1 && isset($rs['jifenchi_mode']) && $rs['jifenchi_mode'] == 1) {
    		        $this->processWithJifenchiMode($aid, $v, $rs, $day, $useNewRatios);
    		    } else {
    		        $this->processWithoutJifenchiMode($aid, $v, $rs, $day, $useNewRatios);
    		    }
    		}
    		die('已完成');
	    }else{
	        die('请先去配置转换比例');
	    }
	}
	
	/**
     * 使用积分池模式处理佣金转换
     * @param int $aid 商户ID
     * @param array $member 会员信息
     * @param array $setting 系统设置
     * @param string $day 当前日期
     * @param bool $useNewRatios 是否使用新的比例设置
     */
    private function processWithJifenchiMode($aid, $member, $setting, $day, $useNewRatios = false) 
    {
        if ($useNewRatios) {
            // 使用新的比例设置
            $jifenRatio = floatval($setting['yongzhuan_jifen']);
            $yueRatio = floatval($setting['yongzhuan_yue']);
            $xianjinquanRatio = floatval($setting['yongzhuan_xianjinquan']);
            
            // 确保总和为1
            $total = $jifenRatio + $yueRatio + $xianjinquanRatio;
            if ($total > 0) {
                $jifenRatio = $jifenRatio / $total;
                $yueRatio = $yueRatio / $total;
                $xianjinquanRatio = $xianjinquanRatio / $total;
            } else {
                // 默认平均分配
                $jifenRatio = 0.33;
                $yueRatio = 0.33;
                $xianjinquanRatio = 0.34;
            }
            
            // 计算三种类型的金额
            $jifen = round($member['commission'] * $jifenRatio, 2);
            $yumoney = round($member['commission'] * $yueRatio, 2);
            $xianjinquan = round($member['commission'] * $xianjinquanRatio, 2);
            
            // 处理为0的情况，防止出现无意义的加零操作
            if ($jifen > 0) {
                // 积分池相关设置
                $jifenchi_jingtai = isset($setting['jifenchi_jingtai']) ? floatval($setting['jifenchi_jingtai']) : 0;
                $jifenchi_dongtai = isset($setting['jifenchi_dongtai']) ? floatval($setting['jifenchi_dongtai']) : 0;
                
                // 确保比例不超过100%
                $total_percent = $jifenchi_jingtai + $jifenchi_dongtai;
                if ($total_percent > 100) {
                    $ratio = 100 / $total_percent;
                    $jifenchi_jingtai *= $ratio;
                    $jifenchi_dongtai *= $ratio;
                }
                
                // 计算需要扣除的静态池和动态池积分
                $jingtai_amount = round($jifen * ($jifenchi_jingtai / 100), 2);
                $dongtai_amount = round($jifen * ($jifenchi_dongtai / 100), 2);
                
                // 转积分
                \app\common\Member::addscore($aid, $member['id'], $jifen, $day.'日常佣金转换成积分');
                
                // 扣除佣金
                \app\common\Member::addcommission($aid, $member['id'], $member['id'], '-'.$jifen, $day.'日常佣金转换积分减佣金');
                
                // 使用addscoreA和addscoreB方法直接操作积分池
                if ($jingtai_amount > 0) {
                    // 增加静态积分池
                    \app\common\Member::addscoreA($aid, $member['id'], -$jingtai_amount, $day.'佣金转换扣除静态积分池');
                }
                
                if ($dongtai_amount > 0) {
                    // 增加动态积分池
                    \app\common\Member::addscoreB($aid, $member['id'], -$dongtai_amount, $day.'佣金转换扣除动态积分池');
                }
            }
            
            // 转余额
            if ($yumoney > 0) {
                \app\common\Member::addmoney($aid, $member['id'], $yumoney, $day.'日常佣金转换加余额' . $yumoney);
                \app\common\Member::addcommission($aid, $member['id'], $member['id'], '-'.$yumoney, $day.'日常佣金转换余额减佣金');
            }
            
            // 转现金券
            if ($xianjinquan > 0) {
                \app\common\Member::addhei($aid, $member['id'], $xianjinquan, $day.'日常佣金转换成现金券');
                \app\common\Member::addcommission($aid, $member['id'], $member['id'], '-'.$xianjinquan, $day.'日常佣金转换现金券减佣金');
            }
        } else {
            // 使用旧的比例设置
            // 1. 计算余额
            $yumoney = round($member['commission'] * (1 - $setting['yongzhuan']), 2);
            
            // 2. 计算积分
            $jifen = round($member['commission'] * $setting['yongzhuan'], 2);
            
            // 3. 获取积分池配置
            $jifenchi_jingtai = isset($setting['jifenchi_jingtai']) ? floatval($setting['jifenchi_jingtai']) : 0;
            $jifenchi_dongtai = isset($setting['jifenchi_dongtai']) ? floatval($setting['jifenchi_dongtai']) : 0;
            
            // 确保比例不超过100%
            $total_percent = $jifenchi_jingtai + $jifenchi_dongtai;
            if ($total_percent > 100) {
                $ratio = 100 / $total_percent;
                $jifenchi_jingtai *= $ratio;
                $jifenchi_dongtai *= $ratio;
            }
            
            // 4. 计算需要扣除的静态池和动态池积分
            $jingtai_amount = round($jifen * ($jifenchi_jingtai / 100), 2);
            $dongtai_amount = round($jifen * ($jifenchi_dongtai / 100), 2);
            
            // 5. 转余额
            \app\common\Member::addmoney($aid, $member['id'], $yumoney, $day.'日常佣金转换加余额' . $yumoney);
            
            // 6. 转积分
            \app\common\Member::addscore($aid, $member['id'], $jifen, $day.'日常佣金转换成积分');
            
            // 7. 扣除佣金
            \app\common\Member::addcommission($aid, $member['id'], $member['id'], '-'.$yumoney, $day.'日常佣金转换余额减佣金');
            \app\common\Member::addcommission($aid, $member['id'], $member['id'], '-'.$jifen, $day.'日常佣金转换积分减佣金');
            
            // 8. 使用addscoreA和addscoreB方法直接操作积分池
            if ($jingtai_amount > 0) {
                // 增加静态积分池
                \app\common\Member::addscoreA($aid, $member['id'], -$jingtai_amount, $day.'佣金转换扣除静态积分池');
            }
            
            if ($dongtai_amount > 0) {
                // 增加动态积分池
                \app\common\Member::addscoreB($aid, $member['id'], -$dongtai_amount, $day.'佣金转换扣除动态积分池');
            }
        }
    }
    
    /**
     * 不使用积分池模式处理佣金转换
     * @param int $aid 商户ID
     * @param array $member 会员信息
     * @param array $setting 系统设置
     * @param string $day 当前日期
     * @param bool $useNewRatios 是否使用新的比例设置
     */
    private function processWithoutJifenchiMode($aid, $member, $setting, $day, $useNewRatios = false) 
    {
        if ($useNewRatios) {
            // 使用新的比例设置
            $jifenRatio = floatval($setting['yongzhuan_jifen']);
            $yueRatio = floatval($setting['yongzhuan_yue']);
            $xianjinquanRatio = floatval($setting['yongzhuan_xianjinquan']);
            
            // 确保总和为1
            $total = $jifenRatio + $yueRatio + $xianjinquanRatio;
            if ($total > 0) {
                $jifenRatio = $jifenRatio / $total;
                $yueRatio = $yueRatio / $total;
                $xianjinquanRatio = $xianjinquanRatio / $total;
            } else {
                // 默认平均分配
                $jifenRatio = 0.33;
                $yueRatio = 0.33;
                $xianjinquanRatio = 0.34;
            }
            
            // 计算三种类型的金额
            $jifen = round($member['commission'] * $jifenRatio, 2);
            $yumoney = round($member['commission'] * $yueRatio, 2);
            $xianjinquan = round($member['commission'] * $xianjinquanRatio, 2);
            
            // 处理为0的情况，防止出现无意义的加零操作
            if ($jifen > 0) {
                // 转积分
                \app\common\Member::addscore($aid, $member['id'], $jifen, $day.'日常佣金转换成积分');
                // 扣除佣金
                \app\common\Member::addcommission($aid, $member['id'], $member['id'], '-'.$jifen, $day.'日常佣金转换积分减佣金');
            }
            
            // 转余额
            if ($yumoney > 0) {
                \app\common\Member::addmoney($aid, $member['id'], $yumoney, $day.'日常佣金转换加余额' . $yumoney);
                \app\common\Member::addcommission($aid, $member['id'], $member['id'], '-'.$yumoney, $day.'日常佣金转换余额减佣金');
            }
            
            // 转现金券
            if ($xianjinquan > 0) {
                \app\common\Member::addhei($aid, $member['id'], $xianjinquan, $day.'日常佣金转换成现金券');
                \app\common\Member::addcommission($aid, $member['id'], $member['id'], '-'.$xianjinquan, $day.'日常佣金转换现金券减佣金');
            }
        } else {
            // 使用旧的比例设置
            // 检查旧设置是否存在，如果没有默认值为0.5（一半积分一半余额）
            $yongzhuan = isset($setting['yongzhuan']) ? floatval($setting['yongzhuan']) : 0.5;
            
            // 分配比例 - 默认将佣金的yongzhuan比例转入积分，剩余部分的一半转入余额，一半转入现金券
            // 1. 计算积分
            $jifen = round($member['commission'] * $yongzhuan, 2);
            
            // 2. 计算余额和现金券（剩余部分平均分配）
            $remaining = $member['commission'] - $jifen;
            $yumoney = round($remaining * 0.5, 2);
            $xianjinquan = round($remaining - $yumoney, 2);
            
            // 处理为0的情况，防止出现无意义的加零操作
            // 3. 转积分
            if ($jifen > 0) {
                \app\common\Member::addscore($aid, $member['id'], $jifen, $day.'日常佣金转换成积分');
                \app\common\Member::addcommission($aid, $member['id'], $member['id'], '-'.$jifen, $day.'日常佣金转换积分减佣金');
            }
            
            // 4. 转余额
            if ($yumoney > 0) {
                \app\common\Member::addmoney($aid, $member['id'], $yumoney, $day.'日常佣金转换加余额' . $yumoney);
                \app\common\Member::addcommission($aid, $member['id'], $member['id'], '-'.$yumoney, $day.'日常佣金转换余额减佣金');
            }
            
            // 5. 转现金券
            if ($xianjinquan > 0) {
                \app\common\Member::addhei($aid, $member['id'], $xianjinquan, $day.'日常佣金转换成现金券');
                \app\common\Member::addcommission($aid, $member['id'], $member['id'], '-'.$xianjinquan, $day.'日常佣金转换现金券减佣金');
            }
        }
    }
	
	/**
	 * 定时提醒云库存
	 * */
	 public function tixingyunkunyun()
	 {
	   $aid = input('param.aid');
	    if(!$aid)
	    {
	         die('请传入aid');
	    } 
	    $ykcrs = Db::name('yunkucun_sysset')->where('aid',$aid)->find();
	    if(!empty($ykcrs))
		{
		    if($ykcrs['status'] == 1 && $ykcrs['buzu']>0)
		    {
		         $userdata = Db::name('yunkucun_users')->where('aid',$aid)->where('num','<',$ykcrs['buzu'])->select()->toArray();
		         foreach($userdata as $v)
		         {
		                $tmplcontent = [];
                        $tmplcontent['thing1'] = '库存数量不足提醒';
                        $tmplcontent['thing4'] = '您的商品:'.$v['proname'].',产品规格:'.$v['ggname'].',库存数量不足';
                        $tmplcontent['thing8'] = '点击进行查看'; //会员等级
                        $tmplcontent['time16'] = date('Y-m-d H:i:s');
                        $rs = \app\common\Wechat::sendtmpl($aid,$v['mid'],'tmpl_kucunbuzu',$tmplcontent,m_url('shopPackage/shop/product?id='.$v['proid'], $aid));

		         }
		    }
		}
	 }
	
	public function index(){
		$config = include(ROOT_PATH.'config.php');
		set_time_limit(0);
		ini_set('memory_limit', -1);
		if(input('param.key')!=$config['authtoken']) die('error');
		$this->perminute();
		//执行了多少次了
		$lastautotimes = cache('autotimes');
		if(!$lastautotimes) $lastautotimes = 0;
		cache('autotimes',$lastautotimes+1);

		//Log::write('perminute');
		$lastautohour = cache('autotimehour');
		if(!$lastautohour){
			$lastautohour = strtotime(date("Ymd H:00:00")); //整点执行
			cache('autotimehour',$lastautohour);
		}
		if($lastautohour <= time() - 3600){
			cache('autotimehour',time());
			$this->perhour();
			//Log::write('perhour');
		}
		$lastautoday = cache('autotimeday');
		if(!$lastautoday){
			$lastautoday = strtotime(date("Ymd 06:00:00")); //6点执行
			cache('autotimeday',$lastautoday);
		}
		if($lastautoday <= time() - 86400){
			cache('autotimeday',time());
			$this->perday();
			\think\facade\Log::write('perday');
		}

		if(getcustom('plug_yuebao')){

			//定时0点执行
			$time = (int)date("H",time());
			if($time == 0){

				//确保一天执行一次
				$yuebaotime = cache('yuebaotime');

				$n_time = strtotime(date("Y-m-d",time()));

				if(!$yuebaotime){
					cache('yuebaotime',$n_time);
				}else{
					if($yuebaotime == $n_time){
						return;
					}else{
						cache('yuebaotime',$n_time);
					}
				}

				//计算余额宝收益
				$this->yuebao();

			}
		}

		die;
	}
	//每分钟执行一次
	private function perminute(){
		$time = time();
		//60分钟自动关闭订单 释放库存
		$orderlist = Db::name('shop_order')->where('status',0)->where('miaoshaid',0)->select()->toArray();
		$autocloseArr = [];
		foreach($orderlist as $order){
			if(!$autocloseArr[$order['aid']]){
				$autocloseArr[$order['aid']] = Db::name('shop_sysset')->where('aid',$order['aid'])->value('autoclose');
			}
			if($order['createtime'] + $autocloseArr[$order['aid']]*60 > time()) continue;
			$aid = $order['aid'];
			$mid = $order['mid'];
			$orderid = intval($order['id']);
			$order = Db::name('shop_order')->where('id',$orderid)->find();
			if(!$order || $order['status']!=0){
				//return $this->json(['status'=>0,'msg'=>'关闭失败,订单状态错误']);
			}else{
				//加库存
				$oglist = Db::name('shop_order_goods')->where('aid',$aid)->where('orderid',$orderid)->select()->toArray();
				foreach($oglist as $og){
				     if($order['ti'] == 0){
    					Db::name('shop_guige')->where('aid',$aid)->where('id',$og['ggid'])->update(['stock'=>Db::raw("stock+".$og['num']),'sales'=>Db::raw("IF(sales>=".$og['num'].",sales-".$og['num'].",0)")]);
    					Db::name('shop_product')->where('aid',$aid)->where('id',$og['proid'])->update(['stock'=>Db::raw("stock+".$og['num']),'sales'=>Db::raw("IF(sales>=".$og['num'].",sales-".$og['num'].",0)")]);
    					if(getcustom('guige_split')){
    						\app\model\ShopProduct::addlinkstock($og['proid'],$og['ggid'],$og['num']);
    					}
				     }
				     if($order['nahuoid'] == 0){
    					Db::name('shop_guige')->where('aid',$aid)->where('id',$og['ggid'])->update(['stock'=>Db::raw("stock+".$og['num']),'sales'=>Db::raw("IF(sales>=".$og['num'].",sales-".$og['num'].",0)")]);
    					Db::name('shop_product')->where('aid',$aid)->where('id',$og['proid'])->update(['stock'=>Db::raw("stock+".$og['num']),'sales'=>Db::raw("IF(sales>=".$og['num'].",sales-".$og['num'].",0)")]);
    					if(getcustom('guige_split')){
    						\app\model\ShopProduct::addlinkstock($og['proid'],$og['ggid'],$og['num']);
    					}
				     }else{
        		        \app\common\Member::yunkucunlog(aid,$order['nahuoid'],$og['proid'],$og['ggid'],$og['num'],$og,'下级订单关闭订单,返还');
        		        if(getcustom('guige_split')){
    						\app\model\ShopProduct::addlinkstock($og['proid'],$og['ggid'],$og['num']);
    					}
        		    }
					
				}
				//优惠券抵扣的返还
				if($order['coupon_rid']){
					Db::name('coupon_record')->where('aid',$aid)->where('mid',$mid)->where('id','in',$order['coupon_rid'])->update(['status'=>0,'usetime'=>'']);
				}
				$rs = Db::name('shop_order')->where('id',$orderid)->where('aid',$aid)->where('mid',$mid)->update(['status'=>4]);
				Db::name('shop_order_goods')->where('orderid',$orderid)->where('aid',$aid)->where('mid',$mid)->update(['status'=>4]);
				//return $this->json(['status'=>1,'msg'=>'操作成功']);
				//$rs = \app\common\Wxpay::closeorder($order['aid'],$order['ordernum'],$order['platform']);
			}
		}
		//秒杀
		$orderlist = Db::name('seckill_order')->where('status',0)->select()->toArray();
		$autocloseArr = [];
		foreach($orderlist as $order){
			if(!$autocloseArr[$order['aid']]){
				$autocloseArr[$order['aid']] = Db::name('seckill_sysset')->where('aid',$order['aid'])->value('autoclose');
			}
			if($order['createtime'] + $autocloseArr[$order['aid']]*60 > time()) continue;
			$aid = $order['aid'];
			$mid = $order['mid'];
			$orderid = intval($order['id']);
			$order = Db::name('seckill_order')->where('id',$orderid)->find();
			if(!$order || $order['status']!=0){
				//return $this->json(['status'=>0,'msg'=>'关闭失败,订单状态错误']);
			}else{
				//加库存
				Db::name('seckill_product')->where('aid',$aid)->where('id',$order['proid'])->update(['stock'=>Db::raw("stock+".$order['num']),'sales'=>Db::raw("IF(sales>=".$order['num'].",sales-".$order['num'].",0)")]);
				//优惠券抵扣的返还
				if($order['coupon_rid'] > 0){
					Db::name('coupon_record')->where('aid',$aid)->where('mid',$mid)->where('id',$order['coupon_rid'])->update(['status'=>0,'usetime'=>'']);
				}
				$rs = Db::name('seckill_order')->where('id',$orderid)->where('aid',$aid)->where('mid',$mid)->update(['status'=>4]);
			}
		}

		//积分兑换
		$orderlist = Db::name('scoreshop_order')->where('status',0)->select()->toArray();
		$autocloseArr = [];
		foreach($orderlist as $order){
			if(!$autocloseArr[$order['aid']]){
				$autocloseArr[$order['aid']] = Db::name('scoreshop_sysset')->where('aid',$order['aid'])->value('autoclose');
			}
			if($order['createtime'] + $autocloseArr[$order['aid']]*60 > time()) continue;
			$aid = $order['aid'];
			$mid = $order['mid'];
			$orderid = intval($order['id']);
			$order = Db::name('scoreshop_order')->where('id',$orderid)->find();
			if(!$order || $order['status']!=0){
				//return $this->json(['status'=>0,'msg'=>'关闭失败,订单状态错误']);
			}else{
				//加库存
				$oglist = Db::name('scoreshop_order_goods')->where('aid',$aid)->where('orderid',$orderid)->select()->toArray();
				foreach($oglist as $og){
					Db::name('scoreshop_product')->where('aid',$aid)->where('id',$og['proid'])->update(['stock'=>Db::raw("stock+".$og['num']),'sales'=>Db::raw("IF(sales>=".$og['num'].",sales-".$og['num'].",0)")]);
				}
				//优惠券抵扣的返还
				if($order['coupon_rid'] > 0){
					Db::name('coupon_record')->where('aid',$aid)->where('mid',$mid)->where('id',$order['coupon_rid'])->update(['status'=>0,'usetime'=>'']);
				}
				$rs = Db::name('scoreshop_order')->where('id',$orderid)->where('aid',$aid)->where('mid',$mid)->update(['status'=>4]);
				Db::name('scoreshop_order_goods')->where('orderid',$orderid)->where('aid',$aid)->where('mid',$mid)->update(['status'=>4]);
			}
		}


		//预约服务
		$orderlist = Db::name('yuyue_order')->where('status',0)->select()->toArray();
		$autocloseArr = [];
		foreach($orderlist as $order){
			if(!$autocloseArr[$order['aid'].'_'.$order['bid']]){
				$autocloseArr[$order['aid'].'_'.$order['bid']] = Db::name('yuyue_set')->where('aid',$order['aid'])->where('bid',$order['bid'])->value('autoclose');
			}
			if($order['createtime'] + $autocloseArr[$order['aid'].'_'.$order['bid']]*60 > time()) continue;
			$aid = $order['aid'];
			$mid = $order['mid'];
			$orderid = intval($order['id']);

			//加库存
			Db::name('yuyue_product')->where('aid',$aid)->where('id',$order['proid'])->update(['stock'=>Db::raw("stock+".$order['num']),'sales'=>Db::raw("IF(sales>=".$order['num'].",sales-".$order['num'].",0)")]);
			Db::name('yuyue_guige')->where('aid',$aid)->where('id',$order['ggid'])->update(['stock'=>Db::raw("stock+".$order['num']),'sales'=>Db::raw("IF(sales>=".$order['num'].",sales-".$order['num'].",0)")]);
			//优惠券抵扣的返还
			if($order['coupon_rid'] > 0){
				Db::name('coupon_record')->where('aid',$aid)->where('mid',$mid)->where('id',$order['coupon_rid'])->update(['status'=>0,'usetime'=>'']);
			}
			$rs = Db::name('yuyue_order')->where('id',$orderid)->where('aid',$aid)->where('mid',$mid)->update(['status'=>4]);
			//积分抵扣的返还
			if($order['scoredkscore'] > 0){
				\app\common\Member::addscore($aid,$order['mid'],$order['scoredkscore'],'订单退款返还');
			}
			//退款成功通知
			$tmplcontent = [];
			$tmplcontent['first'] = '您的订单已经完成退款，¥'.$order['refund_money'].'已经退回您的付款账户，请留意查收。';
			$tmplcontent['remark'] = '请点击查看详情~';
			$tmplcontent['orderProductPrice'] = (string) $order['refund_money'];
			$tmplcontent['orderProductName'] = $order['title'];
			$tmplcontent['orderName'] = $order['ordernum'];
			\app\common\Wechat::sendtmpl(aid,$order['mid'],'tmpl_tuisuccess',$tmplcontent,m_url('pages/my/usercenter'));
			//订阅消息
			$tmplcontent = [];
			$tmplcontent['amount6'] = $order['refund_money'];
			$tmplcontent['thing3'] = $order['title'];
			$tmplcontent['character_string2'] = $order['ordernum'];
			
			$tmplcontentnew = [];
			$tmplcontentnew['amount3'] = $order['refund_money'];
			$tmplcontentnew['thing6'] = $order['title'];
			$tmplcontentnew['character_string4'] = $order['ordernum'];
			\app\common\Wechat::sendwxtmpl($aid,$order['mid'],'tmpl_tuisuccess',$tmplcontentnew,'pages/my/usercenter',$tmplcontent);

			//短信通知
			$member = Db::name('member')->where('id',$order['mid'])->find();
			if($member['tel']){
				$tel = $member['tel'];
			}else{
				$tel = $order['tel'];
			}
			$rs = \app\common\Sms::send($aid,$tel,'tmpl_tuisuccess',['ordernum'=>$order['ordernum'],'money'=>$order['refund_money']]);
		}

		//预约服务 几分钟内未接单自动退款
		if(getcustom('hmy_yuyue')){
			$orderlist = Db::name('yuyue_order')->where('status',1)->where('worker_orderid',0)->select()->toArray();
			$autocloseArr = [];
			foreach($orderlist as $order){
				if(!$autocloseArr[$order['aid'].'_'.$order['bid']]){
					$autocloseArr[$order['aid'].'_'.$order['bid']] = Db::name('yuyue_set')->where('aid',$order['aid'])->where('bid',$order['bid'])->value('minminute');
				}
				if($order['paytime'] + $autocloseArr[$order['aid'].'_'.$order['bid']]*60 > time()) continue;
				$aid = $order['aid'];
				$mid = $order['mid'];
				$bid = $order['bid'];
				$orderid = intval($order['id']);
				//	$rs = Db::name('yuyue_order')->where('id',$orderid)->where('aid',$aid)->where('mid',$mid)->update(['status'=>4]);
				//退款
				Db::name('yuyue_order')->where('id',$orderid)->where('aid',$aid)->where('bid',$bid)->update(['status'=>4,'refund_status'=>2,'refund_money'=>$order['totalprice'],'refund_reason'=>'超时未接单退款','refund_time'=>time()]);
				$rs = \app\common\Order::refund($order,$order['totalprice'],'超时未接单退款');

				$config = include(ROOT_PATH.'config.php');
				$appId=$config['hmyyuyue']['appId'];
				$appSecret=$config['hmyyuyue']['appSecret'];
				$headrs = array('content-type: application/json;charset=UTF-8','appid:'.$appId,'appSecret:'.$appSecret);
				$url = 'https://shifu.api.kkgj123.cn/api/1/order/cancel';
				$data = [];
				$data['sysOrderNo'] = $order['sysOrderNo'];
				$data['cancelParty'] = 3;
				$data['cancelReason'] = '超时取消';
				$data = json_encode($data,JSON_UNESCAPED_UNICODE);
				$res = curl_post($url,$data,'',$headrs);
				$res = json_decode($res,true);
			}
		}
		//超时的团
		$time = time();
		$tlist = Db::name('collage_order_team')->where("`status`=1 and createtime+teamhour*3600<{$time}")->select()->toArray();
		Db::name('collage_order_team')->where("`status`=1 and createtime+teamhour*3600<{$time}")->update(['status'=>3]);//改成失败状态
		if($tlist){//退款
			foreach($tlist as $t){
				$sysset = Db::name('admin')->where('id',$t['aid'])->find();
				$orderlist = Db::name('collage_order')->where('status',1)->where('teamid',$t['id'])->where('buytype','<>',1)->select()->toArray();
				foreach($orderlist as $orderinfo){
					if($orderinfo['paytype']=='微信支付'){
						$rs = \app\common\Wxpay::refund($orderinfo['aid'],$orderinfo['platform'],$orderinfo['ordernum'],$orderinfo['totalprice'],$orderinfo['totalprice'],'拼团失败');
					}else{
						\app\common\Member::addmoney($orderinfo['aid'],$orderinfo['mid'],$orderinfo['totalprice'],'拼团失败退款');
					}
					//积分抵扣的返还
					if($orderinfo['scoredk'] > 0){
						\app\common\Member::addscore($orderinfo['aid'],$orderinfo['mid'],$orderinfo['scoredk'],'拼团失败退款返还');
					}
					//优惠券抵扣的返还
					if($orderinfo['coupon_rid'] > 0){
						Db::name('coupon_record')->where('aid',aid)->where('mid',$orderinfo['mid'])->where('id',$orderinfo['coupon_rid'])->update(['status'=>0,'usetime'=>'']);
					}
					Db::name('collage_order')->where('id',$orderinfo['id'])->update(['status'=>4]);
					//退款成功通知
					$tmplcontent = [];
					$tmplcontent['first'] = '拼团失败退款，¥'.$orderinfo['totalprice'].'已经退回您的付款账户，请留意查收。';
					$tmplcontent['remark'] = '请点击查看详情~';
					$tmplcontent['orderProductPrice'] = (string) $orderinfo['totalprice'];
					$tmplcontent['orderProductName'] = $orderinfo['title'];
					$tmplcontent['orderName'] = $orderinfo['ordernum'];
					\app\common\Wechat::sendtmpl($orderinfo['aid'],$orderinfo['mid'],'tmpl_tuisuccess',$tmplcontent,m_url('activity/collage/orderlist',$orderinfo['aid']));
					//订阅消息
					$tmplcontent = [];
					$tmplcontent['amount6'] = $orderinfo['totalprice'];
					$tmplcontent['thing3'] = $orderinfo['title'];
					$tmplcontent['character_string2'] = $orderinfo['ordernum'];
					
					$tmplcontentnew = [];
					$tmplcontentnew['amount3'] = $orderinfo['totalprice'];
					$tmplcontentnew['thing6'] = $orderinfo['title'];
					$tmplcontentnew['character_string4'] = $orderinfo['ordernum'];
					\app\common\Wechat::sendwxtmpl($orderinfo['aid'],$orderinfo['mid'],'tmpl_tuisuccess',$tmplcontentnew,'pages/my/usercenter',$tmplcontent);

					//短信通知
					$member = Db::name('member')->where('id',$orderinfo['mid'])->find();
					if($member['tel']){
						$tel = $member['tel'];
					}else{
						$tel = $orderinfo['tel'];
					}
					$rs = \app\common\Sms::send($orderinfo['aid'],$tel,'tmpl_tuisuccess',['ordernum'=>$orderinfo['ordernum'],'money'=>$orderinfo['totalprice']]);
				}
			}
		}


		//超时的幸运拼团
		//超时的团
		$time = time();
		$tlist = Db::name('lucky_collage_order_team')->where("`status`=1 and createtime+teamhour*3600<{$time}")->select()->toArray();
		Db::name('lucky_collage_order_team')->where("`status`=1 and createtime+teamhour*3600<{$time}")->update(['status'=>3]);//改成失败状态
		if($tlist){//退款
			foreach($tlist as $t){
				$sysset = Db::name('admin')->where('id',$t['aid'])->find();
				$orderlist = Db::name('lucky_collage_order')->where('status',1)->where('teamid',$t['id'])->where('buytype','<>',1)->select()->toArray();
				foreach($orderlist as $orderinfo){
					if($orderinfo['paytype']=='微信支付'){
						$rs = \app\common\Wxpay::refund($orderinfo['aid'],$orderinfo['platform'],$orderinfo['ordernum'],$orderinfo['totalprice'],$orderinfo['totalprice'],'拼团失败');
					}else{
						\app\common\Member::addmoney($orderinfo['aid'],$orderinfo['mid'],$orderinfo['totalprice'],'拼团失败退款');
					}
					//积分抵扣的返还
					if($orderinfo['scoredk'] > 0){
						\app\common\Member::addscore($orderinfo['aid'],$orderinfo['mid'],$orderinfo['scoredk'],'拼团失败退款返还');
					}
					//优惠券抵扣的返还
					if($orderinfo['coupon_rid'] > 0){
						Db::name('coupon_record')->where('aid',aid)->where('mid',$orderinfo['mid'])->where('id',$orderinfo['coupon_rid'])->update(['status'=>0,'usetime'=>'']);
					}
					Db::name('lucky_collage_order')->where('id',$orderinfo['id'])->update(['status'=>4]);
					//退款成功通知
					$tmplcontent = [];
					$tmplcontent['first'] = '拼团失败退款，¥'.$orderinfo['totalprice'].'已经退回您的付款账户，请留意查收。';
					$tmplcontent['remark'] = '请点击查看详情~';
					$tmplcontent['orderProductPrice'] = (string) $orderinfo['totalprice'];
					$tmplcontent['orderProductName'] = $orderinfo['title'];
					$tmplcontent['orderName'] = $orderinfo['ordernum'];
					\app\common\Wechat::sendtmpl($orderinfo['aid'],$orderinfo['mid'],'tmpl_tuisuccess',$tmplcontent,m_url('activity/luckycollage/orderlist',$orderinfo['aid']));
					//订阅消息
					$tmplcontent = [];
					$tmplcontent['amount6'] = $orderinfo['totalprice'];
					$tmplcontent['thing3'] = $orderinfo['title'];
					$tmplcontent['character_string2'] = $orderinfo['ordernum'];
					
					$tmplcontentnew = [];
					$tmplcontentnew['amount3'] = $orderinfo['totalprice'];
					$tmplcontentnew['thing6'] = $orderinfo['title'];
					$tmplcontentnew['character_string4'] = $orderinfo['ordernum'];
					\app\common\Wechat::sendwxtmpl($orderinfo['aid'],$orderinfo['mid'],'tmpl_tuisuccess',$tmplcontentnew,'pages/my/usercenter',$tmplcontent);

					//短信通知
					$member = Db::name('member')->where('id',$orderinfo['mid'])->find();
					if($member['tel']){
						$tel = $member['tel'];
					}else{
						$tel = $order['tel'];
					}
					$rs = \app\common\Sms::send($orderinfo['aid'],$tel,'tmpl_tuisuccess',['ordernum'=>$orderinfo['ordernum'],'money'=>$orderinfo['totalprice']]);
				}
			}
		}

		// 到时开奖
		$tlist = Db::name('lucky_collage_order_team')->where("`status`=2 and {$time} >= createtime+teamhour*3600")->order('createtime desc')->select()->toArray();
		foreach ($tlist as $key => $value) {
			$luckyOrder = Db::name('lucky_collage_order')->where('status', 1)->where('iszj', 0)->where('teamid', $value['id'])->find();
			if(!empty($luckyOrder))
				\app\model\LuckyCollage::kaijiang($luckyOrder);
		}

		// 机器人自动参团
    	$randTime = mt_rand(360, 3500);
		$tlist = Db::name('lucky_collage_order_team')->where("`status`=1 and {$time} >= createtime+{$randTime}")->order('createtime desc')->select()->toArray();
		foreach ($tlist as $key => $value) {
			// 如果没有机器人参团，随机机器人参团
			$isOrder = Db::name('lucky_collage_order')->where('teamid', $value['id'])->where('isjiqiren', 1)->find();
			if(empty($isOrder)){				
				$copyOrderInfo = Db::name('lucky_collage_order')->where('status', 1)->where('teamid', $value['id'])->find();
				if(!empty($copyOrderInfo)){
					$robots = Db::name('lucky_collage_jiqilist')->orderRaw('rand()')->where('aid', $copyOrderInfo['aid'])->where('is_auto', 1)->limit(1)->find();
					if(!empty($robots)){
						$copyOrderInfo['ordernum'] = date('ymdHis'). $copyOrderInfo['aid'] .rand(1000,9999);
						$copyOrderInfo['mid'] = $robots['id'];
						$copyOrderInfo['freight_price'] = 0;
						$copyOrderInfo['leveldk_money'] = 0;
						$copyOrderInfo['scoredk_money'] = 0;
						$copyOrderInfo['scoredkscore'] = 0;
						$copyOrderInfo['createtime'] = time();
						$copyOrderInfo['isjiqiren'] = 1;
						$copyOrderInfo['status'] = 1;
						$copyOrderInfo['paytype'] = '后台支付2';
						$copyOrderInfo['freight_time'] = ''; 
						$copyOrderInfo['buytype'] = 3;
						$copyOrderInfo['hexiao_code'] = random(16);
						$copyOrderInfo['hexiao_qr'] = createqrcode(m_url('admin/hexiao/hexiao?type=lucky_collage&co='.$copyOrderInfo['hexiao_code']));
						$copyOrderInfo['platform'] = 'wx';

						$copyOrderInfo['linkman'] = '';
						$copyOrderInfo['tel'] = '';
						$copyOrderInfo['area'] = '';
						$copyOrderInfo['area2'] ='';
						$copyOrderInfo['address'] = '';
						$copyOrderInfo['longitude'] = '';
						$copyOrderInfo['latitude'] = '';

						unset($copyOrderInfo['id']);

						Db::name('lucky_collage_order')->insertGetId($copyOrderInfo);

						$teamInfo['num'] = $value['num'] + 1;
						$teamInfo['status'] = ($teamInfo['num'] >= $value['teamnum']) ? 2 : 1;
						Db::name('lucky_collage_order_team')->where('id', $value['id'])->update($teamInfo);
					}
				}
			}
		}
		
		//机器人参团2
		$collagesyssets = Db::name('lucky_collage_sysset')->where('jqrct',1)->where('jqrcttime','>',0)->select()->toArray();
		foreach($collagesyssets as $collagesysset){
    		if($collagesysset['jqrct'] == 1 && $collagesysset['jqrcttime'] >0)
    		{
    		    $subTime = strtotime(date('Y-m-d H:i:s', strtotime('-'.$collagesysset['jqrcttime'].' seconds')));
    		    $tlist = Db::name('lucky_collage_order_team')->where('aid',$collagesysset['aid'])->where("`status`=1 and createtime <= {$subTime} and isji = 0")->order('createtime desc')->select()->toArray();
    		    Db::name('lucky_collage_order_team')->where('aid',$collagesysset['aid'])->where("`status`=1 and createtime <= {$subTime} and isji = 0")->update(['isji'=>1]);
    		    foreach ($tlist as $key => $value) {
        			// 如果没有机器人参团，随机机器人参团
        				$copyOrderInfo = Db::name('lucky_collage_order')->where('aid',$collagesysset['aid'])->where('status', 1)->where('teamid', $value['id'])->find();
        				if(!empty($copyOrderInfo)){
        					$robots = Db::name('lucky_collage_jiqilist')->orderRaw('rand()')->where('aid', $copyOrderInfo['aid'])->limit(1)->find();
        					if(!empty($robots)){
        						$copyOrderInfo['ordernum'] = date('ymdHis'). $copyOrderInfo['aid'] .rand(1000,9999);
        						$copyOrderInfo['mid'] = $robots['id'];
        						$copyOrderInfo['freight_price'] = 0;
        						$copyOrderInfo['leveldk_money'] = 0;
        						$copyOrderInfo['scoredk_money'] = 0;
        						$copyOrderInfo['scoredkscore'] = 0;
        						$copyOrderInfo['createtime'] = time();
        						$copyOrderInfo['isjiqiren'] = 1;
        						$copyOrderInfo['status'] = 1;
        						$copyOrderInfo['paytype'] = '后台支付2';
        						$copyOrderInfo['freight_time'] = ''; 
        						$copyOrderInfo['buytype'] = 3;
        						$copyOrderInfo['hexiao_code'] = random(16);
        						$copyOrderInfo['hexiao_qr'] = createqrcode(m_url('admin/hexiao/hexiao?type=lucky_collage&co='.$copyOrderInfo['hexiao_code']));
        						$copyOrderInfo['platform'] = 'wx';
        
        						$copyOrderInfo['linkman'] = '';
        						$copyOrderInfo['tel'] = '';
        						$copyOrderInfo['area'] = '';
        						$copyOrderInfo['area2'] ='';
        						$copyOrderInfo['address'] = '';
        						$copyOrderInfo['longitude'] = '';
        						$copyOrderInfo['latitude'] = '';
        
        						unset($copyOrderInfo['id']);
        
        						Db::name('lucky_collage_order')->insertGetId($copyOrderInfo);
        
        						$teamInfo['num'] = $value['num'] + 1;
        						$teamInfo['status'] = ($teamInfo['num'] >= $value['teamnum']) ? 2 : 1;
        						Db::name('lucky_collage_order_team')->where('id', $value['id'])->update($teamInfo);
        					}
        				}
        			}
    		}
		}
		
		
		
		if(getcustom('yueke')){
			$orderlist = Db::name('yueke_order')->where('status',0)->select()->toArray();
			$autocloseArr = [];
			foreach($orderlist as $order){
				if(!$autocloseArr[$order['aid']]){
					$autocloseArr[$order['aid']] = Db::name('yueke_set')->where('aid',$order['aid'])->value('autoclose');
				}
				if($order['createtime'] + $autocloseArr[$order['aid']]*60 > time()) continue;
				$aid = $order['aid'];
				$mid = $order['mid'];
				$orderid = intval($order['id']);
				$order = Db::name('yueke_order')->where('id',$orderid)->find();
				if(!$order || $order['status']!=0){
					//return $this->json(['status'=>0,'msg'=>'关闭失败,订单状态错误']);
				}else{
					//优惠券抵扣的返还
					if($order['coupon_rid'] > 0){
						Db::name('coupon_record')->where('aid',$aid)->where('mid',$mid)->where('id',$order['coupon_rid'])->update(['status'=>0,'usetime'=>'']);
					}
					$rs = Db::name('yueke_order')->where('id',$orderid)->where('aid',$aid)->where('mid',$mid)->update(['status'=>4]);
				}
			}
		}

        if(getcustom('express_wx')){
            //判断是否自动派单
            $peisong_set = \db('peisong_set')->where('express_wx_status',1)->where('express_wx_paidan',1)->select()->toArray();
            if($peisong_set){
                foreach ($peisong_set as $set){
                    $orderlist = Db::name('shop_order')->where('aid',$set['aid'])->where('status',1)->where('freight_type',2)->whereNull('express_type')->limit(50)->select()->toArray();
                    foreach ($orderlist as $item){
                        Db::name('shop_order')->where('id',$item['id'])->update(['express_type'=>'express_wx']);
                        \app\custom\ExpressWx::addOrder('shop_order',$item);
                    }
                }
            }
        }

		if(getcustom('plug_mantouxia')){
			Db::name('form_order')->where('money','>',0)->where('paystatus',0)->where('createtime','<',time() - 3600)->delete();
			Db::name('payorder')->where('type','form')->where('status',0)->where('createtime','<',time() - 3600)->delete();
		}

		$wxpaylog = Db::name('wxpay_log')->where('fenzhangmoney','>',0)->where('isfenzhang',0)->where('createtime','<',time()-60)->select()->toArray();
		if($wxpaylog){
			$dbwxpayset = Db::name('sysset')->where('name','wxpayset')->value('value');
			$dbwxpayset = json_decode($dbwxpayset,true);
			foreach($wxpaylog as $v){
				$sub_mchid = $v['sub_mchid'];
				$appinfo = \app\common\System::appinfo($v['aid'],$v['platform']);
				if(!$sub_mchid) $sub_mchid = $appinfo['wxpay_sub_mchid'];
				if($v['bid'] > 0){
					$bset = Db::name('business_sysset')->where('aid',$v['aid'])->find();
					$dbwxpayset = [
						'mchname'=>$bset['wxfw_mchname'],
						'appid'=>$bset['wxfw_appid'],
						'mchid'=>$bset['wxfw_mchid'],
						'mchkey'=>$bset['wxfw_mchkey'],
						'apiclient_cert'=>$bset['wxfw_apiclient_cert'],
						'apiclient_key'=>$bset['wxfw_apiclient_key'],
					];
					$receiver = ['type'=>'MERCHANT_ID','name'=>$dbwxpayset['mchname'],'account'=>$dbwxpayset['mchid'],'relation_type'=>'SERVICE_PROVIDER'];
				}else{
					$admin = Db::name('admin')->where('id',$v['aid'])->find();
					if($admin['choucheng_receivertype'] == 0){
						$receiver = ['type'=>'MERCHANT_ID','name'=>$dbwxpayset['mchname'],'account'=>$dbwxpayset['mchid'],'relation_type'=>'SERVICE_PROVIDER'];
					}elseif($admin['choucheng_receivertype'] == 1){
						$receiver = ['type'=>'MERCHANT_ID','name'=>$admin['choucheng_receivertype1_name'],'account'=>$admin['choucheng_receivertype1_account'],'relation_type'=>'PARTNER'];
					}elseif($admin['choucheng_receivertype'] == 2){
						if($admin['choucheng_receivertype2_openidtype'] == 0){
							$receiver = ['type'=>'PERSONAL_OPENID','name'=>$admin['choucheng_receivertype2_name'],'account'=>$admin['choucheng_receivertype2_account'],'relation_type'=>'PARTNER'];
						}else{
							if($v['platform'] == 'wx'){
								$receiver = ['type'=>'PERSONAL_SUB_OPENID','name'=>$admin['choucheng_receivertype2_name'],'account'=>$admin['choucheng_receivertype2_accountwx'],'relation_type'=>'PARTNER','sub_appid'=>$appinfo['appid']];
							}else{
								$receiver = ['type'=>'PERSONAL_SUB_OPENID','name'=>$admin['choucheng_receivertype2_name'],'account'=>$admin['choucheng_receivertype2_account'],'relation_type'=>'PARTNER','sub_appid'=>$appinfo['appid']];
							}
						}
					}
				}
				$rs = $this->profitsharing($receiver,$sub_mchid,$dbwxpayset,$v['transaction_id'],$v['fenzhangmoney'],0);
				if($rs['status'] == 0){
					\think\facade\Log::write($rs);
					Db::name('wxpay_log')->where('id',$v['id'])->update(['isfenzhang'=>2,'fz_errmsg'=>$rs['msg']]);
				}else{
					Db::name('wxpay_log')->where('id',$v['id'])->update(['isfenzhang'=>1,'fz_errmsg'=>$rs['msg'],'fz_ordernum'=>$rs['ordernum']]);
				}
			}
		}

		$this->fenhong('perminute');

		if(getcustom('restaurant')){
			\app\custom\Restaurant::auto_perminute();
		}

 //再次同步小程序发货
        \app\common\Order::retryUploadShipping();
        
        if(getcustom('everyday_hongbao')) {
            if(date('G') == 8) {
                $this->hbcalculate();
            }
        }

        if(getcustom('image_search')){
            $this->product_img_baidu_sync();
        }
        if(getcustom('choujiang_time')){
            $this->run_dscj();
        }
        
        // 执行单数奖自动发放
        // $this->run_danshujiang();
        
        if(getcustom('invite_free')){
        	//结束已完成的免单未发放的订单
        	\app\custom\InviteFree::deal_finishfree();
		}
//            $this->huidong();

		$this->sxpayquery();

	}
	
	private function sxpayquery(){
		$payorderList = Db::name('payorder')->where('issxpay',1)->where('status',0)->where('createtime','>',time()-10*60)->where('createtime','<',time()-2*60)->select()->toArray();
		foreach($payorderList as $payorder){
			$aid = $payorder['aid'];
			$mid = $payorder['mid'];
			$ordernum = $payorder['ordernum'];
			$rs = \app\custom\Sxpay::tradeQuery($payorder);
			\think\facade\Log::write($rs);
			if($rs['status'] == 1 && ($rs['data']['tranSts'] == 'CLOSED' || $rs['data']['tranSts'] == 'FAIL' || $rs['data']['tranSts'] == 'CANCELED')){
				Db::name('payorder')->where('id',$payorder['id'])->update(['issxpay'=>0]);
			}
			if($rs['status'] == 1 && $rs['data']['tranSts'] == 'SUCCESS'){
				$attach = explode(':',$rs['data']['extend']);
				$aid = intval($attach[0]);
				$tablename = $attach[1];
				$platform = $attach[2];
				if($platform == 'sxpaymp') $platform = 'mp';
				if($platform == 'sxpaywx') $platform = 'wx';
				if($platform == 'sxpayalipay') $platform = 'alipay';
				$transaction_id = $rs['data']['transactionId'];
				$isbusinesspay = 0;
				if($attach[4]){
					$isbusinesspay = 1;
				}
				Db::name('payorder')->where('id',$payorder['id'])->update(['platform'=>$platform,'isbusinesspay'=>$isbusinesspay]);

				if($payorder['score'] > 0){
					$rs = \app\common\Member::addscore($aid,$mid,-$payorder['score'],'支付订单，订单号：'.$ordernum);
					if($rs['status'] == 0){
						$order = $payorder;
						$order['totalprice'] = $order['money'];
						$order['paytypeid'] = 2;
						\app\common\Order::refund($order,$order['money'],'积分扣除失败退款');
						continue;
					}
				}
				$rs = \app\model\Payorder::payorder($payorder['id'],'微信支付',2,$transaction_id);
				if($rs['status']==0) continue;

				$total_fee = intval($payorder['money']*100);

				$set = Db::name('admin_set')->where('aid',$aid)->find();
				//消费送积分
				if($tablename != 'recharge' && $set['scorein_money']>0 && $set['scorein_score']>0){
					$givescore = floor($total_fee*0.01 / $set['scorein_money']) * $set['scorein_score'];
					\app\common\Member::addscore($aid,$mid,$givescore,'消费送'.t('积分'));
				}
				if(getcustom('business_moneypay')){ //多商户设置的消费送积分
					if($payorder['bid'] > 0 && $tablename != 'shop'){
						$bset = Db::name('business_sysset')->where('aid',$aid)->find();
						$givescore = floor($total_fee*0.01 / $bset['scorein_money']) * $bset['scorein_score'];
						\app\common\Member::addscore($aid,$mid,$givescore,'消费送'.t('积分'));
					}
				}
				//充值送积分
				if($tablename == 'recharge' && $set['scorecz_money']>0 && $set['scorecz_score']>0){
					$givescore = floor($total_fee*0.01 / $set['scorecz_money']) * $set['scorecz_score'];
					\app\common\Member::addscore($aid,$mid,$givescore,'充值送'.t('积分'));
				}

				if($rs['status'] == 1){
					//记录
					$data = array();
					$data['aid'] = $aid;
					$data['mid'] = $payorder['mid'];
					$data['openid'] = $rs['data']['uuid'];
					$data['tablename'] = $tablename;
					$data['givescore'] = $givescore;
					$data['ordernum'] = $rs['data']['ordNo'];
					$data['mch_id'] = $rs['data']['mno'];
					$data['transaction_id'] = $rs['data']['transactionId'];
					$data['total_fee'] = $rs['data']['oriTranAmt'];
					$data['createtime'] = time();
					Db::name('wxpay_log')->insert($data);
					\app\common\Member::uplv($aid,$mid);
				}
			}
		}
	}
	private function hbcalculate()
    {
        if(getcustom('everyday_hongbao')) {
            $date = date('Y-m-d');
            $todayStart = strtotime($date);
            $yestdayStart = $todayStart - 86400;
            $yestdayEnd = $yestdayStart + 86399;

            $yestdayDate = date('Y-m-d',$yestdayStart);

            $syssetlist = Db::name('admin_set')->where('1=1')->select()->toArray();
            foreach($syssetlist as $sysset) {
                $aid = $sysset['aid'];
                $hd = Db::name('hongbao_everyday')->where('aid',$aid)->find();
                if($hd['status']!=1 || $hd['num'] < 1) continue;
                if($hd['starttime'] > time() && $hd['endtime'] < time()) continue;
                $haveHongbao = Db::name('hongbao_everyday_list')->where('aid',$aid)->where('createdate','=',$date)->count();
                if($haveHongbao > 0) continue;
                //前一天业绩
                if($hd['shop_order_money_type'] == 'pay') {
                    $where[] = ['status', 'in', [1,2,3]];
                    $where[] = ['paytime', 'between', [$yestdayStart,$yestdayEnd]];
                }else if($hd['shop_order_money_type'] == 'receive') {
                    $where[] = ['status', '=', 3];
                    $where[] = ['collect_time', 'between', [$yestdayStart,$yestdayEnd]];
                } else {
                    $where[] = ['status', 'in', [1,2,3]];
                    $where[] = ['paytime', 'between', [$yestdayStart,$yestdayEnd]];
                }
                $totalOrder = Db::name('shop_order')->where('aid',$aid)->where('bid', 0)->where($where)->sum('totalprice');
                $totalOrder = round($totalOrder * $hd['hongbao_bl'] / 100,2);
                $orderBusiness = Db::name('shop_order')->where('aid',$aid)->where('bid', '>',0)->where($where)->field('id,aid,bid,totalprice')->select()->toArray();
                $business = Db::name('business')->where('aid',$aid)->column('feepercent','id');
                $totalOrderBusiness = 0;
                foreach ($orderBusiness as $item) {
                    $totalOrderBusiness += $item['totalprice'] * $business[$item['bid']] / 100;
                }
                $totalOrderBusiness = round($totalOrderBusiness * $hd['hongbao_bl_business'] / 100,2);
                //买单业绩
                $maidanOrder = Db::name('maidan_order')->where('aid',$aid)->where('createtime','between',[$yestdayStart,$yestdayEnd])->where('status',1)->select()->toArray();
                $totalMaidan = 0;
                foreach ($maidanOrder as $item) {
                    $totalMaidan += $item['paymoney'] * $business[$item['bid']] / 100;
                }
                $totalMaidan = round($totalMaidan * $hd['hongbao_bl_maidan'] / 100,2);

                $yestdayLeft = Db::name('hongbao_everyday_list')->where('aid',$aid)->where('createdate','=',$yestdayDate)->sum('left');
                $total = $totalOrder + $totalOrderBusiness + $totalMaidan + $yestdayLeft;
                //生成随机红包(改为平均)
                $dataHongbao = [];
                $time = time();
//            $total = $total * 100;
                $minMoney = 1;
                $avgMoney = $total/$hd['num'];
                $avgMoney = substr(sprintf("%.3f", $avgMoney), 0, -1);
//            dd($total);
                if($avgMoney < 0.01) continue;
                for($i=0;$i<$hd['num'];$i++) {
                    if($i == $hd['num'] - 1)
                        $money = $total;
                    else
//                    $money = rand($minMoney,($total - ($hd['num']-$i) * $minMoney));
                        $money = $avgMoney;
                    $dataHongbao[] = [
                        'aid' => $aid,
                        'createdate' => $date,
                        'createtime' => $time,
                        'money' => $money,
                        'left' => $money,
                    ];
                    $total -= $money;
                    if($total <= 0) {
                        break;
                    }
                }
                Db::name('hongbao_everyday_list')->limit(100)
                    ->insertAll($dataHongbao);
            }
        }
    }
	//每小时执行一次
	private function perhour(){
		$time = time();
		//商城自动收货
		
		$shopsetlist = Db::name('shop_sysset')->select()->toArray();
		\app\model\Member::member_tag();
		foreach($shopsetlist as $sysset){
			$aid = $sysset['aid'];
			if($aid){
				if(getcustom('plug_yang')){
					$list = Db::name('shop_order')->where("aid={$aid} and bid=0 and status=2 and ".time().">`send_time` + 86400*".$sysset['autoshdays'])->select()->toArray();
				}else{
					$list = Db::name('shop_order')->where("aid={$aid} and status=2 and ".time().">`send_time` + 86400*".$sysset['autoshdays'])->select()->toArray();
				}
				foreach($list as $order){
                    $refundOrder = Db::name('shop_refund_order')->where('refund_status','in',[1,4])->where('aid',$aid)->where('orderid',$order['id'])->count();
                    if($refundOrder){
                        continue;
                    }
					$rs = \app\common\Order::collect($order,'shop');
					if($rs['status'] == 0) continue;
					Db::name('shop_order')->where('id',$order['id'])->update(['status'=>3,'collect_time'=>time()]);
					Db::name('shop_order_goods')->where('orderid',$order['id'])->update(['status'=>3,'endtime'=>time()]);
					\app\common\Member::uplv($aid,$order['mid']);
				}

				if(getcustom('plug_yang')){
					$list = Db::name('shop_order')->where("aid={$aid} and bid>0 and status=2 and ".time().">`send_time` + 3600*(select autocollecthour from ddwx_business where id=ddwx_shop_order.bid)")->select();
					foreach($list as $order){
						$refundOrder = Db::name('shop_refund_order')->where('refund_status','in',[1,4])->where('aid',$aid)->where('orderid',$order['id'])->count();
						if($refundOrder){
							continue;
						}
						$rs = \app\common\Order::collect($order,'shop');
						if($rs['status'] == 0) continue;
						Db::name('shop_order')->where('id',$order['id'])->update(['status'=>3,'collect_time'=>time()]);
						Db::name('shop_order_goods')->where('orderid',$order['id'])->update(['status'=>3,'endtime'=>time()]);
						\app\common\Member::uplv($aid,$order['mid']);
					}
				}
			}
		}
		//秒杀自动收货
        $seckillsetlist = Db::name('seckill_sysset')->select()->toArray();
        foreach($seckillsetlist as $sysset){
            $aid = $sysset['aid'];
            if($aid){
                $list = Db::name('seckill_order')->where("aid={$aid} and status=2 and ".time().">`send_time` + 86400*".$sysset['autoshdays'])->select()->toArray();
                foreach($list as $order){
                    $rs = \app\common\Order::collect($order,'seckill');
                    if($rs['status'] == 0) continue;
                    Db::name('seckill_order')->where('id',$order['id'])->update(['status'=>3,'collect_time'=>time()]);
                }
            }
        }
		//拼团自动收货
		$collagesetlist = Db::name('collage_sysset')->select()->toArray();
		foreach($collagesetlist as $sysset){
			$aid = $sysset['aid'];
			if($aid){
				$list = Db::name('collage_order')->where("aid={$aid} and status=2 and ".time().">`send_time` + 86400*".$sysset['autoshdays'])->select()->toArray();
				foreach($list as $order){
					$rs = \app\common\Order::collect($order,'collage');
					if($rs['status'] == 0) continue;
					Db::name('collage_order')->where('id',$order['id'])->update(['status'=>3,'collect_time'=>time()]);
				}
			}
		}
		//团购自动收货
        $tuangousetlist = Db::name('tuangou_sysset')->select()->toArray();
        foreach($tuangousetlist as $sysset){
            $aid = $sysset['aid'];
            if($aid){
                $list = Db::name('tuangou_order')->where("aid={$aid} and status=2 and ".time().">`send_time` + 86400*".$sysset['autoshdays'])->select()->toArray();
                foreach($list as $order){
                    $rs = \app\common\Order::collect($order,'tuangou');
                    if($rs['status'] == 0) continue;
                    Db::name('tuangou_order')->where('id',$order['id'])->update(['status'=>3,'collect_time'=>time()]);
                }
            }
        }
		//幸运拼团自动收货
		$collagesetlist = Db::name('lucky_collage_sysset')->select()->toArray();
		foreach($collagesetlist as $sysset){
			$aid = $sysset['aid'];
			if($aid){
				$list = Db::name('lucky_collage_order')->where("aid={$aid} and status=2 and ".time().">`send_time` + 86400*".$sysset['autoshdays'])->select()->toArray();
				foreach($list as $order){
					$rs = \app\common\Order::collect($order,'lucky_collage');
					if($rs['status'] == 0) continue;
					Db::name('lucky_collage_order')->where('id',$order['id'])->update(['status'=>3,'collect_time'=>time()]);
				}
			}
		}

		//砍价自动收货
		$kanjiasetlist = Db::name('kanjia_sysset')->select()->toArray();
		foreach($kanjiasetlist as $sysset){
			$aid = $sysset['aid'];
			if($aid){
				$list = Db::name('kanjia_order')->where("aid={$aid} and status=2 and ".time().">`send_time` + 86400*".$sysset['autoshdays'])->select()->toArray();
				foreach($list as $order){
					$orderid = $order['id'];
					$mid = $order['mid'];
					$rs = \app\common\Order::collect($order,'kanjia');
					if($rs['status'] == 0) continue;
					Db::name('kanjia_order')->where('aid',$aid)->where('mid',$mid)->where('id',$orderid)->update(['status'=>3,'collect_time'=>time()]);
				}
			}
		}
		//积分商城自动收货
		$scoreshopsetlist = Db::name('scoreshop_sysset')->select()->toArray();
		foreach($scoreshopsetlist as $sysset){
			$aid = $sysset['aid'];
			if($aid){
				$list = Db::name('scoreshop_order')->where("aid={$aid} and status=2 and ".time().">`send_time` + 86400*".$sysset['autoshdays'])->select()->toArray();
				foreach($list as $order){
					Db::name('scoreshop_order')->where('id',$order['id'])->update(['status'=>3,'collect_time'=>time()]);
				}
			}
		}
		//会员等级到期
		$memberlist = Db::name('member')->where("levelendtime>0 and levelendtime<".time())->select()->toArray();
		foreach($memberlist as $member){
		    $is_default = 1;
            $defaultlevel = Db::name('member_level')->where('aid', $member['aid'])->where('isdefault', 1)->find();
			if(getcustom('next_level_set')){
                //是不是有设置的下个等级
                $curlevel = Db::name('member_level')->where('aid',$member['aid'])->where('id',$member['levelid'])->find();
                if ($curlevel && $curlevel['next_level_id'] > 0 && $curlevel['next_level_id']!=$defaultlevel['id']) {
                    $nextlevel = Db::name('member_level')->where('id',$curlevel['next_level_id'])->find();
                    if($nextlevel){
                        $is_default = 0;
                        $newlv['levelid'] = $nextlevel['id'];
                        $newlv['levelendtime'] = strtotime(date('Y-m-d')) + 86400 + 86400 * $nextlevel['yxqdate'];
                        $newlv['ylevelid'] = $member['levelid'];
                        Db::name('member')->where('id', $member['id'])->update($newlv);
                    }
                }
            }
			if($is_default==1) {
                Db::name('member')->where('id', $member['id'])->update(['levelid' => $defaultlevel['id'], 'levelendtime' => 0,'ylevelid'=>$member['levelid']]);
            }
		}
		$memberlist = Db::name('member')->field('id,aid,shengjiscore,levelid')->where("shengjiscore =0 ")->select()->toArray();//升级积分为0
		foreach($memberlist as $member){
		    $is_default = 1;
            $defaultlevel = Db::name('member_level')->where('aid', $member['aid'])->where('isdefault', 1)->find();
            $newlevelid = $defaultlevel['id'];
            if($member['levelid'])
            {
                //是不是有设置的下个等级
                $curlevel = Db::name('member_level')->where('aid',$member['aid'])->where('id',$member['levelid'])->find();
                if(!empty($curlevel))
                {
                    if($curlevel['shengjijifen_status'] ==1)
                    {
                        if($curlevel['next_level_id'] >0)
                        {
                            $newlevelid = $curlevel['next_level_id'];
                        }
                    }else{
                        $newlevelid = 0;
                    }
                }
            }
            if($newlevelid >0)
            {
                $newlv = [];
                $newlv['levelid'] = $newlevelid;
                Db::name('member')->where('id', $member['id'])->update($newlv);
            }
		}

		//其他分组等级到期后失效
        Db::name('member_level_record')->where("levelendtime>0 and levelendtime<".time())->delete();
		
		//延时结算分销佣金
		$syssetlist = Db::name('admin_set')->where('fxjiesuantime_delaydays','<>',0)->select()->toArray();
		foreach($syssetlist as $sysset){
			\app\common\Order::jiesuanCommission($sysset['aid']);
		}

		if(getcustom('xixie')){
            \app\custom\Xixie::auto_endorder();
        }
        
		$this->fenhong('perhour');

        if(getcustom('restaurant')){
            \app\custom\Restaurant::auto_perhour();
        }
	}
	//每天执行
	private function perday(){
		$this->fenhong('perday');

		if(getcustom('score_withdraw')){
            $this->scoreToWithdraw();
        }

		$this->depositOrderExpire();
		
		if(getcustom('fxjiesuantime_perweek')){
			//每周结算佣金
			$syssetlist = Db::name('admin_set')->where('fxjiesuantime_delaydays','<>',0)->select()->toArray();
			foreach($syssetlist as $sysset){
				\app\common\Order::jiesuanCommissionWeek($sysset['aid']);
			}
		}
		
		// 自动增加股权池金额
// 		$aidList = Db::name('equity_pool_set')->where('is_open', 1)->column('aid');
// 		foreach ($aidList as $aid) {
// 			$this->autoIncreaseEquityPool_internal($aid);
// 		}
	}

//	public function test(){
//		$this->fenhong('perhour');
//	}

	//分红
	private function fenhong($pertime){
		$midCommissionList = [];
		$syssetlist = Db::name('admin_set')->where('1=1')->select()->toArray();
		foreach($syssetlist as $sysset){
			$aid = $sysset['aid'];
			$fhjiesuantype = $sysset['fhjiesuantype'];
            $fhjiesuantime_type = $sysset['fhjiesuantime_type'];//分红结算时间类型 0收货后，1付款后
			$fhjiesuantime = $sysset['fhjiesuantime'];
			if($fhjiesuantime_type == 1) {
                if($pertime == 'perday') continue;
                if($pertime == 'perhour') continue;
                $starttime = 1;
                $endtime = time();
            } else {
				if($fhjiesuantime == 10) continue;  //手动结算
                if($fhjiesuantime == 0){ //按天结算
                    if($pertime == 'perhour') continue;
                    if($pertime == 'perminute') continue;
                    //$starttime = strtotime(date('Y-m-d'))-86400;
					$starttime = 1;
                    $endtime = strtotime(date('Y-m-d'));
                }elseif($fhjiesuantime == 1){ //月初结算
                    if($pertime == 'perhour') continue;
                    if($pertime == 'perminute') continue;
                    //$starttime = strtotime(date('Y-m-01').' -1 month');
					$starttime = 1;
                    $endtime = strtotime(date('Y-m-01'));
                }elseif($fhjiesuantime == 2){ //按小时结算
                    if($pertime == 'perday') continue;
                    if($pertime == 'perminute') continue;
                    $starttime = 1;
                    $endtime = time();
                }elseif($fhjiesuantime == 3){ //每分钟结算
                    if($pertime == 'perday') continue;
                    if($pertime == 'perhour') continue;
                    $starttime = 1;
                    $endtime = time();
                }elseif($fhjiesuantime == 4){ //月底结算
                    if($pertime == 'perhour') continue;
                    if($pertime == 'perminute') continue;
                    if(date("t") != date("j")) continue;
                    $starttime = 1;
                    $endtime = time();
                }elseif($fhjiesuantime == 5){ //年底结算
                    if($pertime == 'perhour') continue;
                    if($pertime == 'perminute') continue;
                    if(date("t") != date("j") || date("m")!=12) continue;
                    $starttime = 1;
                    $endtime = time();
                }
            }
			\app\common\Fenhong::jiesuan($aid,$starttime,$endtime);
		}
	}
	//分账
	private function profitsharing($receiver,$sub_mchid,$dbwxpayset,$transaction_id,$chouchengmoney,$times=0){

		$mchkey = $dbwxpayset['mchkey'];
		$sslcert = ROOT_PATH.str_replace(PRE_URL.'/','',$dbwxpayset['apiclient_cert']);
		$sslkey = ROOT_PATH.str_replace(PRE_URL.'/','',$dbwxpayset['apiclient_key']);

		$pars = array();
		$pars['mch_id'] = $dbwxpayset['mchid'];
		$pars['sub_mch_id'] = $sub_mchid;
		$pars['appid'] = $dbwxpayset['appid'];
		$pars['nonce_str'] = random(32);
		$pars['transaction_id'] = $transaction_id;
		$pars['out_order_no'] = 'P'.date('YmdHis').rand(1000,9999);
		$pars['receivers'] = jsonEncode([['type'=>$receiver['type'],'account'=>$receiver['account'],'amount'=>intval($chouchengmoney*100),'description'=>$sub_mchid.'分账']]);
		if($receiver['type'] == 'PERSONAL_SUB_OPENID'){
			$pars['sub_appid'] = $receiver['sub_appid'];
		}
		//$pars['sign_type'] = 'MD5';
		ksort($pars, SORT_STRING);
		$string1 = '';
		foreach ($pars as $k => $v) {
			$string1 .= "{$k}={$v}&";
		}
		$string1 .= "key=" . $mchkey;
		//$pars['sign'] = strtoupper(md5($string1));
		$pars['sign'] = strtoupper(hash_hmac("sha256",$string1 ,$mchkey));
		$xml = array2xml($pars);
		//Log::write($pars);
		//Log::write($xml);
		//Log::write($sslcert);
		$ch = curl_init ();
		curl_setopt ( $ch, CURLOPT_URL, "https://api.mch.weixin.qq.com/secapi/pay/profitsharing" );
		curl_setopt ( $ch, CURLOPT_CUSTOMREQUEST, "POST" );
		curl_setopt ( $ch, CURLOPT_SSL_VERIFYPEER, FALSE );
		curl_setopt ( $ch, CURLOPT_SSL_VERIFYHOST, FALSE );
		curl_setopt ( $ch, CURLOPT_SSLCERT,$sslcert);
		curl_setopt ( $ch, CURLOPT_SSLKEY,$sslkey);
		curl_setopt ( $ch, CURLOPT_FOLLOWLOCATION, 1 );
		curl_setopt ( $ch, CURLOPT_AUTOREFERER, 1 );
		curl_setopt ( $ch, CURLOPT_POSTFIELDS, $xml );
		curl_setopt ( $ch, CURLOPT_RETURNTRANSFER, true );

		$info = curl_exec ( $ch );
		curl_close ( $ch );
		//Log::write($info);
		$resp = (array)(simplexml_load_string($info,'SimpleXMLElement', LIBXML_NOCDATA));
		//Log::write($resp);
		if($resp['return_code'] == 'SUCCESS' && $resp['result_code']=='SUCCESS'){
			return ['status'=>1,'msg'=>'分账成功','resp'=>$resp,'ordernum'=>$pars['out_order_no']];
		}else{
			//Log::write('profitsharing');
			//Log::write($resp);
			if($times == 0 && ($resp['err_code'] == 'PARAM_ERROR' || $resp['err_code'] == 'RECEIVER_INVALID')){
			//if($times == 0 && $resp['err_code'] == 'RECEIVER_INVALID'){
				$pars = array();
				$pars['mch_id'] = $dbwxpayset['mchid'];
				$pars['sub_mch_id'] = $sub_mchid;
				$pars['appid'] = $dbwxpayset['appid'];
				$pars['nonce_str'] = random(32);
				if($receiver['name']){
					$pars['receiver'] = jsonEncode(['type'=>$receiver['type'],'account'=>$receiver['account'],'name'=>$receiver['name'],'relation_type'=>$receiver['relation_type']]);
				}else{
					$pars['receiver'] = jsonEncode(['type'=>$receiver['type'],'account'=>$receiver['account'],'relation_type'=>$receiver['relation_type']]);
				}
				if($receiver['type'] == 'PERSONAL_SUB_OPENID'){
					$pars['sub_appid'] = $receiver['sub_appid'];
				}
				//$pars['sign_type'] = 'MD5';
				ksort($pars, SORT_STRING);
				$string1 = '';
				foreach ($pars as $k => $v) {
					$string1 .= "{$k}={$v}&";
				}
				$string1 .= "key=" . $mchkey;
				//$pars['sign'] = strtoupper(md5($string1));
				$pars['sign'] = strtoupper(hash_hmac("sha256",$string1 ,$mchkey));
				$xml = array2xml($pars);
				$ch = curl_init ();
				curl_setopt ( $ch, CURLOPT_URL, "https://api.mch.weixin.qq.com/secapi/pay/profitsharingaddreceiver" );
				curl_setopt ( $ch, CURLOPT_CUSTOMREQUEST, "POST" );
				curl_setopt ( $ch, CURLOPT_SSL_VERIFYPEER, FALSE );
				curl_setopt ( $ch, CURLOPT_SSL_VERIFYHOST, FALSE );
				curl_setopt ( $ch, CURLOPT_SSLCERT,$sslcert);
				curl_setopt ( $ch, CURLOPT_SSLKEY,$sslkey);
				curl_setopt ( $ch, CURLOPT_FOLLOWLOCATION, 1 );
				curl_setopt ( $ch, CURLOPT_AUTOREFERER, 1 );
				curl_setopt ( $ch, CURLOPT_POSTFIELDS, $xml );
				curl_setopt ( $ch, CURLOPT_RETURNTRANSFER, true );
				$info = curl_exec ( $ch );
				curl_close ( $ch );
				Log::write('profitsharingaddreceiver');
				Log::write($info);
				sleep(2);
				return $this->profitsharing($receiver,$sub_mchid,$dbwxpayset,$transaction_id,$chouchengmoney,1);
			}
			$msg = '未知错误';
			if ($resp['return_code'] == 'FAIL') {
				$msg = $resp['return_msg'];
			}
			if ($resp['result_code'] == 'FAIL') {
				$msg = $resp['err_code_des'];
			}
			return ['status'=>0,'msg'=>$msg,'resp'=>$resp];
		}
	}

    /**
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * 1、小于1积分时，按1积分允许释放。
     * 2、大于1积分时，对小数位四舍五入取整数。
     */
	public function scoreToWithdraw()
    {
        if(getcustom('score_withdraw')){
            $syssetlist = Db::name('admin_set')->where('1=1')->select()->toArray();
            foreach($syssetlist as $sysset){
                if($sysset['score_withdraw'] == 1 && $sysset['score_withdraw_percent_day'] > 0) {
//                $oneStand = (100 / $sysset['score_withdraw_percent_day']);
                    $oneStand = 0;
                    $mlist = Db::name('member')->where('aid', $sysset['aid'])->where('score', '>', $oneStand)->select()->toArray();
                    if($mlist) {
                        foreach ($mlist as $member) {
                            $score_withdraw = 0;
                            $score_withdraw = round($member['score'] * $sysset['score_withdraw_percent_day'] / 100);
                            if($score_withdraw < 1 && $member['score'] > 1) {
                                $score_withdraw = 1;
                            }
                            if($score_withdraw > 0) {
                                \app\common\Member::addscore($sysset['aid'],$member['id'], $score_withdraw * -1, '转为允提'.t('积分',$sysset['aid']));
                                \app\common\Member::addscore_withdraw($sysset['aid'],$member['id'], $score_withdraw, '转入允提'.t('积分',$sysset['aid']));
                            }
                        }
                    }
                }
            }
        }
    }

      //余额宝
    private function yuebao()
    {
        //读取配置
        $syssetlist = Db::name('admin_set')->where('1=1')->select()->toArray();
        if($syssetlist){
            foreach($syssetlist as $sv){
                //读取大于0的用户余额
                $sel_member = Db::name('member')
                    ->where('aid',$sv['aid'])
                    ->where('money','>',0)
                    ->field('id,money,yuebao_rate')
                    ->select()
                    ->toArray();
                //如果余额宝开启、余额收益比例大于0、且用户存在
                if($sv['open_yuebao'] ==1 && $sv['yuebao_rate'] >0 && $sel_member){

                    foreach($sel_member as $mv){
                        //查询是收益率是否单独设置
                        if($mv['yuebao_rate']>=0){
                            $yuebao_rate = $mv['yuebao_rate']/100;
                        }else{
                            $yuebao_rate = $sv['yuebao_rate']/100;
                        }

                        //计算用户收益
                        $m_money = $mv['money']*$yuebao_rate;
                        if($m_money>0){
                            $m_money = round($m_money,2);
                            \app\common\Member::addyuebaomoney($sv['aid'],$mv['id'],$m_money,t('余额宝').'收益',1);
                        }
                    }
                }
            }
        }
    }

    //寄存订单过期
    private function depositOrderExpire()
    {
        $syssetlist = Db::name('restaurant_deposit_sysset')->where('1=1')->select()->toArray();
        if($syssetlist) {
            $time = time();
            foreach ($syssetlist as $set) {
                if($set['time'] > 0) {
                    Db::name('restaurant_deposit_order')->where('aid',$set['aid'])->where('bid',$set['bid'])
                        ->where('status',1)->where('createtime','<',$time-intval($set['time'])*86400)->update(['status' => 4]);
                }
            }
        }
    }

    public function product_img_baidu_sync()
    {
        if(getcustom('image_search')){
            $limit = 10;
            $aids = Db::name('admin')->where('image_search',1)->where('status',1)->column('id');
            if(empty($aids)) return;
            $syssetlist = Db::name('baidu_set')->whereIn('aid',$aids)->where('image_search',1)->where('baidu_apikey','<>','')->where('baidu_secretkey','<>','')->select()->toArray();
            foreach($syssetlist as $sysset) {
                $aid = $sysset['aid'];
                $baidu = new \app\custom\Baidu($aid,$sysset['baidu_appid'],$sysset['baidu_apikey'],$sysset['baidu_secretkey'],$sysset['image_search_num']);
                $baidu->sync($limit);
            }
        }
    }

    //定时抽奖 1分钟执行一次
    public function run_dscj()
    {
        if (getcustom('choujiang_time')) {
            \app\model\Dscj::kaijiang();
        }
    }
    
    /**
     * 单数奖自动任务入口
     */
    public function run_danshujiang()
    {
        try {
            $token = input('param.token');
            $aid = input('param.aid');
            $activity_id = input('param.activity_id');
            
            if(empty($token) || empty($aid)) {
                die('参数错误: 缺少token或aid');
            }
            
            // 验证token
            $query = Db::name('danshujiang_list')->where('aid', $aid);
            
            if(!empty($activity_id)) {
                $query->where('id', $activity_id);
            }
            
            $activities = $query->where('auto_token', $token)
                ->where('claim_mode', 2) // 自动领取模式
                ->select()
                ->toArray();
                
            if(empty($activities)) {
                die('验证失败: token无效或非自动领取模式');
            }
            
            $total_processed = 0;
            $total_success = 0;
            
            // 处理每个活动
            foreach($activities as $activity) {
                Log::info("单数奖自动发放任务：开始处理活动[{$activity['id']}]{$activity['title']} [aid:{$activity['aid']}]");
                
                // 获取该活动待处理的奖励记录
                    $records = Db::name('danshujiang_record')
                        ->where('aid', $activity['aid'])
                        ->where('activity_id', $activity['id'])
                    ->where('status', 0) // 待发放状态
                    ->select()
                    ->toArray();
                        
                if (empty($records)) {
                        Log::info("单数奖自动发放任务：活动[{$activity['id']}]{$activity['title']}没有待处理的奖励 [aid:{$activity['aid']}]");
                        continue;
                    }
                
                // 获取活动允许的商品ID列表
                $allowedProductIds = explode(',', $activity['product_ids']);
                    
                    $activity_processed = 0;
                    $activity_success = 0;
                    
                    // 开始事务
                    Db::startTrans();
                    try {
                        foreach ($records as $record) {
                            $activity_processed++;
                            $total_processed++;
                            
                            // 获取订单信息
                            $order = Db::name('shop_order')
                                ->field('ordernum,totalprice')
                                ->where('id', $record['order_id'])
                                ->find();
                            
                            if (empty($order)) {
                                Log::error("单数奖自动发放任务：订单不存在, 记录ID: {$record['id']}, 订单ID: {$record['order_id']} [aid:{$activity['aid']}]");
                                continue;
                            }
                        
                        // 检查订单商品是否在活动允许的商品列表中
                        $orderProductIds = Db::name('shop_order_goods')
                            ->where('aid', $activity['aid'])
                            ->where('orderid', $record['order_id'])
                            ->column('proid');
                            
                        $validProductIds = array_intersect($orderProductIds, $allowedProductIds);
                        if (empty($validProductIds)) {
                            Log::error("单数奖自动发放任务：订单商品不符合活动要求, 记录ID: {$record['id']}, 订单ID: {$record['order_id']} [aid:{$activity['aid']}]");
                            
                            // 更新记录状态为已拒绝
                            Db::name('danshujiang_record')
                                ->where('id', $record['id'])
                                ->update([
                                    'status' => 2, // 已拒绝
                                    'reject_reason' => '订单商品不符合活动要求',
                                    'update_time' => time()
                                ]);
                            continue;
                        }
                            
                            // 计算说明
                            $remark = sprintf(
                                '单数奖励自动发放 订单号:%s 第%d单 订单金额:%.2f 奖励比例:%d%% 奖励金额:%.2f', 
                                $order['ordernum'],
                                $record['order_num'],
                                $order['totalprice'],
                                $record['reward_rate'],
                                $record['reward_amount']
                            );
                            
                            // 如果需要扣除贡献值
                            $contribution_deducted = 0;
                            if ($activity['deduct_contribution'] == 1 && $activity['contribution_rate'] > 0) {
                                $contribution_deducted = round($record['reward_amount'] * ($activity['contribution_rate'] / 100), 2);
                                if ($contribution_deducted > 0) {
                                    // 检查用户是否有足够的贡献值
                                    $userContribution = Db::name('member')->where('id', $record['mid'])->value('gongxianzhi');
                                    if ($userContribution < $contribution_deducted) {
                                        // 如果贡献值不足，只扣除用户拥有的贡献值
                                        $contribution_deducted = $userContribution > 0 ? $userContribution : 0;
                                        Log::info("单数奖自动发放任务：用户[{$record['mid']}]贡献值不足，调整扣除金额为: {$contribution_deducted} [aid:{$activity['aid']}]");
                                    }

                                    if ($contribution_deducted > 0) {
                                        // 扣除贡献值
                                        $contribution_remark = sprintf(
                                            '单数奖励自动发放扣除贡献值 订单号:%s 第%d单 扣除比例:%d%% 扣除金额:%.2f', 
                                            $order['ordernum'],
                                            $record['order_num'],
                                            $activity['contribution_rate'],
                                            $contribution_deducted
                                        );
                                        
                                        // 记录日志
                                        Log::info("单数奖自动发放任务：用户[{$record['mid']}]扣除贡献值: {$contribution_deducted}, {$contribution_remark} [aid:{$activity['aid']}]");
                                        
                                        // 扣除贡献值
                                        \app\common\Member::addgongxianzhi($record['aid'], $record['mid'], -$contribution_deducted, $contribution_remark);
                                    }
                                    
                                    // 更新记录
                                    Db::name('danshujiang_record')
                                        ->where('id', $record['id'])
                                        ->update([
                                            'contribution_deducted' => $contribution_deducted
                                        ]);
                                }
                            }
                            
                            // 使用Member::addcommission处理佣金
                            \app\common\Member::addcommission($record['aid'], $record['mid'], 0, $record['reward_amount'], $remark);
                            
                            // 更新记录状态
                            Db::name('danshujiang_record')
                                ->where('id', $record['id'])
                                ->update([
                                    'status' => 1,
                                    'update_time' => time()
                                ]);
                            
                            $activity_success++;
                            $total_success++;
                            
                            Log::info("单数奖自动发放任务：用户[{$record['mid']}]奖励自动发放成功: {$record['reward_amount']}, {$remark} [aid:{$activity['aid']}]");
                        }
                        
                        Db::commit();
                        Log::info("单数奖自动发放任务：活动[{$activity['id']}]{$activity['title']}处理完成，成功: {$activity_success}/{$activity_processed} [aid:{$activity['aid']}]");
                    } catch (\Exception $e) {
                        Db::rollback();
                        Log::error("单数奖自动发放任务：活动[{$activity['id']}]{$activity['title']}处理失败: {$e->getMessage()} [aid:{$activity['aid']}]");
                        $errors[] = "活动[{$activity['id']}]{$activity['title']}处理失败: {$e->getMessage()}";
                }
            }
            
            // 记录结束时间
            $end_time = microtime(true);
            $execution_time = round($end_time - $start_time, 2);
            Log::info("单数奖自动发放任务完成，耗时: {$execution_time}秒，处理: {$total_processed}，成功: {$total_success} [aid:{$aid}]");
            
            // 如果是API请求，返回结果
            if ($is_api_request) {
                $total_results[$aid] = [
                    'status' => 1, 
                    'msg' => "自动发放任务完成，处理: {$total_processed}，成功: {$total_success}", 
                    'data' => [
                        'processed' => $total_processed,
                        'success' => $total_success,
                        'errors' => $errors,
                        'execution_time' => $execution_time
                    ]
                ];
            }
        } catch (\Exception $e) {
            Log::error("单数奖自动发放任务异常: {$e->getMessage()} [aid:{$aid}]");
            $errors[] = "单数奖自动发放任务异常: {$e->getMessage()}";
        }
        
        // 如果是API请求，返回结果
        if ($is_api_request) {
            Log::info("============================================================");
            Log::info("单数奖自动任务执行完成");
            Log::info("============================================================");
            if (count($total_results) == 1) {
                return json($total_results[$aid]);
            } else {
                return json(['status' => 1, 'msg' => '处理完成', 'data' => $total_results]);
            }
        }
    }

    public function huidong()
    {
        \app\custom\HuiDong::syncMember();
    }

	/**
	 * 发放等级推荐奖励
	 * @param int $aid 应用ID
	 */
	public function sendLevelReward($aid = 0)
	{
		if(!$aid) {
			$aid = input('param.aid');
		}
		if(!$aid) {
			return json(['code' => 1, 'msg' => '请传入aid']);
		}
		
		// 检查功能是否开启
		$settings = Db::name('level_reward_set')->where('aid', $aid)->find();
		if (!$settings || $settings['status'] != 1) {
			return json(['code' => 1, 'msg' => '功能未开启']);
		}
		
		// 预先查询所有可能需要的数据
		$member_levels = Db::name('member_level')
			->where('aid', $aid)
			->column('name', 'id');
			
		// 获取待发放的推荐记录
		$records = Db::name('level_reward_record')
			->where('aid', $aid)
			->where('status', 0) // 未发放
			->select()
			->toArray();
		
		if (empty($records)) {
			return json(['code' => 0, 'msg' => '没有待发放的奖励']);
		}
		
		// 提前获取所有相关会员信息，避免循环中查询
		$member_ids = array_merge(
			array_column($records, 'from_mid'),
			array_column($records, 'to_mid')
		);
		$member_ids = array_unique($member_ids);
		$members = Db::name('member')
			->where('id', 'in', $member_ids)
			->column('*', 'id');
		
		// 开启事务
		Db::startTrans();
		try {
			$count = 0;
			$error_count = 0;
			$log = [];
			
			foreach ($records as $record) {
				// 检查记录完整性
				if (!isset($record['from_mid']) || !isset($record['to_mid']) || !isset($record['reward_amount'])) {
					$log[] = "记录ID:{$record['id']}数据不完整，跳过处理";
					$error_count++;
					continue;
				}
				
				// 检查会员是否存在
				if (!isset($members[$record['from_mid']])) {
					$log[] = "记录ID:{$record['id']}的推荐人(ID:{$record['from_mid']})不存在，跳过处理";
					$error_count++;
					continue;
				}
				
				$success = false;
				// 根据奖励类型发放不同的奖励
				switch ($record['reward_type']) {
					case 'contribution':
						// 贡献值奖励
						$success = \app\common\Member::addgongxianzhi($aid, $record['from_mid'], $record['reward_amount'], '等级推荐奖励');
						break;
					case 'balance':
						// 余额奖励
						$success = \app\common\Member::addmoney($aid, $record['from_mid'], $record['reward_amount'], '等级推荐奖励');
						break;
					case 'commission':
						// 佣金奖励
						$success = \app\common\Member::addcommission($aid, $record['from_mid'], 0, $record['reward_amount'], '等级推荐奖励');
						break;
					case 'points':
						// 积分奖励
						$success = \app\common\Member::addscore($aid, $record['from_mid'], $record['reward_amount'], '等级推荐奖励');
						break;
					case 'xianjinquan':
						// 现金券奖励
						$success = \app\common\Member::addhei($aid, $record['from_mid'], $record['reward_amount'], '等级推荐奖励');
						break;
				}
				
				if (!$success) {
					$log[] = "记录ID:{$record['id']}奖励发放失败";
					$error_count++;
					continue;
				}
				
				// 更新记录状态
				Db::name('level_reward_record')
					->where('id', $record['id'])
					->update([
						'status' => 1, // 已发放
						'updatetime' => time()
					]);
				
				// 发送通知
				if (!empty($settings['complete_notice'])) {
					$member = $members[$record['from_mid']] ?? [];
					$to_member = $members[$record['to_mid']] ?? [];
					$from_level_name = $member_levels[$record['from_level']] ?? '未知等级';
					$to_level_name = $member_levels[$record['to_level']] ?? '未知等级';
					
					$reward_type_name = '';
					switch ($record['reward_type']) {
						case 'contribution':
							$reward_type_name = t('贡献值');
							break;
						case 'balance':
							$reward_type_name = t('余额');
							break;
						case 'commission':
							$reward_type_name = t('佣金');
							break;
						case 'points':
							$reward_type_name = t('积分');
							break;
						case 'xianjinquan':
							$reward_type_name = t('现金券');
							break;
					}
					
					$notice = $settings['complete_notice'];
					$notice = str_replace('{nickname}', $member['nickname'] ?? '会员', $notice);
					$notice = str_replace('{level_name}', $to_level_name, $notice);
					$notice = str_replace('{reward_type}', $reward_type_name, $notice);
					$notice = str_replace('{reward_amount}', $record['reward_amount'], $notice);
					
					// 添加累计模式的提示信息
					if (isset($record['reward_mode']) && $record['reward_mode'] == 'cumulative') {
						$notice = str_replace('{reward_mode}', '累计创客奖励', $notice);
						$notice = str_replace('{recommend_count}', $record['recommend_count'] ?? 0, $notice);
					} else if (isset($record['reward_mode']) && $record['reward_mode'] == 'cycle') {
						// 处理循环奖励模式的通知
						$cycle_index = $record['cycle_index'] ?? 1; // 默认为第1次循环
						$cycle_count = $record['recommend_count'] ?? 5; // 默认每5人一个循环
						
						$notice = str_replace('{reward_mode}', '循环奖励', $notice);
						$notice = str_replace('{recommend_count}', $cycle_count, $notice);
						
						// 添加循环次数的提示
						$notice = $notice . '（每满'.$cycle_count.'人奖励一次，这是第'.$cycle_index.'次奖励）';
					} else {
						$notice = str_replace('{reward_mode}', '常规奖励', $notice);
						$notice = str_replace('{recommend_count}', 1, $notice);
					}
					
					// 移除 Message::sendmsg 调用
					// \app\common\Message::sendmsg($aid, $record['from_mid'], $notice, 'levelreward');
					
					// 记录通知内容到日志，供参考
					\think\facade\Log::info("会员[{$record['from_mid']}]推荐奖励通知: {$notice}");
				}
				
				$count++;
			}
			
			// 提交事务
			Db::commit();
			
			// 记录日志
			if (!empty($log)) {
				\think\facade\Log::info(implode("\n", $log));
			}
			
			return json([
				'code' => 0, 
				'msg' => "成功发放{$count}条奖励" . ($error_count > 0 ? "，{$error_count}条奖励发放失败" : ""),
				'data' => [
					'success_count' => $count,
					'error_count' => $error_count,
					'log' => $log
				]
			]);
			
		} catch (\Exception $e) {
			// 回滚事务
			Db::rollback();
			\think\facade\Log::error('等级推荐奖励发放异常: ' . $e->getMessage());
			return json(['code' => 1, 'msg' => '奖励发放异常: ' . $e->getMessage()]);
		}
	}

	/**
	 * 生成等级推荐奖励记录
	 * 用于检查符合条件的推荐关系并生成待发放的奖励记录
	 * @param int $aid 应用ID
	 */
	public function generateLevelReward($aid = 0)
	{
		if(!$aid) {
			$aid = input('param.aid/d', 0);
		}
		if(!$aid) {
			return json(['status' => 0, 'msg' => '请传入aid']);
		}
		
		// 检查功能是否开启
		$settings = Db::name('level_reward_set')->where('aid', $aid)->find();
		if (!$settings || $settings['status'] != 1) {
			return json(['status' => 0, 'msg' => '功能未开启']);
		}
		
		// 记录开始时间
		$start_time = microtime(true);
		\think\facade\Log::info('开始执行等级推荐奖励记录生成任务 [aid:'.$aid.']');
		
		// 获取所有有效的奖励规则
		$rules = Db::name('level_reward')
			->where('aid', $aid)
			->where('status', 1)
			->select()
			->toArray();
			
		if (empty($rules)) {
			return json(['status' => 1, 'msg' => '没有有效的奖励规则']);
		}
		
		// 记录本次生成的奖励数
		$count = 0;
		$error_count = 0;
		$log = [];
		
		// 开启事务
		Db::startTrans();
		try {
			// 获取所有会员数据
			$members = Db::name('member')
				->where('aid', $aid)
				->field('id, pid, levelid, path, nickname')
				->select()
				->toArray();
				
			// 构建会员索引
			$member_index = [];
			foreach ($members as $m) {
				$member_index[$m['id']] = $m;
			}
			
			// 生成规则和等级的映射，以便快速查找
			$rule_map = [];
			foreach ($rules as $rule) {
				// 获取奖励规则
				$reward_rules = json_decode($rule['reward_rules'], true) ?: [];
				if (empty($reward_rules)) continue;
				
				// 解析from_level_ids和to_level_ids
				$from_level_ids = json_decode($rule['from_level_ids'], true) ?: [$rule['from_level']];
				$to_level_ids = json_decode($rule['to_level_ids'], true) ?: [$rule['to_level']];
				
				// 记录规则
				foreach ($from_level_ids as $from_level) {
					foreach ($to_level_ids as $to_level) {
						$key = $from_level . '_' . $to_level;
						if (!isset($rule_map[$key])) {
							$rule_map[$key] = [];
						}
						$rule_map[$key][] = [
							'reward_rules' => $reward_rules,
							'reward_mode' => $rule['reward_mode'] ?? 'regular'
						];
					}
				}
			}
			
			// 获取已有的奖励记录，避免重复生成
			$exist_records = Db::name('level_reward_record')
				->where('aid', $aid)
				->field('from_mid, to_mid, from_level, to_level')
				->select()
				->toArray();
				
			// 构建已存在记录的索引
			$exist_map = [];
			foreach ($exist_records as $record) {
				$key = $record['from_mid'] . '_' . $record['to_mid'] . '_' . 
					   $record['from_level'] . '_' . $record['to_level'];
				$exist_map[$key] = true;
			}
			
			// 检查每个会员的推荐关系
			foreach ($members as $member) {
				if (empty($member['pid'])) continue;
				
				// 获取推荐人信息
				$referrer = $member_index[$member['pid']] ?? null;
				if (!$referrer) continue;
				
				// 检查是否符合奖励条件
				$key = $referrer['levelid'] . '_' . $member['levelid'];
				if (!isset($rule_map[$key])) continue;
				
				// 遍历匹配的规则
				foreach ($rule_map[$key] as $rule_info) {
					// 检查是否已经生成过此规则的奖励记录
					$record_key = $referrer['id'] . '_' . $member['id'] . '_' . 
								 $referrer['levelid'] . '_' . $member['levelid'];
					if (isset($exist_map[$record_key])) continue;
					
					// 根据奖励模式和规则生成奖励记录
					$reward_mode = $rule_info['reward_mode'];
					
					foreach ($rule_info['reward_rules'] as $reward_rule) {
						// 对于常规模式，每次都生成记录
						// 对于累计模式，需要计算符合条件的会员数量
						if ($reward_mode == 'cumulative') {
							// 计算推荐人推荐的符合条件的会员数量
							$recommend_count = Db::name('member')
								->where('aid', $aid)
								->where('pid', $referrer['id'])
								->count();
							
							// 检查是否达到累计人数要求
							if ($recommend_count < $reward_rule['recommend_count']) {
								continue;
							}
							
							// 检查之前是否已经获得了类似奖励
							$similar_rewards = Db::name('level_reward_record')
								->where('aid', $aid)
								->where('from_mid', $referrer['id'])
								->where('from_level', $referrer['levelid'])
								->where('reward_amount', $reward_rule['reward_amount'])
								->where('reward_type', $reward_rule['reward_type'])
								->count();
								
							if ($similar_rewards > 0) {
								continue;
							}
						} elseif ($reward_mode == 'cycle') {
							// 循环奖励模式：每满N人发放一次奖励
							
							// 计算推荐人推荐的会员数量
							$recommend_count = Db::name('member')
								->where('aid', $aid)
								->where('pid', $referrer['id'])
								->count();
							
							// 计算应该发放几次奖励
							$cycle_count = intval($reward_rule['recommend_count']);
							if ($cycle_count <= 0) $cycle_count = 5; // 默认每5人一个循环
							$reward_times = floor($recommend_count / $cycle_count);
							
							if ($reward_times <= 0) {
								continue; // 未达到一个循环
							}
							
							// 检查已经发放的次数
							$sent_rewards = Db::name('level_reward_record')
								->where('aid', $aid)
								->where('from_mid', $referrer['id'])
								->where('reward_amount', $reward_rule['reward_amount'])
								->where('reward_type', $reward_rule['reward_type'])
								->where('reward_mode', 'cycle') // 确保只统计循环奖励模式的记录
								->count();
							
							// 检查待发放的次数 - 不需要区分待发放和已发放，只关心总数
							// $pending_rewards = Db::name('level_reward_record')
							// 	->where('aid', $aid)
							// 	->where('from_mid', $referrer['id'])
							// 	->where('reward_amount', $reward_rule['reward_amount'])
							// 	->where('reward_type', $reward_rule['reward_type'])
							// 	->where('status', 0) // 未发放的
							// 	->count();
								
							// 计算还需要生成多少条奖励记录
							$need_rewards = $reward_times - $sent_rewards;
							if ($need_rewards <= 0) {
								continue; // 已经生成了足够的奖励记录
							}
							
							// 循环创建多条记录
							for ($i = 0; $i < $need_rewards; $i++) {
								// 获取当前规则ID
								$currentRuleId = 0;
								foreach ($rules as $r) {
									$from_levels = json_decode($r['from_level_ids'], true) ?: [$r['from_level']];
									$to_levels = json_decode($r['to_level_ids'], true) ?: [$r['to_level']];
									$r_rules = json_decode($r['reward_rules'], true) ?: [];
									
									// 检查规则模式和奖励金额/类型是否匹配
									if ($r['reward_mode'] == 'cycle' && 
										in_array($referrer['levelid'], $from_levels) && 
										in_array($member['levelid'], $to_levels)) {
										
										// 检查奖励规则是否匹配
										foreach ($r_rules as $r_rule) {
											if ($r_rule['reward_amount'] == $reward_rule['reward_amount'] && 
												$r_rule['reward_type'] == $reward_rule['reward_type']) {
												$currentRuleId = $r['id'];
												break 2; // 找到匹配的规则，跳出两层循环
											}
										}
									}
								}
								
								if (!$currentRuleId) {
									\think\facade\Log::error("找不到匹配的规则ID：推荐人[{$referrer['nickname']}]，被推荐人[{$member['nickname']}]，奖励{$reward_rule['reward_amount']}（{$reward_rule['reward_type']}）");
									continue; // 没有找到匹配的规则ID，跳过
								}
							
								// 生成奖励记录
								$cycle_record_data = [
									'aid' => $aid,
									'rule_id' => $currentRuleId, // 使用找到的规则ID
									'from_mid' => $referrer['id'],
									'from_level' => $referrer['levelid'],
									'to_mid' => $member['id'],
									'to_level' => $member['levelid'],
									'reward_amount' => $reward_rule['reward_amount'],
									'reward_type' => $reward_rule['reward_type'],
									'status' => 0, // 未发放
									'createtime' => time(),
									'updatetime' => 0,
									'reward_mode' => 'cycle',
									'recommend_count' => $cycle_count,
									'cycle_index' => $sent_rewards + $i + 1 // 第几个循环
								];
								
								// 插入记录
								$cycle_record_id = Db::name('level_reward_record')->insertGetId($cycle_record_data);
								
								if ($cycle_record_id) {
									$count++;
									$current_cycle = $sent_rewards + $i + 1;
									$log[] = "生成循环奖励记录：推荐人[{$referrer['nickname']}]累计推荐{$recommend_count}人，第{$current_cycle}次循环奖励{$reward_rule['reward_amount']}（{$reward_rule['reward_type']}）";
								} else {
									$error_count++;
									$log[] = "生成循环奖励记录失败：推荐人[{$referrer['nickname']}]";
								}
							}
							
							// 循环模式已处理完毕，跳过常规记录生成
							continue;
						}
						
						// 生成奖励记录（常规模式和累计模式）
						$record_data = [
							'aid' => $aid,
							'from_mid' => $referrer['id'],
							'from_level' => $referrer['levelid'],
							'to_mid' => $member['id'],
							'to_level' => $member['levelid'],
							'reward_amount' => $reward_rule['reward_amount'],
							'reward_type' => $reward_rule['reward_type'],
							'status' => 0, // 未发放
							'createtime' => time(),
							'updatetime' => 0,
							'reward_mode' => $reward_mode,
							'recommend_count' => $reward_rule['recommend_count']
						];
						
						// 插入记录
						$record_id = Db::name('level_reward_record')->insertGetId($record_data);
						
						if ($record_id) {
							$count++;
							$log[] = "生成奖励记录：推荐人[{$referrer['nickname']}]推荐了[{$member['nickname']}]，奖励{$reward_rule['reward_amount']}（{$reward_rule['reward_type']}）";
						} else {
							$error_count++;
							$log[] = "生成奖励记录失败：推荐人[{$referrer['nickname']}]推荐了[{$member['nickname']}]";
						}
					}
				}
			}
			
			// 提交事务
			Db::commit();
			
			// 记录日志
			if (!empty($log)) {
				\think\facade\Log::info(implode("\n", $log));
			}
			
			// 记录结束时间
			$end_time = microtime(true);
			$execution_time = round($end_time - $start_time, 2);
			\think\facade\Log::info("等级推荐奖励记录生成任务完成，耗时: {$execution_time}秒，处理成功: {$count}条 [aid:{$aid}]");
			
			return json([
				'status' => 1, 
				'msg' => "成功生成{$count}条奖励记录" . ($error_count > 0 ? "，{$error_count}条生成失败" : ""),
				'data' => [
					'success_count' => $count,
					'error_count' => $error_count,
					'execution_time' => $execution_time,
					'log' => $log
				]
			]);
			
		} catch (\Exception $e) {
			// 回滚事务
			Db::rollback();
			\think\facade\Log::error('等级推荐奖励记录生成异常: ' . $e->getMessage());
			return json(['status' => 0, 'msg' => '奖励记录生成异常: ' . $e->getMessage()]);
		}
	}

    /**
     * 自动增加股权池金额
     */
    public function autoIncreaseEquityPool()
    {
        $aid = input('param.aid');
        if (!$aid) {
            die('请传入aid');
        }
        
        // 检查股权池功能是否开启
        $set = Db::name('equity_pool_set')->where('aid', $aid)->find();
        if (!$set || $set['is_open'] == 0) {
            die('股权池功能未开启或未设置');
        }
        
        // 检查今天是否已经执行过自动增加
        $today = date('Y-m-d');
        $lastRunLog = Db::name('equity_pool_log')
            ->where('aid', $aid)
            ->where('type', 2) // 类型2是手动增加
            ->where('remark', 'like', '%自动增加股权池金额%')
            ->whereTime('createtime', 'today')
            ->find();
            
        if ($lastRunLog) {
            die('今天已经执行过自动增加操作，上次执行时间：' . date('Y-m-d H:i:s', $lastRunLog['createtime']) . '，增加金额：' . $lastRunLog['amount']);
        }
        
        // 获取当前股权池数据
        $pool = Db::name('equity_pool')->where('aid', $aid)->find();
        if (!$pool) {
            // 初始化股权池数据
            $now = time();
            $pool = [
                'aid' => $aid,
                'total_amount' => 0,
                'equity_count' => 0,
                'createtime' => $now,
                'updatetime' => $now,
                'status' => 1
            ];
            Db::name('equity_pool')->insert($pool);
            $pool['id'] = Db::name('equity_pool')->getLastInsID();
        }
        
        // 计算昨日订单总金额
        $yesterday_start = strtotime(date('Y-m-d 00:00:00', strtotime('-1 day')));
        $yesterday_end = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));
        
        // 查询订单总金额（已支付的订单）
        $yesterday_total = Db::name('shop_order')
            ->where('aid', $aid)
            ->where('status', 'in', [1, 2, 3]) // 已支付、已发货、已完成的订单
            ->where('paytime', 'between', [$yesterday_start, $yesterday_end])
            ->sum('totalprice');
        
        // 按比例计算增加金额
        $increase_amount = round($yesterday_total * $set['rate'] / 100, 2);
        
        // 确保有最小增加金额
        $min_amount = 0.01; // 最小增加0.01元
        if ($increase_amount < $min_amount && $yesterday_total > 0) {
            $increase_amount = $min_amount;
        }
        
        // 如果没有订单或计算结果为0，则不增加
        if ($increase_amount <= 0) {
            die('昨日没有有效订单或计算金额为0，不执行增加操作');
        }
        
        // 计算股权价值和数量
        $before_amount = $pool['total_amount'];
        $after_amount = $before_amount + $increase_amount;
        
        // 开始事务
        Db::startTrans();
        try {
            // 更新股权池金额
            Db::name('equity_pool')
                ->where('aid', $aid)
                ->update([
                    'total_amount' => $after_amount,
                    'updatetime' => time()
                ]);
            
            // 记录操作日志
            Db::name('equity_pool_log')->insert([
                'aid' => $aid,
                'order_id' => 0,
                'mid' => 0, // 系统操作
                'type' => 2, // 手动增加
                'amount' => $increase_amount,
                'before_amount' => $before_amount,
                'after_amount' => $after_amount,
                'remark' => '系统自动增加股权池金额（昨日订单总额：'.$yesterday_total.'元）',
                'createtime' => time()
            ]);
            
            Db::commit();
            die('自动增加股权池金额成功，增加金额：' . $increase_amount . '，昨日订单总额：' . $yesterday_total . '元');
        } catch (\Exception $e) {
            Db::rollback();
            die('操作失败：' . $e->getMessage());
        }
    }
	
	/**
     * 内部处理股权池自动增加金额的方法，供perday调用
     */
    private function autoIncreaseEquityPool_internal($aid)
    {
        // 检查股权池功能是否开启
        $set = Db::name('equity_pool_set')->where('aid', $aid)->find();
        if (!$set || $set['is_open'] == 0) {
            return;
        }
        
        // 检查今天是否已经执行过自动增加
        $lastRunLog = Db::name('equity_pool_log')
            ->where('aid', $aid)
            ->where('type', 2) // 类型2是手动增加
            ->where('remark', 'like', '%自动增加股权池金额%')
            ->whereTime('createtime', 'today')
            ->find();
            
        if ($lastRunLog) {
            Log::write('股权池自动增加已跳过，aid：'.$aid.'，今天已执行过');
            return;
        }
        
        // 获取当前股权池数据
        $pool = Db::name('equity_pool')->where('aid', $aid)->find();
        if (!$pool) {
            // 初始化股权池数据
            $now = time();
            $pool = [
                'aid' => $aid,
                'total_amount' => 0,
                'equity_count' => 0,
                'createtime' => $now,
                'updatetime' => $now,
                'status' => 1
            ];
            Db::name('equity_pool')->insert($pool);
            $pool['id'] = Db::name('equity_pool')->getLastInsID();
        }
        
        // 计算昨日订单总金额
        $yesterday_start = strtotime(date('Y-m-d 00:00:00', strtotime('-1 day')));
        $yesterday_end = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));
        
        // 查询订单总金额（已支付的订单）
        $yesterday_total = Db::name('shop_order')
            ->where('aid', $aid)
            ->where('status', 'in', [1, 2, 3]) // 已支付、已发货、已完成的订单
            ->where('paytime', 'between', [$yesterday_start, $yesterday_end])
            ->sum('totalprice');
        
        // 按比例计算增加金额
        $increase_amount = round($yesterday_total * $set['rate'] / 100, 2);
        
        // 确保有最小增加金额
        $min_amount = 0.01; // 最小增加0.01元
        if ($increase_amount < $min_amount && $yesterday_total > 0) {
            $increase_amount = $min_amount;
        }
        
        // 如果没有订单或计算结果为0，则不增加
        if ($increase_amount <= 0) {
            Log::write('股权池自动增加已跳过，aid：'.$aid.'，昨日没有有效订单或计算金额为0');
            return;
        }
        
        // 计算股权价值和数量
        $before_amount = $pool['total_amount'];
        $after_amount = $before_amount + $increase_amount;
        
        // 开始事务
        Db::startTrans();
        try {
            // 更新股权池金额
            Db::name('equity_pool')
                ->where('aid', $aid)
                ->update([
                    'total_amount' => $after_amount,
                    'updatetime' => time()
                ]);
            
            // 记录操作日志
            Db::name('equity_pool_log')->insert([
                'aid' => $aid,
                'order_id' => 0,
                'mid' => 0, // 系统操作
                'type' => 2, // 手动增加
                'amount' => $increase_amount,
                'before_amount' => $before_amount,
                'after_amount' => $after_amount,
                'remark' => '系统自动增加股权池金额（昨日订单总额：'.$yesterday_total.'元）',
                'createtime' => time()
            ]);
            
            Db::commit();
            Log::write('自动增加股权池金额成功，aid：'.$aid.'，增加金额：'.$increase_amount.'，昨日订单总额：'.$yesterday_total);
        } catch (\Exception $e) {
            Db::rollback();
            Log::write('自动增加股权池金额失败，aid：'.$aid.'，错误：' . $e->getMessage());
        }
    }

    /**
     * 自动为即将到期的周期服务工单创建预约订单
     */
    public function autoCreatePeriodicYuyueOrders()
    {
        // 查找所有开启了周期服务的账号
        $aids = Db::name('admin_set')
                    ->where('periodicservice', 1) // 假设 admin_set 有字段标识是否开启周期服务
                    ->column('aid');
        if(empty($aids)) {
            Log::info('自动创建周期预约单：未找到开启周期服务的账号。');
            return;
        }

        $days_in_advance = 7; // 提前几天创建预约单（可配置）
        $date_threshold = date('Y-m-d', strtotime("+" . $days_in_advance . " days"));
        $today = date('Y-m-d');
        $count = 0;
        $fail_count = 0;

        Log::info('开始自动创建周期服务预约单 (截止日期: ' . $date_threshold . ')');

        foreach($aids as $current_aid) {
            // 查找需要创建预约单的工单
            $stages_to_process = Db::name('periodic_service_stage')->alias('s')
                ->join('periodicservice_order o', 'o.id = s.order_id') // 关联主订单确保主订单状态正常
                ->where('s.aid', $current_aid)
                ->where('s.status', 0) // 只处理"待服务"的工单
                ->whereNull('s.linked_yuyue_order_id') // 只处理未关联预约单的
                ->where('s.scheduled_date', '>=', $today) // 计划日期是今天或未来
                ->where('s.scheduled_date', '<=', $date_threshold) // 计划日期在阈值内
                ->where('o.status', 1) // 只处理"服务中"的主订单
                ->field('s.id')
                ->select()
                ->toArray();

            if (!empty($stages_to_process)) {
                Log::info('账号(ID:'.$current_aid.') 找到 ' . count($stages_to_process) . ' 个待处理工单。');
                // 实例化控制器以调用 protected 方法
                // 注意：这里需要确保 PeriodicServiceOrder 控制器可以被实例化，并且构造函数没有特殊依赖
                try {
                    $controller = new \app\controller\PeriodicServiceOrder(); 
                } catch (\Throwable $th) {
                    Log::error('实例化 PeriodicServiceOrder 控制器失败: ' . $th->getMessage());
                    continue; // 跳过此账号
                }

                foreach ($stages_to_process as $stage_data) {
                    $stageId = $stage_data['id'];
                    // 调用核心逻辑创建预约单
                    $result = $controller->createYuyueOrderFromStageForAuto($stageId); 
                    if ($result['code'] === 0) {
                        $count++;
                        Log::info('为工单(ID:'.$stageId.', AID:'.$current_aid.')成功创建预约单: ' . ($result['msg'] ?? ''));
                    } elseif ($result['code'] === 2) {
                        // 状态为2表示已关联，正常跳过，不计入失败
                        Log::info('工单(ID:'.$stageId.', AID:'.$current_aid.')已关联预约单，跳过。');
                    } else {
                        $fail_count++;
                        Log::error('为工单(ID:'.$stageId.', AID:'.$current_aid.')创建预约单失败: ' . ($result['msg'] ?? '未知错误'));
                    }
                    // 可以加个延时避免请求过于频繁
                    usleep(100000); // 100ms
                }
            }
        }
        Log::info('自动创建周期服务预约单任务结束，成功创建: ' . $count . ' 个，失败: ' . $fail_count . ' 个。');
    }
    
    /**
     * 供 ApiAuto 调用的 createYuyueOrderFromStage 版本
     * （如果 createYuyueOrderFromStage 是 protected，需要这样包装一下）
     */
    public function createYuyueOrderFromStageForAuto($stageId)
    {
        // 这里直接调用 PeriodicServiceOrder 中的核心方法
        // 需要确保 ApiAuto 可以访问到这个方法，可能需要将核心方法改为 public 或通过其他方式调用
         try {
            $controller = new \app\controller\PeriodicServiceOrder(); 
            // 如果 createYuyueOrderFromStage 是 protected，需要改成 public 或用其他方式调用
            // 假设 createYuyueOrderFromStage 已经是 public 或可以访问
             return $controller->createYuyueOrderFromStage($stageId);
        } catch (\Throwable $th) {
            Log::error('调用 createYuyueOrderFromStage 失败: ' . $th->getMessage());
            return ['code' => 1, 'msg' => '调用创建逻辑失败'];
        }
    }
    
    /**
     * 自动发放团队业绩奖励
     * 支持分层递减算法和传统算法
     */
    public function run_tuandui()
    {
        // 2025-01-03 22:55:53,565-INFO-[ApiAuto][run_tuandui_001] 开始执行团队奖励自动发放
        Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][run_tuandui_001] 开始执行团队奖励自动发放", 'info');
        
        $token = input('param.token');
        $aid = input('param.aid/d');
        $activity_id = input('param.activity_id/d');
        
        // 2025-01-03 22:55:53,566-INFO-[ApiAuto][run_tuandui_002] 参数验证，token:{$token}, aid:{$aid}, activity_id:{$activity_id}
        Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][run_tuandui_002] 参数验证，token:{$token}, aid:{$aid}, activity_id:{$activity_id}", 'info');
        
        if(empty($token) || empty($aid) || empty($activity_id)) {
            // 2025-01-03 22:55:53,567-ERROR-[ApiAuto][run_tuandui_003] 参数不完整
            Log::write(date('Y-m-d H:i:s')."-ERROR-[ApiAuto][run_tuandui_003] 参数不完整", 'error');
            die('参数不完整');
        }
        
        // 验证token和获取活动信息
        $activity = Db::name('tuandui_list')
            ->where('aid', $aid)
            ->where('id', $activity_id)
            ->where('auto_token', $token)
            ->find();
            
        if(!$activity) {
            // 2025-01-03 22:55:53,568-ERROR-[ApiAuto][run_tuandui_004] 活动不存在或token无效
            Log::write(date('Y-m-d H:i:s')."-ERROR-[ApiAuto][run_tuandui_004] 活动不存在或token无效", 'error');
            die('活动不存在或token无效');
        }
        
        // 检查活动状态
        if($activity['status'] != 1) {
            // 2025-01-03 22:55:53,569-INFO-[ApiAuto][run_tuandui_005] 活动未启用，状态：{$activity['status']}
            Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][run_tuandui_005] 活动未启用，状态：{$activity['status']}", 'info');
            die('活动未启用');
        }
        
        // 检查是否为自动发放模式
        if($activity['claim_mode'] != 2) {
            // 2025-01-03 22:55:53,570-INFO-[ApiAuto][run_tuandui_006] 非自动发放模式，claim_mode：{$activity['claim_mode']}
            Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][run_tuandui_006] 非自动发放模式，claim_mode：{$activity['claim_mode']}", 'info');
            die('非自动发放模式');
        }
        
        // 根据extreme_value_switch确定算法类型
        $algorithm_type = ($activity['extreme_value_switch'] == 1) ? 'layered_reduction' : 'standard';
        
        // 2025-01-03 22:55:53,571-INFO-[ApiAuto][run_tuandui_007] 选择算法类型：{$algorithm_type}，extreme_value_switch：{$activity['extreme_value_switch']}
        Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][run_tuandui_007] 选择算法类型：{$algorithm_type}，extreme_value_switch：{$activity['extreme_value_switch']}", 'info');
        
        // 获取符合条件的团队长列表
        $level_ids = explode(',', $activity['level_ids']);
        $leaders = Db::name('member')
            ->where('aid', $aid)
            ->where('level', 'in', $level_ids)
            ->where('status', 1)
            ->field('id,nickname,level')
            ->select()
            ->toArray();
            
        if(empty($leaders)) {
            // 2025-01-03 22:55:53,572-INFO-[ApiAuto][run_tuandui_008] 没有符合条件的团队长
            Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][run_tuandui_008] 没有符合条件的团队长", 'info');
            die('没有符合条件的团队长');
        }
        
        // 2025-01-03 22:55:53,573-INFO-[ApiAuto][run_tuandui_009] 找到{count($leaders)}个符合条件的团队长
        Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][run_tuandui_009] 找到".count($leaders)."个符合条件的团队长", 'info');
        
        $success_count = 0;
        $fail_count = 0;
        
        // 开始事务
        Db::startTrans();
        try {
            foreach($leaders as $leader) {
                // 2025-01-03 22:55:53,574-INFO-[ApiAuto][run_tuandui_010] 处理团队长：{$leader['nickname']}(ID:{$leader['id']})
                Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][run_tuandui_010] 处理团队长：{$leader['nickname']}(ID:{$leader['id']})", 'info');
                
                // 检查是否已经发放过奖励
                $existing_record = Db::name('tuandui_record')
                    ->where('aid', $aid)
                    ->where('activity_id', $activity_id)
                    ->where('mid', $leader['id'])
                    ->where('status', 'in', [1, 2]) // 已发放或已领取
                    ->find();
                    
                if($existing_record) {
                    // 2025-01-03 22:55:53,575-INFO-[ApiAuto][run_tuandui_011] 团队长{$leader['nickname']}已发放过奖励，跳过
                    Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][run_tuandui_011] 团队长{$leader['nickname']}已发放过奖励，跳过", 'info');
                    continue;
                }
                
                // 调用奖励计算逻辑
                $result = $this->calculateTeamReward($aid, $activity, $leader['id'], $algorithm_type);
                
                if($result['code'] == 0 && $result['data']['can_claim']) {
                    // 自动发放奖励
                    $reward_data = [
                        'aid' => $aid,
                        'activity_id' => $activity_id,
                        'mid' => $leader['id'],
                        'nickname' => $leader['nickname'],
                        'level' => $leader['level'],
                        'team_performance' => $result['data']['team_performance'],
                        'reward_amount' => $result['data']['reward_amount'],
                        'algorithm_type' => $algorithm_type,
                        'calculation_details' => json_encode($result['data']['calculation_details'], JSON_UNESCAPED_UNICODE),
                        'status' => 1, // 已发放
                        'claim_time' => time(),
                        'createtime' => time()
                    ];
                    
                    $record_id = Db::name('tuandui_record')->insertGetId($reward_data);
                    
                    // 增加用户余额
                    Db::name('member')->where('id', $leader['id'])->inc('money', $result['data']['reward_amount']);
                    
                    // 记录资金变动日志
                    $this->addTeamRewardMoneyLog([
                        'mid' => $leader['id'],
                        'money' => $result['data']['reward_amount'],
                        'remark' => "团队业绩奖励自动发放：{$activity['title']}",
                        'record_id' => $record_id
                    ]);
                    
                    $success_count++;
                    
                    // 2025-01-03 22:55:53,576-INFO-[ApiAuto][run_tuandui_012] 成功发放奖励给{$leader['nickname']}，金额：{$result['data']['reward_amount']}
                    Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][run_tuandui_012] 成功发放奖励给{$leader['nickname']}，金额：{$result['data']['reward_amount']}", 'info');
                } else {
                    // 2025-01-03 22:55:53,577-INFO-[ApiAuto][run_tuandui_013] 团队长{$leader['nickname']}不符合奖励条件：{$result['msg']}
                    Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][run_tuandui_013] 团队长{$leader['nickname']}不符合奖励条件：{$result['msg']}", 'info');
                }
            }
            
            Db::commit();
            
            // 2025-01-03 22:55:53,578-INFO-[ApiAuto][run_tuandui_014] 团队奖励自动发放完成，成功：{$success_count}，失败：{$fail_count}
            Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][run_tuandui_014] 团队奖励自动发放完成，成功：{$success_count}，失败：{$fail_count}", 'info');
            
            die("团队奖励自动发放完成，成功发放：{$success_count}个，失败：{$fail_count}个");
            
        } catch (\Exception $e) {
            Db::rollback();
            
            // 2025-01-03 22:55:53,579-ERROR-[ApiAuto][run_tuandui_015] 自动发放失败：{$e->getMessage()}
            Log::write(date('Y-m-d H:i:s')."-ERROR-[ApiAuto][run_tuandui_015] 自动发放失败：{$e->getMessage()}", 'error');
            
            die('自动发放失败：' . $e->getMessage());
        }
    }
    
    /**
     * 计算团队奖励
     * 支持分层递减算法和传统算法
     */
    private function calculateTeamReward($aid, $activity, $leader_id, $algorithm_type)
    {
        // 2025-01-03 22:55:53,580-INFO-[ApiAuto][calculateTeamReward_001] 开始计算团队奖励，leader_id:{$leader_id}，算法：{$algorithm_type}
        Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][calculateTeamReward_001] 开始计算团队奖励，leader_id:{$leader_id}，算法：{$algorithm_type}", 'info');
        
        try {
            // 根据时间范围类型确定统计时间
            $time_range = $this->getTimeRange($activity);
            
            // 获取团队成员
            $team_members = $this->getTeamMembers($aid, $leader_id);
            
            if(empty($team_members)) {
                return ['code' => 1, 'msg' => '没有团队成员', 'data' => []];
            }
            
            // 计算团队业绩
            $team_performance = $this->calculateTeamPerformance($aid, $activity, $team_members, $time_range, $algorithm_type);
            
            // 2025-01-03 22:55:53,581-INFO-[ApiAuto][calculateTeamReward_002] 团队业绩计算完成，总业绩：{$team_performance['total_performance']}
            Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][calculateTeamReward_002] 团队业绩计算完成，总业绩：{$team_performance['total_performance']}", 'info');
            
            // 根据奖励规则计算奖励金额
            $reward_result = $this->calculateRewardAmount($activity, $team_performance['total_performance']);
            
            if($reward_result['can_claim']) {
                // 2025-01-03 22:55:53,582-INFO-[ApiAuto][calculateTeamReward_003] 符合奖励条件，奖励金额：{$reward_result['reward_amount']}
                Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][calculateTeamReward_003] 符合奖励条件，奖励金额：{$reward_result['reward_amount']}", 'info');
            } else {
                // 2025-01-03 22:55:53,583-INFO-[ApiAuto][calculateTeamReward_004] 不符合奖励条件
                Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][calculateTeamReward_004] 不符合奖励条件", 'info');
            }
            
            return [
                'code' => 0,
                'msg' => '计算成功',
                'data' => [
                    'team_performance' => $team_performance['total_performance'],
                    'reward_amount' => $reward_result['reward_amount'],
                    'can_claim' => $reward_result['can_claim'],
                    'calculation_details' => [
                        'algorithm_type' => $algorithm_type,
                        'team_members_count' => count($team_members),
                        'performance_details' => $team_performance,
                        'reward_details' => $reward_result
                    ]
                ]
            ];
            
        } catch (\Exception $e) {
            // 2025-01-03 22:55:53,584-ERROR-[ApiAuto][calculateTeamReward_005] 计算失败：{$e->getMessage()}
            Log::write(date('Y-m-d H:i:s')."-ERROR-[ApiAuto][calculateTeamReward_005] 计算失败：{$e->getMessage()}", 'error');
            
            return ['code' => 1, 'msg' => '计算失败：' . $e->getMessage(), 'data' => []];
        }
    }
    
    /**
     * 获取时间范围
     */
    private function getTimeRange($activity)
    {
        $time_range_type = $activity['time_range_type'];
        
        switch($time_range_type) {
            case 1: // 本月
                $start_time = strtotime(date('Y-m-01 00:00:00'));
                $end_time = strtotime(date('Y-m-t 23:59:59'));
                break;
            case 2: // 上月
                $start_time = strtotime(date('Y-m-01 00:00:00', strtotime('-1 month')));
                $end_time = strtotime(date('Y-m-t 23:59:59', strtotime('-1 month')));
                break;
            case 3: // 本年
                $start_time = strtotime(date('Y-01-01 00:00:00'));
                $end_time = strtotime(date('Y-12-31 23:59:59'));
                break;
            case 4: // 自定义
                $start_time = strtotime($activity['custom_start_date'] . ' 00:00:00');
                $end_time = strtotime($activity['custom_end_date'] . ' 23:59:59');
                break;
            default:
                $start_time = strtotime(date('Y-m-01 00:00:00'));
                $end_time = strtotime(date('Y-m-t 23:59:59'));
        }
        
        return ['start_time' => $start_time, 'end_time' => $end_time];
    }
    
    /**
     * 获取团队成员
     */
    private function getTeamMembers($aid, $leader_id)
    {
        // 获取直接下级
        $direct_members = Db::name('member')
            ->where('aid', $aid)
            ->where('pid', $leader_id)
            ->where('status', 1)
            ->field('id,nickname,level,pid')
            ->select()
            ->toArray();
            
        $all_members = [$leader_id]; // 包含团队长自己
        
        // 递归获取所有下级
        $this->getSubMembers($aid, $direct_members, $all_members);
        
        return array_unique($all_members);
    }
    
    /**
     * 递归获取下级成员
     */
    private function getSubMembers($aid, $members, &$all_members)
    {
        foreach($members as $member) {
            $all_members[] = $member['id'];
            
            // 获取该成员的下级
            $sub_members = Db::name('member')
                ->where('aid', $aid)
                ->where('pid', $member['id'])
                ->where('status', 1)
                ->field('id,nickname,level,pid')
                ->select()
                ->toArray();
                
            if(!empty($sub_members)) {
                $this->getSubMembers($aid, $sub_members, $all_members);
            }
        }
    }
    
    /**
     * 计算团队业绩
     */
    private function calculateTeamPerformance($aid, $activity, $team_members, $time_range, $algorithm_type)
    {
        $product_ids = explode(',', $activity['product_ids']);
        $performance_type = $activity['performance_type'];
        $performance_stat_mode = $activity['performance_stat_mode'] ?? 0; // 新增：业绩统计方式
        
        $total_performance = 0;
        $member_performances = [];
        
        if($algorithm_type == 'layered_reduction') {
            // 分层递减算法：排除直接下级的贡献
            $leader_id = $team_members[0]; // 第一个是团队长
            
            // 获取直接下级
            $direct_subordinates = Db::name('member')
                ->where('aid', $aid)
                ->where('pid', $leader_id)
                ->where('status', 1)
                ->column('id');
                
            // 计算时排除直接下级的业绩
            $calculation_members = array_diff($team_members, $direct_subordinates);
            $calculation_members = array_merge([$leader_id], $calculation_members); // 重新加入团队长
            
        } else {
            // 传统算法：包含所有成员
            $calculation_members = $team_members;
        }
        
        foreach($calculation_members as $member_id) {
            if($performance_type == 1) {
                // 按团队金额，根据统计方式计算
                if($performance_stat_mode == 0) {
                    // 按销售金额统计
                    $performance = Db::name('shop_order')
                        ->where('aid', $aid)
                        ->where('mid', $member_id)
                        ->where('status', 'in', [1, 2, 3])
                        ->where('paytime', 'between', [$time_range['start_time'], $time_range['end_time']])
                        ->sum('totalprice');
                } else if($performance_stat_mode == 1) {
                    // 按销售利润统计 (销售价格 - 成本价)
                    $performance = Db::name('shop_order_goods')->alias('og')
                        ->join('shop_order o', 'o.id = og.orderid')
                        ->where('o.aid', $aid)
                        ->where('o.mid', $member_id)
                        ->where('o.status', 'in', [1, 2, 3])
                        ->where('o.paytime', 'between', [$time_range['start_time'], $time_range['end_time']])
                        ->sum('og.real_totalprice - og.cost_price * og.num');
                } else if($performance_stat_mode == 2) {
                    // 按成本价统计
                    $performance = Db::name('shop_order_goods')->alias('og')
                        ->join('shop_order o', 'o.id = og.orderid')
                        ->where('o.aid', $aid)
                        ->where('o.mid', $member_id)
                        ->where('o.status', 'in', [1, 2, 3])
                        ->where('o.paytime', 'between', [$time_range['start_time'], $time_range['end_time']])
                        ->sum('og.cost_price * og.num');
                } else {
                    // 默认按销售金额统计
                    $performance = Db::name('shop_order')
                        ->where('aid', $aid)
                        ->where('mid', $member_id)
                        ->where('status', 'in', [1, 2, 3])
                        ->where('paytime', 'between', [$time_range['start_time'], $time_range['end_time']])
                        ->sum('totalprice');
                }
                
                // 记录统计模式日志
                \think\facade\Log::write('团队业绩统计 - 成员ID:'.$member_id.'，统计方式:'.$performance_stat_mode.'，业绩值:'.$performance, 'info');
                
            } else {
                // 按指定商品数量统计
                $performance = Db::name('shop_order_goods')->alias('og')
                    ->join('shop_order o', 'o.id = og.orderid')
                    ->where('o.aid', $aid)
                    ->where('o.mid', $member_id)
                    ->where('o.status', 'in', [1, 2, 3])
                    ->where('o.paytime', 'between', [$time_range['start_time'], $time_range['end_time']])
                    ->where('og.proid', 'in', $product_ids)
                    ->sum('og.num');
            }
            
            $member_performances[$member_id] = floatval($performance);
            $total_performance += $member_performances[$member_id];
        }
        
        return [
            'total_performance' => $total_performance,
            'member_performances' => $member_performances,
            'calculation_members' => $calculation_members
        ];
    }
    
    /**
     * 计算奖励金额
     */
    private function calculateRewardAmount($activity, $team_performance)
    {
        $reward_rules = json_decode($activity['reward_rules'], true);
        $reward_type = $activity['reward_type'];
        
        $reward_amount = 0;
        $can_claim = false;
        $matched_rule = null;
        
        // 按业绩从高到低排序规则
        usort($reward_rules, function($a, $b) {
            return $b['achievement'] - $a['achievement'];
        });
        
        foreach($reward_rules as $rule) {
            if($team_performance >= $rule['achievement']) {
                $matched_rule = $rule;
                $can_claim = true;
                
                if($reward_type == 1) {
                    // 比例奖励
                    $reward_amount = $team_performance * $rule['reward_value'] / 100;
                } else {
                    // 固定奖励
                    $reward_amount = $rule['reward_value'];
                }
                break;
            }
        }
        
        return [
            'reward_amount' => $reward_amount,
            'can_claim' => $can_claim,
            'matched_rule' => $matched_rule
        ];
    }
    
    /**
     * 记录团队奖励资金变动日志
     */
    private function addTeamRewardMoneyLog($data)
    {
        $log_data = [
            'aid' => aid,
            'mid' => $data['mid'],
            'money' => $data['money'],
            'type' => 1, // 收入
            'remark' => $data['remark'],
            'createtime' => time()
        ];
        
        Db::name('money_log')->insert($log_data);
        
        // 2025-01-03 22:55:53,585-INFO-[ApiAuto][addTeamRewardMoneyLog_001] 记录资金变动日志，用户ID：{$data['mid']}，金额：{$data['money']}
        Log::write(date('Y-m-d H:i:s')."-INFO-[ApiAuto][addTeamRewardMoneyLog_001] 记录资金变动日志，用户ID：{$data['mid']}，金额：{$data['money']}", 'info');
    }

	/**
	 * 商家入驻奖励定时任务
	 * 每天执行一次，检查商家入驻天数并发放奖励
	 */
	public function sendBusinessReward()
	{
		$aid = input('param.aid');
		if(!$aid) {
			die('请传入aid');
		}
		
		// 记录任务开始日志
		\think\facade\Log::write('【商家入驻奖励】开始执行，aid='.$aid, 'info');
		
		// 获取系统设置
		$adminSet = Db::name('admin_set')->where('aid', $aid)->find();
		if(!$adminSet || $adminSet['business_reward_enabled'] != 1) {
			\think\facade\Log::write('【商家入驻奖励】功能未开启，aid='.$aid, 'info');
			die('商家入驻奖励功能未开启');
		}
		
		// 获取奖励设置参数
		$rewardDays = intval($adminSet['business_reward_days'] ?? 7); // 奖励天数，默认7天
		$rewardAmount = floatval($adminSet['business_reward_amount'] ?? 100.00); // 奖励金额，默认100元
		$rewardType = intval($adminSet['business_reward_type'] ?? 1); // 奖励类型，默认1现金
		$inviterRate = floatval($adminSet['business_reward_inviter_rate'] ?? 100.00); // 邀请人奖励比例，默认100%
		$rewardNotice = $adminSet['business_reward_notice'] ?? '恭喜您获得商家入驻奖励！'; // 奖励说明
		
		\think\facade\Log::write('【商家入驻奖励】设置参数: 奖励天数='.$rewardDays.', 奖励金额='.$rewardAmount.', 奖励类型='.$rewardType.', 邀请人比例='.$inviterRate.'%', 'info');
		
		// 计算目标日期：当前时间减去奖励天数
		$targetTime = time() - ($rewardDays * 24 * 3600);
		$targetDate = date('Y-m-d H:i:s', $targetTime);
		
		// 查找符合条件的商家：
		// 1. 状态为1（审核通过）
		// 2. 审核通过时间在目标时间之前
		// 3. 奖励状态为0（未达标）或1（待发放）
		$businesses = Db::name('business')
			->where('aid', $aid)
			->where('status', 1) // 审核通过
			->where('createtime', '<=', $targetTime) // 入驻时间满足要求
			->where('reward_status', 'in', [0, 1]) // 未达标或待发放
			->select()
			->toArray();
			
		\think\facade\Log::write('【商家入驻奖励】找到 '.count($businesses).' 个符合条件的商家，目标时间='.$targetDate, 'info');
		
		$processCount = 0; // 处理计数
		$successCount = 0; // 成功计数
		
		foreach($businesses as $business) {
			$processCount++;
			
			\think\facade\Log::write('【商家入驻奖励】处理商家: bid='.$business['id'].', name='.$business['name'].', mid='.$business['mid'].', 入驻时间='.date('Y-m-d H:i:s', $business['createtime']), 'info');
			
			try {
				// 开启事务
				Db::startTrans();
				
				// 计算邀请人奖励金额
				$inviterAmount = 0;
				if($inviterRate > 0 && $business['inviter_mid'] > 0) {
					$inviterAmount = round($rewardAmount * $inviterRate / 100, 2);
				}
				
				// 更新商家奖励状态为已发放
				Db::name('business')
					->where('id', $business['id'])
					->update([
						'reward_status' => 2, // 已发放
						'reward_check_time' => time(),
						'reward_send_time' => time()
					]);
				
				// 给商家发放奖励
				if($business['mid'] > 0) {
					if($rewardType == 1) {
						// 现金奖励，增加余额
						\app\common\Member::addmoney($aid, $business['mid'], $rewardAmount, '商家入驻奖励-'.$rewardNotice);
						\think\facade\Log::write('【商家入驻奖励】给商家发放现金奖励: mid='.$business['mid'].', 金额='.$rewardAmount, 'info');
					} else {
						// 积分奖励，增加积分
						\app\common\Member::addscore($aid, $business['mid'], $rewardAmount, '商家入驻奖励-'.$rewardNotice);
						\think\facade\Log::write('【商家入驻奖励】给商家发放积分奖励: mid='.$business['mid'].', 积分='.$rewardAmount, 'info');
					}
				}
				
				// 给邀请人发放奖励
				if($inviterAmount > 0 && $business['inviter_mid'] > 0) {
					if($rewardType == 1) {
						// 现金奖励，增加余额
						\app\common\Member::addmoney($aid, $business['inviter_mid'], $inviterAmount, '推荐商家入驻奖励-'.$business['name']);
						\think\facade\Log::write('【商家入驻奖励】给邀请人发放现金奖励: mid='.$business['inviter_mid'].', 金额='.$inviterAmount, 'info');
					} else {
						// 积分奖励，增加积分
						\app\common\Member::addscore($aid, $business['inviter_mid'], $inviterAmount, '推荐商家入驻奖励-'.$business['name']);
						\think\facade\Log::write('【商家入驻奖励】给邀请人发放积分奖励: mid='.$business['inviter_mid'].', 积分='.$inviterAmount, 'info');
					}
				}
				
				// 插入奖励记录
				$rewardLogData = [
					'aid' => $aid,
					'bid' => $business['id'],
					'mid' => $business['mid'],
					'inviter_mid' => $business['inviter_mid'] ?? 0,
					'reward_type' => $rewardType,
					'reward_amount' => $rewardAmount,
					'inviter_amount' => $inviterAmount,
					'check_days' => $rewardDays,
					'status' => 1, // 已发放
					'business_name' => $business['name'],
					'business_createtime' => $business['createtime'],
					'send_time' => time(),
					'remark' => $rewardNotice,
					'createtime' => time()
				];
				
				Db::name('business_reward_log')->insert($rewardLogData);
				
				// 提交事务
				Db::commit();
				$successCount++;
				
				\think\facade\Log::write('【商家入驻奖励】成功发放奖励: bid='.$business['id'].', 商家奖励='.$rewardAmount.', 邀请人奖励='.$inviterAmount, 'info');
				
			} catch (\Exception $e) {
				// 回滚事务
				Db::rollback();
				\think\facade\Log::write('【商家入驻奖励】发放失败: bid='.$business['id'].', 错误='.$e->getMessage(), 'error');
			}
		}
		
		\think\facade\Log::write('【商家入驻奖励】任务完成，处理了 '.$processCount.' 个商家，成功发放 '.$successCount.' 个', 'info');

		die('已完成，处理了 '.$processCount.' 个商家，成功发放 '.$successCount.' 个奖励');
	}

	/**
	 * 处理用户升级队列
	 * 每分钟执行一次：星号/1 * * * * curl https://域名/?s=/ApiAuto/processUplvQueue/aid/应用ID
	 */
	public function processUplvQueue() {
		$aid = input('param.aid');
		if(!$aid || !is_numeric($aid)) {
			die('aid参数错误');
		}

		$processCount = 0;
		$successCount = 0;
		$errorCount = 0;

		\think\facade\Log::write('【用户升级队列】开始处理队列任务 - AID:'.$aid);

		// 获取指定应用的配置
		$admin_set = Db::name('admin_set')->where('aid', $aid)->where('uplv_async_enabled', 1)->find();

		if($admin_set) {
			$batch_size = $admin_set['uplv_async_batch_size'] ?? 10;
			$max_retry = $admin_set['uplv_async_max_retry'] ?? 3;

			// 获取待处理的队列项目
			$queue_items = Db::name('member_uplv_queue')
				->where('aid', $aid)
				->where('status', 0)
				->where('retry_count', '<', $max_retry)
				->order('priority desc, created_time asc')
				->limit($batch_size)
				->select()
				->toArray();

			foreach($queue_items as $item) {
				$processCount++;

				try {
					// 标记为处理中
					Db::name('member_uplv_queue')->where('id', $item['id'])->update([
						'status' => 1,
						'updated_time' => date('Y-m-d H:i:s')
					]);

					\think\facade\Log::write('【用户升级队列】开始处理 - 队列ID:'.$item['id'].' 用户ID:'.$item['mid'].' 团队人数:'.$item['team_count']);

					// 执行升级
					$start_time = microtime(true);
					\app\common\Member::uplv_force($item['aid'], $item['mid'], $item['orderid'], $item['levelid']);
					$process_time = round((microtime(true) - $start_time) * 1000, 2);

					// 标记为完成
					Db::name('member_uplv_queue')->where('id', $item['id'])->update([
						'status' => 2,
						'processed_time' => date('Y-m-d H:i:s'),
						'updated_time' => date('Y-m-d H:i:s'),
						'error_msg' => null
					]);

					$successCount++;
					\think\facade\Log::write('【用户升级队列】处理成功 - 队列ID:'.$item['id'].' 用户ID:'.$item['mid'].' 耗时:'.$process_time.'ms');

				} catch(\Exception $e) {
					$errorCount++;
					$retry_count = $item['retry_count'] + 1;
					$status = ($retry_count >= $max_retry) ? 3 : 0; // 超过最大重试次数标记为失败

					// 处理失败，增加重试次数
					Db::name('member_uplv_queue')->where('id', $item['id'])->update([
						'status' => $status,
						'retry_count' => $retry_count,
						'error_msg' => $e->getMessage(),
						'updated_time' => date('Y-m-d H:i:s')
					]);

					\think\facade\Log::write('【用户升级队列】处理失败 - 队列ID:'.$item['id'].' 用户ID:'.$item['mid'].' 重试次数:'.$retry_count.' 错误:'.$e->getMessage());
				}
			}
		} else {
			\think\facade\Log::write('【用户升级队列】应用ID:'.$aid.' 未开启异步升级或配置不存在');
			die('应用ID:'.$aid.' 未开启异步升级或配置不存在');
		}

		\think\facade\Log::write('【用户升级队列】任务完成，处理了 '.$processCount.' 个任务，成功 '.$successCount.' 个，失败 '.$errorCount.' 个');

		die('已完成，处理了 '.$processCount.' 个升级任务，成功 '.$successCount.' 个，失败 '.$errorCount.' 个');
	}

	/**
	 * 梦想启蒙自动处理任务
	 * 处理待生成的梦想启蒙记录
	 */
	public function processDreamInspiration()
	{
		$aid = input('param.aid');
		if(!$aid) {
			die('请传入aid');
		}

		// 检查梦想启蒙功能是否开启
		$setting = Db::name('dream_inspiration_settings')->where(['aid'=>$aid,'is_enabled'=>1])->find();
		if(!$setting) {
			\think\facade\Log::write('【梦想启蒙自动处理】应用ID:'.$aid.' 梦想启蒙功能未开启');
			die('应用ID:'.$aid.' 梦想启蒙功能未开启');
		}

		// 获取待处理的记录（状态为0且创建时间在5分钟内的记录）
		$where = [
			['aid', '=', $aid],
			['status', '=', 0],
			['create_time', '>', time() - 300] // 5分钟内的记录
		];

		$records = Db::name('dream_inspiration_records')
			->where($where)
			->limit(10) // 每次处理10条
			->select()
			->toArray();

		if(empty($records)) {
			\think\facade\Log::write('【梦想启蒙自动处理】应用ID:'.$aid.' 没有待处理的记录');
			die('应用ID:'.$aid.' 没有待处理的记录');
		}

		$processCount = 0;
		$successCount = 0;
		$errorCount = 0;

		foreach($records as $record) {
			$processCount++;

			try {
				// 调用工作流生成图片
				$apiCoze = new \app\controller\ApiCoze();
				$result = $apiCoze->runWorkflowInternal($record['workflow_id'], ['prompt' => $record['prompt_text']]);

				if($result['code'] == 1 && isset($result['data']['output'])) {
					// 解析返回结果，提取图片URL
					$output = $result['data']['output'];
					$imageUrl = '';

					// 根据实际返回格式解析图片URL
					if(is_string($output)) {
						// 如果是字符串，尝试解析JSON
						$outputData = json_decode($output, true);
						if($outputData && isset($outputData['image_url'])) {
							$imageUrl = $outputData['image_url'];
						} elseif(filter_var($output, FILTER_VALIDATE_URL)) {
							// 如果直接是URL
							$imageUrl = $output;
						}
					} elseif(is_array($output) && isset($output['image_url'])) {
						$imageUrl = $output['image_url'];
					}

					// 更新记录状态
					$updateData = [
						'result_image' => $imageUrl,
						'workflow_result' => json_encode($result['data'], JSON_UNESCAPED_UNICODE),
						'status' => 1, // 已生成
						'update_time' => time()
					];

					Db::name('dream_inspiration_records')->where(['id'=>$record['id']])->update($updateData);
					$successCount++;

					\think\facade\Log::write('【梦想启蒙自动处理】记录ID:'.$record['id'].' 处理成功，生成图片：'.$imageUrl);

				} else {
					// 工作流执行失败
					$errorMsg = $result['msg'] ?? '工作流执行失败';
					Db::name('dream_inspiration_records')->where(['id'=>$record['id']])->update([
						'error_msg' => $errorMsg,
						'update_time' => time()
					]);
					$errorCount++;

					\think\facade\Log::write('【梦想启蒙自动处理】记录ID:'.$record['id'].' 处理失败：'.$errorMsg);
				}

			} catch (\Exception $e) {
				// 异常处理
				$errorMsg = $e->getMessage();
				Db::name('dream_inspiration_records')->where(['id'=>$record['id']])->update([
					'error_msg' => $errorMsg,
					'update_time' => time()
				]);
				$errorCount++;

				\think\facade\Log::write('【梦想启蒙自动处理】记录ID:'.$record['id'].' 处理异常：'.$errorMsg);
			}
		}

		\think\facade\Log::write('【梦想启蒙自动处理】任务完成，处理了 '.$processCount.' 个任务，成功 '.$successCount.' 个，失败 '.$errorCount.' 个');

		die('已完成，处理了 '.$processCount.' 个梦想启蒙任务，成功 '.$successCount.' 个，失败 '.$errorCount.' 个');
	}
}
