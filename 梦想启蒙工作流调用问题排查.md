# 梦想启蒙工作流调用问题排查

## 🔍 问题描述

用户反映：上传图片开始转化，但没有看到请求工作流接口。

## 🚨 **问题根本原因已找到并修复**

**主要问题**：前端使用了错误的API调用方式 `this.$http.post()`，导致 `Cannot read property 'post' of undefined` 错误，API根本没有被调用。

**解决方案**：将所有API调用改为项目标准的 `app.post()` 方法。

## 🛠️ 已完成的修复

### 0. **🔥 前端API调用方式修复（关键问题）**

#### 问题：
- 前端使用了 `this.$http.post()` 方法，但项目中没有配置这个HTTP库
- 导致 `Cannot read property 'post' of undefined` 错误
- API调用根本没有执行，所以看不到工作流接口请求

#### 修复：
```javascript
// 修复前（错误）：
this.$http.post('ApiDreamInspiration/generateImage', requestData).then(res => {
    // ...
});

// 修复后（正确）：
const app = getApp();
app.post('ApiDreamInspiration/generateImage', requestData, (res) => {
    // 成功回调
}, (error) => {
    // 错误回调
});
```

### 1. **参数传递问题修复**

#### 问题：
- 前端传递了 `image_url` 参数，但后端没有接收
- 工作流调用时只传递了 `prompt` 参数，缺少图片URL

#### 修复：
```php
// 接收前端传递的图片URL参数
$imageUrl = input('image_url', '', 'trim'); // 用户上传的图片URL

// 验证图片URL
if(empty($imageUrl)){
    return json(['code'=>0,'msg'=>'请上传您的照片']);
}

// 保存到数据库记录
$recordData = [
    // ... 其他字段
    'user_image_url' => $imageUrl, // 用户上传的图片URL
    // ... 其他字段
];

// 调用工作流时传递完整参数
$this->executeWorkflow($recordId, $setting['workflow_id'], $promptText, $imageUrl);
```

### 2. **工作流参数完善**

#### 修复前：
```php
// 只传递提示词
$result = $apiCoze->runWorkflowInternal($workflowId, ['prompt' => $promptText]);
```

#### 修复后：
```php
// 传递完整参数
$parameters = [
    'prompt' => $promptText,
    'image_url' => $imageUrl,
    'user_image' => $imageUrl // 兼容不同的参数名
];
$result = $apiCoze->runWorkflowInternal($workflowId, $parameters);
```

### 3. **数据库表结构更新**

添加了 `user_image_url` 字段到 `ddwx_dream_inspiration_records` 表：

```sql
ALTER TABLE `ddwx_dream_inspiration_records` 
ADD COLUMN `user_image_url` varchar(500) NOT NULL DEFAULT '' COMMENT '用户上传的图片URL' 
AFTER `dream_content`;
```

### 4. **调试日志增强**

添加了详细的调试日志来跟踪工作流调用过程：

```php
// 记录调试日志
\app\common\System::plog('梦想启蒙开始调用工作流', [
    'record_id' => $recordId,
    'workflow_id' => $workflowId,
    'image_url' => $imageUrl,
    'prompt_length' => strlen($promptText)
]);

// 记录参数日志
\app\common\System::plog('梦想启蒙工作流参数', $parameters);

// 记录调用结果
\app\common\System::plog('梦想启蒙工作流调用结果', [
    'result_code' => $result['code'] ?? 'unknown',
    'result_msg' => $result['msg'] ?? 'no message',
    'has_data' => isset($result['data'])
]);
```

## 🔧 排查步骤

### 1. **检查前端调用**
确认前端是否正确传递了 `image_url` 参数：

```javascript
const requestData = {
    gender: userGender,
    dream_content: dreamContent,
    image_url: this.capturedImageUrl // 通过统一上传接口获取的图片URL
};

this.$http.post('ApiDreamInspiration/generateImage', requestData)
```

### 2. **检查后端接收**
确认后端是否正确接收了参数：

```php
$imageUrl = input('image_url', '', 'trim'); // 用户上传的图片URL

if(empty($imageUrl)){
    return json(['code'=>0,'msg'=>'请上传您的照片']);
}
```

### 3. **检查工作流配置**
确认后台是否正确配置了工作流：

1. 进入后台 "扩展 → 扣子API" 检查API配置
2. 进入 "扩展 → 梦想启蒙 → 启蒙设置" 检查工作流配置
3. 确认工作流ID是否正确

### 4. **检查工作流调用**
查看操作日志中的工作流调用记录：

- 梦想启蒙开始调用工作流
- 梦想启蒙工作流参数
- 梦想启蒙工作流调用结果

### 5. **检查数据库记录**
查看 `ddwx_dream_inspiration_records` 表中的记录：

```sql
SELECT id, mid, user_image_url, workflow_id, status, error_msg, create_time 
FROM ddwx_dream_inspiration_records 
ORDER BY create_time DESC 
LIMIT 10;
```

## 🚨 常见问题

### 1. **图片URL为空**
- **原因**：前端上传接口返回的URL格式不正确
- **解决**：检查统一上传接口的返回格式

### 2. **工作流ID不存在**
- **原因**：后台配置的工作流ID错误或工作流被删除
- **解决**：重新配置正确的工作流ID

### 3. **Coze API配置错误**
- **原因**：API密钥错误或过期
- **解决**：重新配置正确的API密钥

### 4. **工作流参数不匹配**
- **原因**：工作流期望的参数名与传递的不一致
- **解决**：调整参数名或工作流配置

## 📋 测试清单

### 前端测试：
- [ ] 图片上传功能正常
- [ ] 上传后获取到正确的图片URL
- [ ] API调用传递了正确的参数

### 后端测试：
- [ ] 正确接收前端传递的参数
- [ ] 数据库记录创建成功
- [ ] 工作流调用参数正确
- [ ] 调试日志记录完整

### 工作流测试：
- [ ] 工作流配置正确
- [ ] API密钥有效
- [ ] 工作流能够处理图片和文本参数
- [ ] 返回结果格式正确

## 🔍 调试命令

### 查看最新的操作日志：
```sql
SELECT * FROM ddwx_system_log 
WHERE content LIKE '%梦想启蒙%' 
ORDER BY create_time DESC 
LIMIT 20;
```

### 查看最新的梦想记录：
```sql
SELECT id, mid, nickname, user_image_url, workflow_id, status, error_msg, create_time 
FROM ddwx_dream_inspiration_records 
ORDER BY create_time DESC 
LIMIT 10;
```

### 查看工作流执行日志：
```sql
SELECT * FROM ddwx_coze_workflow_log 
ORDER BY create_time DESC 
LIMIT 10;
```

## 📞 下一步行动

1. **部署更新**：执行更新的数据库脚本和代码
2. **测试验证**：进行完整的端到端测试
3. **日志监控**：观察调试日志输出
4. **问题定位**：根据日志信息定位具体问题
5. **持续优化**：根据测试结果进行进一步优化

## 💡 优化建议

1. **异步处理**：考虑将工作流调用改为真正的异步处理
2. **重试机制**：添加工作流调用失败的重试逻辑
3. **状态通知**：添加实时状态推送给前端
4. **错误处理**：完善各种异常情况的处理
