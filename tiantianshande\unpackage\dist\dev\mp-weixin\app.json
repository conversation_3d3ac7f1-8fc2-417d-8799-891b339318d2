{"pages": ["pages/index/index", "pages/index/main", "pages/index/location", "pages/index/webView", "pages/index/webView3", "pages/index/webView2", "pages/index/reg", "pages/index/login", "pages/index/city", "pages/index/getpwd", "pages/index/bind", "pages/address/address", "pages/address/addressadd", "pages/my/usercenter", "pages/pay/pay"], "subPackages": [{"root": "shopPackage", "pages": ["shop/product", "shop/commentlist", "shop/cart", "shop/prolist", "shop/search", "shop/buy", "shop/mendian", "shop/classify", "shop/classify2", "shop/daigoulist", "shop/daikebuy", "shop/daigoupick", "shop/productcopy", "shop/fastbuy", "shop/fastbuy2", "shop/category1", "shop/category2", "shop/category3", "shop/category4", "shop/product/product"]}, {"root": "activity", "pages": ["commission/index", "commission/posterdengji", "commission/teamchart", "commission/moneylog2", "commission/commissionlog", "commission/ordergoodsinfo", "commission/commissionloghuang", "commission/commissionloglv", "commission/commissionlogxiaofeizhi", "commission/commissionlog<PERSON><PERSON>ian", "commission/order<PERSON><PERSON><PERSON>", "commission/withdraw", "commission/myteam", "commission/myteamnew", "commission/myteamnew2", "commission/myxingjilog", "commission/buy", "commission/myyukucun", "commission/downorder", "commission/poster", "commission/fhlog", "commission/fhorder", "commission/fenhong", "commission/teamfenhong", "commission/areafenhong", "commission/order<PERSON>eji", "commission/myteamline", "commission/mysameline", "commission/commissionrecord", "commission/commissionranking", "scoreshop/index", "scoreshop/prolist", "scoreshop/product", "scoreshop/cart", "scoreshop/rank", "scoreshop/buy", "scoreshop/orderlist", "scoreshop/orderdetail", "scoreshop/refund", "collage/index", "collage/product", "collage/team", "collage/buy", "collage/orderlist", "collage/orderdetail", "collage/comment", "collage/commentlist", "collage/refund", "seckill/index", "seckill/product", "seckill/buy", "seckill/orderlist", "seckill/orderdetail", "seckill/comment", "seckill/commentlist", "seckill/refund", "tuangou/prolist", "tuangou/product", "tuangou/buy", "tuang<PERSON>/orderlist", "tuangou/orderdetail", "tuangou/comment", "tuangou/commentlist", "tuangou/refund", "xydzp/index", "xydzp/myprize", "choujiang/logistics", "ggk/index", "ggk/myprize", "luntan/index", "luntan/ltlist", "luntan/detail", "luntan/fatie", "luntan/pinglun", "luntan/fatielog", "luntan/focuslog", "peisong/dating", "peisong/orderlist", "peisong/orderdetail", "peisong/my", "peisong/moneylog", "peisong/withdraw", "peisong/setinfo", "peisongdan/dating", "peison<PERSON><PERSON>/orderlist", "p<PERSON>songdan/orderdetail", "p<PERSON><PERSON><PERSON><PERSON>/my", "peisongdan/moneylog", "p<PERSON><PERSON><PERSON><PERSON>/withdraw", "peisongdan/setinfo", "shortvideo/index", "shortvideo/detail", "shortvideo/uploadvideo", "shortvideo/myupload", "kecheng/list", "kecheng/learninglog", "kecheng/bijixiangqing", "kecheng/wodezhanghu", "kecheng/learningbiji", "kecheng/product", "kecheng/mldetail", "kecheng/orderlist", "kecheng/tiku", "kecheng/complete", "kecheng/recordlog", "kecheng/error", "kecheng/category3", "kecheng/category", "kecheng/search", "luckycollage/classify", "luckycollage/prolist", "luckycollage/product", "luckycollage/team", "luckycollage/buy", "luckycollage/orderlist", "luckycollage/orderdetail", "luckycollage/comment", "luckycollage/commentlist", "luckycollage/refund", "luckycollage/index", "luckycollage/product2", "express/index", "express/mail", "express/addressadd", "express/address", "express/logistics", "express/kddetail", "workorder/index", "workorder/detail", "workorder/record", "yuebao/yuebaolog", "y<PERSON><PERSON>/withdraw"]}, {"root": "pagesExt", "pages": ["order/orderlist", "order/batchInvoice", "order/orderzhangdan", "jifenchi/jingtaiA", "ranking/list", "ranking/listjituan", "jifenchi/jingtaiB", "jifenchi/jingtai<PERSON>ong", "jifenchi/myjifenchi", "paidui/paidui", "paidui/periods", "tuo<PERSON><PERSON><PERSON>/mylist", "tuozhanyuan/apply", "tuozhanyuan/tuozhanteam", "tuozhanyuan/myteam", "tuozhanyuan/tuozhanfeimingxi", "tuozhanyuan/index", "tuozhanyuan/tuozhancrm", "tuozhanyuan/shangjipull", "tuo<PERSON>hanyuan/shangjilist", "tuozhanyuan/shangjidetail", "tuozhanyuan/shangjixiugai", "tuozhanyuan/shagjigenjinjilu", "tuozhanyuan/shiyebutongji", "tuozhanyuan/myshiyebu", "cityagent/index", "cityagent/bind", "cityagent/city-detail", "cityagent/coverage", "cityagent/withdraw", "cityagent/withdrawlog", "cityagent/income", "cityagent/merchant", "cityagent/merchant_detail", "maidan/maidanlog", "maidan/maidandetail", "maidan/pay", "maidanpays/maidanlog", "maidanpays/maidandetail", "maidanpays/pay", "article/artlist", "article/detail", "article/pinglun", "zhuanzhang/zhuanzhang", "zhuanzhang/zhuanzhangjishou", "zhuanzhang/zhuanzhanggxz", "zhuanzhang/zhuanzhuanghjf", "zhuanzhang/heizhuanzhang", "zhuanzhang/heizhuanzhangyue", "zhuanzhang/zhuanjifen", "zhuanzhang/zhuanjifen2", "zhuanzhang/zhuanxiaofeizhi", "zhuanzhang/zhuanxiaofeizhidetail", "zhuanzhang/score_to_commission", "order/refundlist", "order/refundDetail", "order/comment", "order/commentdp", "order/commentps", "order/detail", "order/logistics", "order/refundSelect", "order/refund", "order/invoice", "business/index", "business/xinindex", "business/sales-ranking", "business/shoprecharge", "business/main", "business/clist", "business/blist", "business/dlist", "business/cityblist", "business/apply", "business/applyduochengshi", "business/zhunongapply", "business/commentlist", "business/clist2", "business/maps", "lipin/index", "lipin/prodh", "lipin/dhlog", "lipin2/index", "lipin2/prodh", "lipin2/dhlog", "sign/index", "sign/signrecord", "coupon/record", "coupons/record", "coupons/couponlist", "coupons/couponlist2", "coupons/mycoupon", "coupons/coupondetail", "coupons/dh", "coupons/prodh", "coupons/coupongive", "cycle/product", "cycle/planDetail", "cycle/buy", "cycle/checkDate", "cycle/planList", "cycle/orderList", "cycle/orderDetail", "cycle/planWrite", "cycle/logistics", "cycle/refund", "cycle/comment", "cycle/commentps", "cycle/commentlist", "cycle/prolist", "zuji/prolist", "zuji/pro<PERSON><PERSON>ji", "zuji/buy", "zuji/submitBuy", "yueke/orderlist", "yueke/orderdetail", "yueke/workerlogin", "yueke/workerorderlist", "yueke/workerorderdetail", "mingpian/index", "mingpian/edit", "mingpian/favorite", "mingpian/readlog", "mingpian/favoritelog", "highVoltage/highVoltageApplication", "highVoltage/voltageApplicationForm", "highVoltage/voltageApply", "highVoltage/electricityApply", "kefu/index", "electricityForm/recordList", "electricityForm/recordDetail", "electricityForm/recordReplyList", "levelreward/blist", "levelreward/index", "bpv/index", "tuozhanyuan/withdrawtuozhan", "equity_pool/index", "equity_pool/myEquity", "equity_pool/ranking"]}, {"root": "admin", "pages": ["index/login", "purchase/index", "purchase/detail", "purchase/create", "return/index", "return/detail", "return/selectorder", "return/create", "index/index", "product/headquarters", "index/setpwd", "index/setinfo", "index/recharge", "member/historys", "hexiao/hexiao", "hexiao/record", "finance/index", "finance/commissionlog", "finance/comwithdrawdetail", "finance/comwithdrawlog", "finance/moneylog", "finance/rechargelog", "finance/withdrawdetail", "finance/withdrawlog", "finance/bmoneylog", "finance/bwithdraw", "finance/bwithdrawlog", "finance/txset", "finance/yuebaowithdrawlog", "finance/yuebaowithdrawdetail", "finance/yuebaolog", "finance/mdmoneylog", "finance/mdwithdraw", "finance/mdwithdrawlog", "finance/mdtxset", "finance/waitreceivelog", "finance/balance", "finance/businessRecharge", "finance/businessRechargeLog", "finance/businessRechargeDetail", "finance/balanceTransfer", "kefu/index", "kefu/message", "member/index", "member/commissionlog", "member/detail", "member/history", "order/collageorder", "order/collageorderdetail", "order/cycleorder", "order/cycleorderdetail", "order/cycleplanlist", "order/cycleplandetail", "order/luckycollageorder", "order/luckycollageorderdetail", "order/kanjiaorder", "order/kanjiaorderdetail", "order/seckillorder", "order/seckillorderdetail", "order/yuyueorder", "order/yuyueorderdetail", "order/scoreshoporder", "order/scoreshoporderdetail", "order/shoporder", "order/shoporderdetail", "order/shopRefundOrder", "order/shopRefundOrderDetail", "order/tuangouorder", "order/tuangouorderdetail", "order/maidanlog", "order/maidandetail", "order/yuekeorder", "order/yuekeorderdetail", "workorder/record", "workorder/add", "workorder/formlog", "workorder/formdetail", "workorder/myformdetail", "form/formlog", "form/formdetail", "shortvideo/uploadvideo", "shortvideo/myupload", "product/edit", "product/index", "daihuotuan/edit", "daihuotuan/selectgoods", "daihuotuan/index", "index/businessqr", "restaurant/product/edit", "restaurant/product/index", "restaurant/category/edit", "restaurant/category/index", "restaurant/tableEdit", "restaurant/table", "restaurant/tableWaiter", "restaurant/tableWaiterDetail", "restaurant/tableWaiterPay", "restaurant/tableCategoryEdit", "restaurant/tableCategory", "restaurant/takeawayorder", "restaurant/takeawayorderdetail", "restaurant/shoporder", "restaurant/shoporderdetail", "restaurant/shoporderEdit", "restaurant/bookingorder", "restaurant/bookingorderdetail", "restaurant/depositorder", "restaurant/depositorderdetail", "restaurant/booking", "restaurant/bookingTableList", "restaurant/queue", "restaurant/queueCategory", "restaurant/queueCategoryEdit", "freight/index", "freight/edit", "freight/region-select", "businessposter/index"]}, {"root": "adminExt", "pages": ["index/login", "index/index", "index/setpage", "index/setnotice", "index/setpwd", "index/setinfo", "index/recharge", "hexiao/hexiao", "hexiao/record", "hexiao/recordgroup", "finance/index", "finance/commissionlog", "finance/comwithdrawdetail", "finance/comwithdrawlog", "finance/moneylog", "finance/scorelog", "finance/bscorelog", "finance/rechargelog", "finance/withdrawdetail", "finance/withdrawlog", "finance/bmoneylog", "finance/bwithdraw", "finance/bwithdrawlog", "finance/txset", "finance/yuebaowithdrawlog", "finance/yuebaowithdrawdetail", "finance/yuebaolog", "finance/mdmoneylog", "finance/mdwithdraw", "finance/mdwithdrawlog", "finance/mdtxset", "couponmoney/record", "couponmoney/withdraw", "couponmoney/withdrawlog", "kefu/index", "kefu/message", "member/index", "member/detail", "member/richinfo", "member/history", "member/code", "member/codebuy", "order/collageorder", "order/collageorderdetail", "order/cycleorder", "order/cycleorderdetail", "order/cycleplanlist", "order/cycleplandetail", "order/luckycollageorder", "order/luckycollageorderdetail", "order/kanjiaorder", "order/kanjiaorderdetail", "order/seckillorder", "order/seckillorderdetail", "order/yuyueorder", "order/yuyueorderdetail", "order/scoreshoporder", "order/scoreshoporderdetail", "order/shoporder", "order/shoporderdetail", "order/shopRefundOrder", "order/shopRefundOrderDetail", "order/weightOrderFahuo", "order/tuangouorder", "order/tuangouorderdetail", "order/yuekeorder", "order/yuekeorderdetail", "order/dkorder", "order/dkaddress", "order/dkaddressadd", "order/dkfastbuy", "order/dksearch", "order/maidanlog", "order/addmember", "workorder/category", "workorder/record", "workorder/formlog", "workorder/formdetail", "workorder/myformdetail", "workorder/jindu", "workorder/updatecate", "yuyue/selectworker", "form/formlog", "form/formdetail", "shortvideo/uploadvideo", "shortvideo/myupload", "product/edit", "product/index", "product/category2/index", "product/category2/edit", "index/businessqr", "scoreproduct/edit", "scoreproduct/index", "restaurant/product/edit", "restaurant/product/index", "restaurant/category/edit", "restaurant/category/index", "restaurant/tableEdit", "restaurant/table", "restaurant/tableWaiter", "restaurant/tableWaiterDetail", "restaurant/tableWaiterPay", "restaurant/tableCategoryEdit", "restaurant/tableCategory", "restaurant/takeawayorder", "restaurant/takeawayorderdetail", "restaurant/shoporder", "restaurant/shoporderdetail", "restaurant/shoporderEdit", "restaurant/bookingorder", "restaurant/bookingorderdetail", "restaurant/depositorder", "restaurant/depositorderdetail", "restaurant/booking", "restaurant/bookingTableList", "restaurant/queue", "restaurant/queueCategory", "restaurant/queueCategoryEdit", "health/record", "health/recordlog", "health/result", "yingxiao/queueFree", "product/editstock", "business/index", "order/maidandetail", "mendian/list", "mendian/detail", "mendian/withdrawlog", "mendian/withdrawdetail", "hotel/orderlist", "hotel/orderdetail", "hotel/refundyajin", "hotel/refundyajinDetail", "huodongbaoming/order", "huodongbaoming/orderdetail", "shop/shopstock", "shop/shopstockgoods", "coupon/index", "coupon/edit", "coupon/prolist", "coupon/restaurantList", "coupon/category", "bonuspoolgold/goldlog", "bonuspoolgold/goldwithdraw", "finance/transfermendianmoney", "set/qrcodeShop", "queuefree/queueFreeSet"]}, {"root": "live", "pages": ["live", "list"]}, {"root": "restaurant", "pages": ["takeaway/blist", "takeaway/index", "takeaway/product", "takeaway/commentlist", "takeaway/buy", "takeaway/orderlist", "takeaway/comment", "takeaway/commentdp", "takeaway/commentps", "takeaway/orderdetail", "takeaway/logistics", "takeaway/refund", "shop/index", "shop/search", "shop/product", "shop/commentlist", "shop/buy", "shop/orderlist", "shop/comment", "shop/commentdp", "shop/commentps", "shop/orderdetail", "shop/logistics", "shop/refund", "booking/add", "booking/tableList", "booking/orderlist", "booking/detail", "queue/index", "queue/quhao", "queue/record", "deposit/orderlog", "deposit/orderdetail", "deposit/add"]}, {"root": "pagesExa", "pages": ["kaizhanghao/zhuzhanghao", "danshujiang/index", "tuandui/index", "tuandui/detail", "tuandui/records", "ranking-reward/index", "ranking-reward/rules", "ranking-reward/preview", "ranking-reward/records", "yuefenhong/index", "yuefenhong/detail", "kaizhanghao/regzhuzhanghao", "daike/daikebuy", "daike/buy", "daike/daikepay", "daike/daigoulist", "daike/da<PERSON><PERSON><PERSON>min", "daike/daigoupick", "daike/address/address", "daike/address/addressadd", "dikuai/pigfarm", "dikuai/farm", "dikuai/salerecords", "shoumai/sale", "shoumai/index", "shoumai/voucher", "shoumai/walletsite", "my/setshou<PERSON>an", "my/setavatar", "my/setinvite", "my/setusd", "my/qianyue", "my/withdraw", "my/renz<PERSON><PERSON><PERSON><PERSON><PERSON>", "my/levelinfo", "my/levelup", "my/leveluppay", "my/scorelog", "my/scoreloghuang", "my/favorite", "my/history", "my/paypwd", "my/set", "my/mydata", "my/setpwd", "my/setnickname", "my/editaddress", "my/setweixin", "my/seteducation", "my/setmaritalstatus", "my/setprofession", "my/set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>e", "my/setrealname", "my/setidcard", "my/setidcardimg", "my/setidcardimgwx", "my/setidcardimgzfb", "my/settel", "my/setsex", "my/setbirthday", "my/setaliaccount", "my/setbankinfo", "my/duihuan<PERSON><PERSON><PERSON>", "kecheng/list", "kecheng/mldetail", "kecheng/product", "kecheng/tiku", "kecheng/orderlist", "kecheng/complete", "kecheng/recordlog", "kecheng/error", "kecheng/category3", "tuanzhang/apply", "tuanzhang/blist", "tuanzhang/index", "tuanzhang/buy", "tuanzhang/buy/pay", "tuanzhang/product", "tuanzhangadmin/index/login", "tuanzhangadmin/index/mtuanlist", "tuanzhangadmin/index/mytuanlist", "tuanzhangadmin/index/tuanedit", "tuanzhangadmin/index/index", "tuanzhangadmin/finance/index", "tuanzhangadmin/finance/commissionlog", "tuanzhangadmin/finance/comwithdrawdetail", "tuanzhangadmin/finance/comwithdrawlog", "tuanzhangadmin/finance/moneylog", "tuanzhangadmin/finance/rechargelog", "tuanzhangadmin/finance/withdrawdetail", "tuanzhangadmin/finance/withdrawlog", "tuanzhangadmin/finance/bmoneylog", "tuanzhangadmin/finance/bwithdraw", "tuanzhangadmin/finance/bwithdrawlog", "tuanzhangadmin/finance/txset", "tuanzhangadmin/finance/yuebaowithdrawlog", "tuanzhangadmin/finance/yuebaowithdrawdetail", "tuanzhangadmin/finance/yuebaolog", "tuanzhangadmin/finance/mdmoneylog", "tuanzhangadmin/finance/mdwithdraw", "tuanzhangadmin/finance/mdwithdrawlog", "tuanzhangadmin/finance/mdtxset", "tuanzhangadmin/index/setinfo", "tuanzhangadmin/index/setpwd", "tuanzhangadmin/index/artlistadmin", "tongxunlu/peolist", "tongxunlu/maps", "jianbanbaoming/index", "jianbanbaoming/yuedetail", "jianbanbaoming/baoming", "jianbanbaoming/shuoming", "jianban<PERSON>ming/yuelist", "miaosha/index", "miaosha/product", "miaosha/pay", "miaosha/payshangchuan", "miaosha/lirun", "miaosha/lishi", "mi<PERSON><PERSON>/orderlist", "miaosha/changci", "miaosha/fukuan", "miaosha/pay2", "miaosha/pay3", "miaosha/weituo", "miaosha/detail", "miaosha/detail2", "miaosha/orderlist2", "miaosha/buy", "miaosha/buyweituo", "mi<PERSON><PERSON>/my<PERSON>osha", "miaosha/shenhe", "miaosha/classify2", "caigougongying/index", "caigougongying/main", "caigougongying/fatie", "caigougongying/detail", "caigougongying/ltlist", "caigougongying/pinglun", "caigougongying/focuslog", "caigougongying/fatielog"]}, {"root": "daihuobiji", "pages": ["detail/bijilist", "detail/index", "detail/index2", "detail/selectgoods", "detail/userbiji", "detail/userbijishe", "detail/member/following", "detail/member/follower", "yuyue/yuelist", "yuyue/yuedetail", "detail/fatie", "detail/fabiji", "detail/fatieedit", "detail/newFatie", "detail/pinglun", "detail/fatielog", "kuaituan/ditu", "kuaituan/addtuan", "kuaituan/selectgoods", "kuaituan/addtuanadmin/mtuanlist", "kuaituan/classify", "kuaituan/tuanlist", "kuaituan/tuanzhangtuanlist", "kuaituan/artlist", "kuaituan/detail", "kuaituan/tuanzhangdetail"]}, {"root": "<PERSON><PERSON><PERSON>", "pages": ["index", "search", "partdetails", "company", "resume", "jobMatch", "jobFilter", "myFavorites", "myApply", "working<PERSON>ob", "jobDetail"]}, {"root": "yuyue", "pages": ["worker/index", "worker/promote", "selectpeople", "comments", "calendar", "product", "product2", "prolist", "buy", "buy2", "orderlist", "orderdetail", "logistics", "comment", "commentps", "commentlist", "peolist", "usercalendar", "peolist2", "peodetail", "peodetail2", "j<PERSON><PERSON><PERSON>", "jdorderdetail", "my", "dating", "moneylog", "search", "refund", "setinfo", "withdraw", "login", "setpwd", "appoint", "selectworker", "packageorderlist", "packagelist", "packagedetail", "packagebuy", "packageorderdetail", "apply", "packageappoint", "cycle/productList", "cycle/productDetail", "cycle/buy", "cycle/orderList", "cycle/orderDetail"]}, {"root": "pagesB", "pages": ["login/login", "login/reg", "huo<PERSON><PERSON>ming/prolist", "huodongbaoming/prolist2", "shezhennew/index", "shezhennew/analysis", "shezhennew/report", "huodongbaoming/product", "huo<PERSON>baoming/proorderlist", "huodongbaoming/buy", "huo<PERSON><PERSON><PERSON>/orderlist", "huodongbaoming/orderdetail", "maidan/pay", "maidan/maidanlog", "maidan/maidandetail", "shezhen/guide", "shezhen/camera", "shezhen/photo-upload", "shezhen/analysis", "shezhen/result", "shezhen/history", "shezhen/complete", "diagnosis/face/guide-new", "diagnosis/face/camera-new", "diagnosis/comprehensive/guide-new", "diagnosis/face/index", "diagnosis/face/result", "diagnosis/comprehensive/index", "diagnosis/comprehensive/result", "paidan/activity-list", "paidan/activity-products", "paidan/my-position", "paidan/position-tree", "paidan/config-list", "paidan/reward-records", "paidan/statistics", "shangxiang/index", "shangxiang/myWishes", "shangxiang/wishList", "fenhongdian/fenhongdian", "theater/dingchangrili", "theater/dingchangPayment", "theater/hexiao", "theater/detail", "theater/orderlist", "theater/dingchangOrderDetail", "dingchang/dingchanglist", "dingchang/dingchangdetail", "dingchang/yuelist", "dingchang/yuedetail", "dingchang/dingchangOrder", "dingchang/content", "dingchang/dingchangOrderDetail", "dingchang/dingchang<PERSON>i", "dingchang/dingchangPayment", "dingchang/dingchangToPayment", "dingchang/dingchangIndent", "dingchang/commentdp", "dreamark/index", "dreamark/dialogue", "dreamark/camera", "dreamark/camera-new", "dreamark/voice-chat", "dreamark/ending", "dreamark/test", "coze/index", "coze/chat", "coze/workflow", "coze/workflow-logs", "coze/history"]}, {"root": "hotel", "pages": ["index/index", "index/hotellist", "index/hoteldetails", "index/buy", "index/signature", "order/orderlist", "order/orderdetail", "order/comment", "order/refund", "order/commentlist"]}, {"root": "pagesExb", "pages": ["filem/filemlist", "filem/detail", "filem/pdf-viewer-page", "daxuepage/clist", "daxuepage/blist", "daxuepage/zhuanye", "daxuepage/index", "daxuepage/specialityDetails", "daxuepage/fractionalLine", "daxuepage/fractionalLineList", "daxuepage/schoolBlurb", "daxuepage/articledetail", "kanjia/index", "kanjia/product", "kanjia/join", "kanjia/helplist", "kanjia/buy", "kanjia/orderlist", "kanjia/orderdetail", "kanjia/refund", "coupon/couponlist", "coupon/mycoupon", "coupon/coupondetail", "coupon/coupongive", "toupiao/index", "toupiao/detail", "toupiao/phb", "toupiao/baoming", "toupiao/shuoming", "training/index", "training/detail", "training/pinglun", "message/pinglun", "message/msglist", "message/detail", "yunkucun/prolist", "yunkucun/xiajiorderlist", "liandong/myteamjilu", "form/formlog", "form/formdetail", "money/moneylog", "money/cashcoupon", "money/yuejiechongzhi", "money/withdraw", "money/recharge", "shop/category1", "shop/category2", "shop/category3", "shop/category4", "shop/classify", "shop/prolist", "shop/classify2", "shop/fastbuy", "shop/fastbuy2", "business/index", "order/orderlist", "sign/index", "index/city"]}], "window": {"navigationBarTextStyle": "black", "navigationBarBackgroundColor": "#FFFFFF", "navigationBarTitleText": ""}, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "plugins": {}, "requiredPrivateInfos": ["getLocation", "<PERSON><PERSON><PERSON><PERSON>", "chooseLocation"], "requiredBackgroundModes": ["audio", "backgroundFetch"], "lazyCodeLoading": "requiredComponents", "usingComponents": {"t-index-address": "/components/t-index-address/t-index-address"}}