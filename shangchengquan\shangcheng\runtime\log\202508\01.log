[2025-08-01T11:23:29+08:00][info] 2025-08-01 11:23:29-DEBUG-[ApiDreamInspiration][executeWorkflow] 开始调用工作流: 记录ID=9, 工作流ID=7519352650686890038, 性别=0, 职业=医生
[2025-08-01T11:23:29+08:00][info] 2025-08-01 11:23:29-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流参数: {"BOT_USER_INPUT":"","gender":"女","image_url":"https:\/\/kuaifengimg.azheteng.cn\/upload\/62\/20250801\/df91958b96b747e04b4d192940d24a0b.png","zhiye":"医生"}
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:29-INFO-[Coze][runWorkflow_001] 开始运行工作流，ID: 7519352650686890038, 异步: 是
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:29-INFO-[Coze][sendRequest_002] 准备发送请求: POST https://api.coze.cn/v1/workflow/run
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:29-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 212.05ms
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:29-DEBUG-[Coze][runWorkflow_debug] API响应结构: {"code":1,"msg":"请求成功","data":{"code":0,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533452561836179496&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","execute_id":"7533452561836179496","msg":"Success"}}
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:29-INFO-[Coze][runWorkflow_002] 工作流执行成功，执行ID: 7533452561836179496，模式: 异步
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:30-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流调用结果: code=1, msg=请求成功, has_data=yes
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:30-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流原始返回数据: {"code":0,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533452561836179496&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","execute_id":"7533452561836179496","msg":"Success"}
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:30-DEBUG-[ApiDreamInspiration][executeWorkflow] 异步执行，保存执行ID: 7533452561836179496
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:30-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533452561836179496
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:30-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533452561836179496
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:30-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533452561836179496
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:30-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 140.66ms
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:30-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:30-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"create_time":1754018610,"logid":"202508011123299742CDB04D20E42E2FE0","node_execute_status":[],"token":"0","execute_id":"7533452561836179496","connector_id":"1024","is_output_trimmed":false,"connector_uid":"1599136090822611","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","usage":{"token_count":0,"output_count":0,"input_count":0},"error_code":"","bot_id":"0","update_time":1754018610,"execute_status":"Running","run_mode":2,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533452561836179496&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2"}],"detail":{"logid":"2025080111233094113A74BF7586F16E26"}}}
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:30-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:23:30+08:00][info] 2025-08-01 11:23:30-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=9, 状态=0, 用户ID=6750
[2025-08-01T11:23:33+08:00][info] 2025-08-01 11:23:33-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533452561836179496
[2025-08-01T11:23:33+08:00][info] 2025-08-01 11:23:33-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533452561836179496
[2025-08-01T11:23:33+08:00][info] 2025-08-01 11:23:33-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533452561836179496
[2025-08-01T11:23:33+08:00][info] 2025-08-01 11:23:33-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 233.1ms
[2025-08-01T11:23:33+08:00][info] 2025-08-01 11:23:33-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:23:33+08:00][info] 2025-08-01 11:23:33-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"bot_id":"0","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533452561836179496&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","usage":{"input_count":0,"token_count":0,"output_count":0},"execute_id":"7533452561836179496","update_time":1754018610,"connector_id":"1024","is_output_trimmed":false,"execute_status":"Running","node_execute_status":[],"create_time":1754018610,"connector_uid":"1599136090822611","run_mode":2,"logid":"202508011123299742CDB04D20E42E2FE0","error_code":"","token":"0"}],"detail":{"logid":"202508011123339C84D142EB3438868C41"}}}
[2025-08-01T11:23:33+08:00][info] 2025-08-01 11:23:33-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:23:33+08:00][info] 2025-08-01 11:23:33-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=9, 状态=0, 用户ID=6750
[2025-08-01T11:23:36+08:00][info] 2025-08-01 11:23:36-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533452561836179496
[2025-08-01T11:23:36+08:00][info] 2025-08-01 11:23:36-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533452561836179496
[2025-08-01T11:23:36+08:00][info] 2025-08-01 11:23:36-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533452561836179496
[2025-08-01T11:23:36+08:00][info] 2025-08-01 11:23:36-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 119.45ms
[2025-08-01T11:23:36+08:00][info] 2025-08-01 11:23:36-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:23:36+08:00][info] 2025-08-01 11:23:36-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"detail":{"logid":"202508011123360933ACD5BF308B13924C"},"code":0,"msg":"","data":[{"connector_id":"1024","update_time":1754018610,"token":"0","execute_status":"Running","run_mode":2,"node_execute_status":[],"error_code":"","execute_id":"7533452561836179496","connector_uid":"1599136090822611","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533452561836179496&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","logid":"202508011123299742CDB04D20E42E2FE0","bot_id":"0","create_time":1754018610,"is_output_trimmed":false,"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","usage":{"token_count":0,"output_count":0,"input_count":0}}]}}
[2025-08-01T11:23:36+08:00][info] 2025-08-01 11:23:36-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:23:36+08:00][info] 2025-08-01 11:23:36-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=9, 状态=0, 用户ID=6750
[2025-08-01T11:23:40+08:00][info] 2025-08-01 11:23:40-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533452561836179496
[2025-08-01T11:23:40+08:00][info] 2025-08-01 11:23:40-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533452561836179496
[2025-08-01T11:23:40+08:00][info] 2025-08-01 11:23:40-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533452561836179496
[2025-08-01T11:23:40+08:00][info] 2025-08-01 11:23:40-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 117.04ms
[2025-08-01T11:23:40+08:00][info] 2025-08-01 11:23:40-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:23:40+08:00][info] 2025-08-01 11:23:40-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"detail":{"logid":"20250801112340EC65EF0F6ED6AAF1455F"},"code":0,"msg":"","data":[{"usage":{"token_count":0,"output_count":0,"input_count":0},"bot_id":"0","create_time":1754018610,"update_time":1754018610,"is_output_trimmed":false,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533452561836179496&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","connector_uid":"1599136090822611","logid":"202508011123299742CDB04D20E42E2FE0","node_execute_status":[],"error_code":"","token":"0","run_mode":2,"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","execute_id":"7533452561836179496","connector_id":"1024","execute_status":"Running"}]}}
[2025-08-01T11:23:40+08:00][info] 2025-08-01 11:23:40-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:23:40+08:00][info] 2025-08-01 11:23:40-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=9, 状态=0, 用户ID=6750
[2025-08-01T11:23:43+08:00][info] 2025-08-01 11:23:43-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533452561836179496
[2025-08-01T11:23:43+08:00][info] 2025-08-01 11:23:43-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533452561836179496
[2025-08-01T11:23:43+08:00][info] 2025-08-01 11:23:43-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533452561836179496
[2025-08-01T11:23:43+08:00][info] 2025-08-01 11:23:43-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 109.45ms
[2025-08-01T11:23:43+08:00][info] 2025-08-01 11:23:43-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:23:43+08:00][info] 2025-08-01 11:23:43-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"msg":"","data":[{"execute_id":"7533452561836179496","bot_id":"0","create_time":1754018610,"update_time":1754018610,"token":"0","execute_status":"Running","logid":"202508011123299742CDB04D20E42E2FE0","usage":{"token_count":0,"output_count":0,"input_count":0},"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533452561836179496&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","node_execute_status":[],"error_code":"","connector_uid":"1599136090822611","connector_id":"1024","is_output_trimmed":false,"run_mode":2,"output":"{\"Output\":\"\",\"node_status\":\"{}\"}"}],"detail":{"logid":"2025080111234396D4AA95EE669D80923C"},"code":0}}
[2025-08-01T11:23:43+08:00][info] 2025-08-01 11:23:43-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:23:43+08:00][info] 2025-08-01 11:23:43-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=9, 状态=0, 用户ID=6750
[2025-08-01T11:23:46+08:00][info] 2025-08-01 11:23:46-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533452561836179496
[2025-08-01T11:23:46+08:00][info] 2025-08-01 11:23:46-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533452561836179496
[2025-08-01T11:23:46+08:00][info] 2025-08-01 11:23:46-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533452561836179496
[2025-08-01T11:23:46+08:00][info] 2025-08-01 11:23:46-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 132.83ms
[2025-08-01T11:23:46+08:00][info] 2025-08-01 11:23:46-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:23:46+08:00][info] 2025-08-01 11:23:46-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"bot_id":"0","create_time":1754018610,"connector_uid":"1599136090822611","usage":{"input_count":0,"token_count":0,"output_count":0},"error_code":"","connector_id":"1024","token":"0","execute_id":"7533452561836179496","execute_status":"Running","logid":"202508011123299742CDB04D20E42E2FE0","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533452561836179496&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","node_execute_status":[],"update_time":1754018610,"is_output_trimmed":false,"run_mode":2,"output":"{\"node_status\":\"{}\",\"Output\":\"\"}"}],"detail":{"logid":"20250801112346A6C7A96315F24DE319D9"}}}
[2025-08-01T11:23:46+08:00][info] 2025-08-01 11:23:46-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:23:46+08:00][info] 2025-08-01 11:23:46-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=9, 状态=0, 用户ID=6750
[2025-08-01T11:23:49+08:00][info] 2025-08-01 11:23:49-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533452561836179496
[2025-08-01T11:23:50+08:00][info] 2025-08-01 11:23:49-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533452561836179496
[2025-08-01T11:23:50+08:00][info] 2025-08-01 11:23:49-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533452561836179496
[2025-08-01T11:23:50+08:00][info] 2025-08-01 11:23:49-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 128.42ms
[2025-08-01T11:23:50+08:00][info] 2025-08-01 11:23:49-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:23:50+08:00][info] 2025-08-01 11:23:49-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"execute_id":"7533452561836179496","bot_id":"0","update_time":1754018610,"token":"0","run_mode":2,"connector_id":"1024","create_time":1754018610,"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","node_execute_status":[],"is_output_trimmed":false,"connector_uid":"1599136090822611","logid":"202508011123299742CDB04D20E42E2FE0","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533452561836179496&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","error_code":"","execute_status":"Running","usage":{"token_count":0,"output_count":0,"input_count":0}}],"detail":{"logid":"202508011123498CACB9496AADC6D72408"}}}
[2025-08-01T11:23:50+08:00][info] 2025-08-01 11:23:49-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:23:50+08:00][info] 2025-08-01 11:23:50-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=9, 状态=0, 用户ID=6750
[2025-08-01T11:23:53+08:00][info] 2025-08-01 11:23:53-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533452561836179496
[2025-08-01T11:23:53+08:00][info] 2025-08-01 11:23:53-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533452561836179496
[2025-08-01T11:23:53+08:00][info] 2025-08-01 11:23:53-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533452561836179496
[2025-08-01T11:23:53+08:00][info] 2025-08-01 11:23:53-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 119.76ms
[2025-08-01T11:23:53+08:00][info] 2025-08-01 11:23:53-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:23:53+08:00][info] 2025-08-01 11:23:53-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"connector_id":"1024","update_time":1754018610,"create_time":1754018610,"connector_uid":"1599136090822611","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","execute_id":"7533452561836179496","bot_id":"0","run_mode":2,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533452561836179496&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","usage":{"token_count":0,"output_count":0,"input_count":0},"error_code":"","is_output_trimmed":false,"token":"0","execute_status":"Running","logid":"202508011123299742CDB04D20E42E2FE0","node_execute_status":[]}],"detail":{"logid":"202508011123539CBEBE49CC46F7F55B98"}}}
[2025-08-01T11:23:53+08:00][info] 2025-08-01 11:23:53-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:23:53+08:00][info] 2025-08-01 11:23:53-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=9, 状态=0, 用户ID=6750
[2025-08-01T11:23:56+08:00][info] 2025-08-01 11:23:56-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533452561836179496
[2025-08-01T11:23:56+08:00][info] 2025-08-01 11:23:56-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533452561836179496
[2025-08-01T11:23:56+08:00][info] 2025-08-01 11:23:56-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533452561836179496
[2025-08-01T11:23:56+08:00][info] 2025-08-01 11:23:56-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 137.07ms
[2025-08-01T11:23:56+08:00][info] 2025-08-01 11:23:56-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:23:56+08:00][info] 2025-08-01 11:23:56-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"execute_status":"Running","connector_uid":"1599136090822611","run_mode":2,"execute_id":"7533452561836179496","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533452561836179496&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","usage":{"input_count":0,"token_count":0,"output_count":0},"error_code":"","logid":"202508011123299742CDB04D20E42E2FE0","token":"0","update_time":1754018610,"connector_id":"1024","create_time":1754018610,"is_output_trimmed":false,"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","node_execute_status":[],"bot_id":"0"}],"detail":{"logid":"20250801112356793BCC568A1920A25D35"}}}
[2025-08-01T11:23:56+08:00][info] 2025-08-01 11:23:56-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:23:56+08:00][info] 2025-08-01 11:23:56-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=9, 状态=0, 用户ID=6750
[2025-08-01T11:23:59+08:00][info] 2025-08-01 11:23:59-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533452561836179496
[2025-08-01T11:23:59+08:00][info] 2025-08-01 11:23:59-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533452561836179496
[2025-08-01T11:23:59+08:00][info] 2025-08-01 11:23:59-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533452561836179496
[2025-08-01T11:23:59+08:00][info] 2025-08-01 11:23:59-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 101.66ms
[2025-08-01T11:23:59+08:00][info] 2025-08-01 11:23:59-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:23:59+08:00][info] 2025-08-01 11:23:59-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"run_mode":2,"execute_id":"7533452561836179496","create_time":1754018610,"update_time":1754018610,"token":"0","logid":"202508011123299742CDB04D20E42E2FE0","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533452561836179496&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","error_code":"","connector_id":"1024","execute_status":"Running","connector_uid":"1599136090822611","usage":{"output_count":0,"input_count":0,"token_count":0},"bot_id":"0","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","node_execute_status":[],"is_output_trimmed":false}],"detail":{"logid":"202508011123596AD6A8834BCDA3E065AD"}}}
[2025-08-01T11:23:59+08:00][info] 2025-08-01 11:23:59-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:23:59+08:00][info] 2025-08-01 11:23:59-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=9, 状态=0, 用户ID=6750
[2025-08-01T11:24:03+08:00][info] 2025-08-01 11:24:03-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533452561836179496
[2025-08-01T11:24:03+08:00][info] 2025-08-01 11:24:03-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533452561836179496
[2025-08-01T11:24:03+08:00][info] 2025-08-01 11:24:03-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533452561836179496
[2025-08-01T11:24:03+08:00][info] 2025-08-01 11:24:03-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 142.64ms
[2025-08-01T11:24:03+08:00][info] 2025-08-01 11:24:03-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:24:03+08:00][info] 2025-08-01 11:24:03-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"data":[{"bot_id":"0","is_output_trimmed":false,"connector_uid":"1599136090822611","logid":"202508011123299742CDB04D20E42E2FE0","execute_status":"Running","run_mode":2,"error_code":"","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","node_execute_status":[],"execute_id":"7533452561836179496","connector_id":"1024","create_time":1754018610,"update_time":1754018610,"token":"0","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533452561836179496&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","usage":{"token_count":0,"output_count":0,"input_count":0}}],"detail":{"logid":"202508011124037AF5EFEE2CB7BD154190"},"code":0,"msg":""}}
[2025-08-01T11:24:03+08:00][info] 2025-08-01 11:24:03-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:24:03+08:00][info] 2025-08-01 11:24:03-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=9, 状态=0, 用户ID=6750
[2025-08-01T11:24:06+08:00][info] 2025-08-01 11:24:06-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533452561836179496
[2025-08-01T11:24:06+08:00][info] 2025-08-01 11:24:06-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533452561836179496
[2025-08-01T11:24:06+08:00][info] 2025-08-01 11:24:06-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533452561836179496
[2025-08-01T11:24:06+08:00][info] 2025-08-01 11:24:06-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 128.69ms
[2025-08-01T11:24:06+08:00][info] 2025-08-01 11:24:06-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Success，数据库状态: completed
[2025-08-01T11:24:06+08:00][info] 2025-08-01 11:24:06-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"connector_uid":"1599136090822611","logid":"202508011123299742CDB04D20E42E2FE0","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533452561836179496&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","error_message":"","is_output_trimmed":false,"run_mode":2,"connector_id":"1024","create_time":1754018610,"update_time":1754018644,"token":"949","node_execute_status":[],"error_code":"0","execute_id":"7533452561836179496","bot_id":"0","execute_status":"Success","output":"{\"node_status\":\"{}\",\"Output\":\"{\\\"image\\\":\\\"https:\/\/s.coze.cn\/t\/Ea9m38oLPUg\/\\\"}\"}","usage":{"token_count":949,"output_count":19,"input_count":930}}],"detail":{"logid":"20250801112406D1061ECCCCF3908A7357"}}}
[2025-08-01T11:24:06+08:00][info] 2025-08-01 11:24:06-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流完成，更新状态: 图片URL=https://s.coze.cn/t/Ea9m38oLPUg/, 记录ID=9
[2025-08-01T11:24:06+08:00][info] 2025-08-01 11:24:06-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=9, 状态=1, 用户ID=6750
[2025-08-01T11:28:47+08:00][info] 2025-08-01 11:28:47-DEBUG-[ApiDreamInspiration][executeWorkflow] 开始调用工作流: 记录ID=10, 工作流ID=7519352650686890038, 性别=0, 职业=医生
[2025-08-01T11:28:47+08:00][info] 2025-08-01 11:28:47-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流参数: {"BOT_USER_INPUT":"","gender":"女","image_url":"https:\/\/kuaifengimg.azheteng.cn\/upload\/62\/20250801\/c261ddea43436e897a29cdd588491f44.png","zhiye":"医生"}
[2025-08-01T11:28:47+08:00][info] 2025-08-01 11:28:47-INFO-[Coze][runWorkflow_001] 开始运行工作流，ID: 7519352650686890038, 异步: 是
[2025-08-01T11:28:47+08:00][info] 2025-08-01 11:28:47-INFO-[Coze][sendRequest_002] 准备发送请求: POST https://api.coze.cn/v1/workflow/run
[2025-08-01T11:28:47+08:00][info] 2025-08-01 11:28:47-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 185.31ms
[2025-08-01T11:28:47+08:00][info] 2025-08-01 11:28:47-DEBUG-[Coze][runWorkflow_debug] API响应结构: {"code":1,"msg":"请求成功","data":{"code":0,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533453925015584778&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","execute_id":"7533453925015584778","msg":"Success"}}
[2025-08-01T11:28:47+08:00][info] 2025-08-01 11:28:47-INFO-[Coze][runWorkflow_002] 工作流执行成功，执行ID: 7533453925015584778，模式: 异步
[2025-08-01T11:28:47+08:00][info] 2025-08-01 11:28:47-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流调用结果: code=1, msg=请求成功, has_data=yes
[2025-08-01T11:28:47+08:00][info] 2025-08-01 11:28:47-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流原始返回数据: {"code":0,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533453925015584778&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","execute_id":"7533453925015584778","msg":"Success"}
[2025-08-01T11:28:47+08:00][info] 2025-08-01 11:28:47-DEBUG-[ApiDreamInspiration][executeWorkflow] 异步执行，保存执行ID: 7533453925015584778
[2025-08-01T11:28:47+08:00][info] 2025-08-01 11:28:47-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533453925015584778
[2025-08-01T11:28:48+08:00][info] 2025-08-01 11:28:47-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533453925015584778
[2025-08-01T11:28:48+08:00][info] 2025-08-01 11:28:47-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533453925015584778
[2025-08-01T11:28:48+08:00][info] 2025-08-01 11:28:47-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 260.78ms
[2025-08-01T11:28:48+08:00][info] 2025-08-01 11:28:47-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:28:48+08:00][info] 2025-08-01 11:28:47-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"connector_id":"1024","update_time":1754018928,"run_mode":2,"execute_status":"Running","logid":"************47BA7C5ECDEE24AD98494C","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","node_execute_status":[],"execute_id":"7533453925015584778","create_time":1754018928,"is_output_trimmed":false,"token":"0","connector_uid":"1599136090822611","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533453925015584778&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","error_code":"","bot_id":"0","usage":{"token_count":0,"output_count":0,"input_count":0}}],"detail":{"logid":"************47967EEAB0F88D72F13F92"}}}
[2025-08-01T11:28:48+08:00][info] 2025-08-01 11:28:47-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:28:48+08:00][info] 2025-08-01 11:28:48-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=10, 状态=0, 用户ID=6750
[2025-08-01T11:28:51+08:00][info] 2025-08-01 11:28:51-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533453925015584778
[2025-08-01T11:28:51+08:00][info] 2025-08-01 11:28:51-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533453925015584778
[2025-08-01T11:28:51+08:00][info] 2025-08-01 11:28:51-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533453925015584778
[2025-08-01T11:28:51+08:00][info] 2025-08-01 11:28:51-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 129.45ms
[2025-08-01T11:28:51+08:00][info] 2025-08-01 11:28:51-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:28:51+08:00][info] 2025-08-01 11:28:51-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"data":[{"create_time":1754018928,"is_output_trimmed":false,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533453925015584778&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","usage":{"output_count":0,"input_count":0,"token_count":0},"execute_id":"7533453925015584778","run_mode":2,"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","bot_id":"0","token":"0","error_code":"","update_time":1754018928,"execute_status":"Running","connector_uid":"1599136090822611","logid":"************47BA7C5ECDEE24AD98494C","node_execute_status":[],"connector_id":"1024"}],"detail":{"logid":"************515BA7D03E497E830C844D"},"code":0,"msg":""}}
[2025-08-01T11:28:51+08:00][info] 2025-08-01 11:28:51-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:28:51+08:00][info] 2025-08-01 11:28:51-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=10, 状态=0, 用户ID=6750
[2025-08-01T11:28:54+08:00][info] 2025-08-01 11:28:54-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533453925015584778
[2025-08-01T11:28:54+08:00][info] 2025-08-01 11:28:54-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533453925015584778
[2025-08-01T11:28:54+08:00][info] 2025-08-01 11:28:54-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533453925015584778
[2025-08-01T11:28:54+08:00][info] 2025-08-01 11:28:54-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 98.68ms
[2025-08-01T11:28:54+08:00][info] 2025-08-01 11:28:54-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:28:54+08:00][info] 2025-08-01 11:28:54-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"run_mode":2,"logid":"************47BA7C5ECDEE24AD98494C","node_execute_status":[],"execute_id":"7533453925015584778","update_time":1754018928,"token":"0","execute_status":"Running","connector_uid":"1599136090822611","create_time":1754018928,"is_output_trimmed":false,"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","bot_id":"0","connector_id":"1024","usage":{"input_count":0,"token_count":0,"output_count":0},"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533453925015584778&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","error_code":""}],"detail":{"logid":"************54B87CC55D68131210DDDB"}}}
[2025-08-01T11:28:54+08:00][info] 2025-08-01 11:28:54-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:28:54+08:00][info] 2025-08-01 11:28:54-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=10, 状态=0, 用户ID=6750
[2025-08-01T11:28:57+08:00][info] 2025-08-01 11:28:57-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533453925015584778
[2025-08-01T11:28:57+08:00][info] 2025-08-01 11:28:57-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533453925015584778
[2025-08-01T11:28:57+08:00][info] 2025-08-01 11:28:57-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533453925015584778
[2025-08-01T11:28:57+08:00][info] 2025-08-01 11:28:57-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 118.73ms
[2025-08-01T11:28:57+08:00][info] 2025-08-01 11:28:57-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:28:57+08:00][info] 2025-08-01 11:28:57-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"execute_status":"Running","run_mode":2,"usage":{"token_count":0,"output_count":0,"input_count":0},"create_time":1754018928,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533453925015584778&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","bot_id":"0","is_output_trimmed":false,"token":"0","connector_uid":"1599136090822611","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","node_execute_status":[],"error_code":"","execute_id":"7533453925015584778","connector_id":"1024","update_time":1754018928,"logid":"************47BA7C5ECDEE24AD98494C"}],"detail":{"logid":"************57F03B23269A6A5B21854A"}}}
[2025-08-01T11:28:57+08:00][info] 2025-08-01 11:28:57-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:28:57+08:00][info] 2025-08-01 11:28:57-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=10, 状态=0, 用户ID=6750
[2025-08-01T11:29:00+08:00][info] 2025-08-01 11:29:00-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533453925015584778
[2025-08-01T11:29:01+08:00][info] 2025-08-01 11:29:00-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533453925015584778
[2025-08-01T11:29:01+08:00][info] 2025-08-01 11:29:00-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533453925015584778
[2025-08-01T11:29:01+08:00][info] 2025-08-01 11:29:00-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 112.75ms
[2025-08-01T11:29:01+08:00][info] 2025-08-01 11:29:00-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:29:01+08:00][info] 2025-08-01 11:29:00-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"usage":{"token_count":0,"output_count":0,"input_count":0},"bot_id":"0","update_time":1754018928,"execute_status":"Running","run_mode":2,"node_execute_status":[],"create_time":1754018928,"token":"0","connector_id":"1024","is_output_trimmed":false,"connector_uid":"1599136090822611","logid":"************47BA7C5ECDEE24AD98494C","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533453925015584778&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","execute_id":"7533453925015584778","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","error_code":""}],"detail":{"logid":"20250801112900A08694C2E67F9C0D313E"}}}
[2025-08-01T11:29:01+08:00][info] 2025-08-01 11:29:00-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:29:01+08:00][info] 2025-08-01 11:29:01-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=10, 状态=0, 用户ID=6750
[2025-08-01T11:29:04+08:00][info] 2025-08-01 11:29:04-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533453925015584778
[2025-08-01T11:29:04+08:00][info] 2025-08-01 11:29:04-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533453925015584778
[2025-08-01T11:29:04+08:00][info] 2025-08-01 11:29:04-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533453925015584778
[2025-08-01T11:29:04+08:00][info] 2025-08-01 11:29:04-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 104.4ms
[2025-08-01T11:29:04+08:00][info] 2025-08-01 11:29:04-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:29:04+08:00][info] 2025-08-01 11:29:04-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"detail":{"logid":"20250801112904471BB58481B397E7B17E"},"code":0,"msg":"","data":[{"connector_uid":"1599136090822611","run_mode":2,"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","create_time":1754018928,"is_output_trimmed":false,"execute_status":"Running","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533453925015584778&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","node_execute_status":[],"bot_id":"0","connector_id":"1024","token":"0","logid":"************47BA7C5ECDEE24AD98494C","execute_id":"7533453925015584778","update_time":1754018928,"usage":{"input_count":0,"token_count":0,"output_count":0},"error_code":""}]}}
[2025-08-01T11:29:04+08:00][info] 2025-08-01 11:29:04-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:29:04+08:00][info] 2025-08-01 11:29:04-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=10, 状态=0, 用户ID=6750
[2025-08-01T11:29:07+08:00][info] 2025-08-01 11:29:07-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533453925015584778
[2025-08-01T11:29:07+08:00][info] 2025-08-01 11:29:07-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533453925015584778
[2025-08-01T11:29:07+08:00][info] 2025-08-01 11:29:07-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533453925015584778
[2025-08-01T11:29:07+08:00][info] 2025-08-01 11:29:07-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 106.82ms
[2025-08-01T11:29:07+08:00][info] 2025-08-01 11:29:07-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:29:07+08:00][info] 2025-08-01 11:29:07-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"detail":{"logid":"2025080111290786C77C8E68B00E16311B"},"code":0,"msg":"","data":[{"bot_id":"0","usage":{"token_count":0,"output_count":0,"input_count":0},"create_time":1754018928,"is_output_trimmed":false,"connector_uid":"1599136090822611","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","execute_id":"7533453925015584778","update_time":1754018928,"logid":"************47BA7C5ECDEE24AD98494C","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533453925015584778&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","error_code":"","node_execute_status":[],"connector_id":"1024","token":"0","execute_status":"Running","run_mode":2}]}}
[2025-08-01T11:29:07+08:00][info] 2025-08-01 11:29:07-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:29:07+08:00][info] 2025-08-01 11:29:07-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=10, 状态=0, 用户ID=6750
[2025-08-01T11:29:10+08:00][info] 2025-08-01 11:29:10-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533453925015584778
[2025-08-01T11:29:10+08:00][info] 2025-08-01 11:29:10-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533453925015584778
[2025-08-01T11:29:10+08:00][info] 2025-08-01 11:29:10-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533453925015584778
[2025-08-01T11:29:10+08:00][info] 2025-08-01 11:29:10-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 114.37ms
[2025-08-01T11:29:10+08:00][info] 2025-08-01 11:29:10-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:29:10+08:00][info] 2025-08-01 11:29:10-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"data":[{"bot_id":"0","create_time":1754018928,"run_mode":2,"error_code":"","token":"0","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","node_execute_status":[],"update_time":1754018928,"is_output_trimmed":false,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533453925015584778&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","usage":{"output_count":0,"input_count":0,"token_count":0},"execute_id":"7533453925015584778","connector_id":"1024","execute_status":"Running","connector_uid":"1599136090822611","logid":"************47BA7C5ECDEE24AD98494C"}],"detail":{"logid":"202508011129101800F29D076B4E8812A9"},"code":0,"msg":""}}
[2025-08-01T11:29:10+08:00][info] 2025-08-01 11:29:10-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:29:10+08:00][info] 2025-08-01 11:29:10-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=10, 状态=0, 用户ID=6750
[2025-08-01T11:29:14+08:00][info] 2025-08-01 11:29:14-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533453925015584778
[2025-08-01T11:29:14+08:00][info] 2025-08-01 11:29:14-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533453925015584778
[2025-08-01T11:29:14+08:00][info] 2025-08-01 11:29:14-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533453925015584778
[2025-08-01T11:29:14+08:00][info] 2025-08-01 11:29:14-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 107.44ms
[2025-08-01T11:29:14+08:00][info] 2025-08-01 11:29:14-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:29:14+08:00][info] 2025-08-01 11:29:14-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"msg":"","data":[{"execute_id":"7533453925015584778","bot_id":"0","node_execute_status":[],"usage":{"output_count":0,"input_count":0,"token_count":0},"is_output_trimmed":false,"connector_id":"1024","run_mode":2,"error_code":"","logid":"************47BA7C5ECDEE24AD98494C","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533453925015584778&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","create_time":1754018928,"update_time":1754018928,"token":"0","execute_status":"Running","connector_uid":"1599136090822611"}],"detail":{"logid":"20250801112914B144F874B600C09A3D2C"},"code":0}}
[2025-08-01T11:29:14+08:00][info] 2025-08-01 11:29:14-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:29:14+08:00][info] 2025-08-01 11:29:14-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=10, 状态=0, 用户ID=6750
[2025-08-01T11:29:17+08:00][info] 2025-08-01 11:29:17-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533453925015584778
[2025-08-01T11:29:17+08:00][info] 2025-08-01 11:29:17-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533453925015584778
[2025-08-01T11:29:17+08:00][info] 2025-08-01 11:29:17-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533453925015584778
[2025-08-01T11:29:17+08:00][info] 2025-08-01 11:29:17-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 135.11ms
[2025-08-01T11:29:17+08:00][info] 2025-08-01 11:29:17-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:29:17+08:00][info] 2025-08-01 11:29:17-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"run_mode":2,"update_time":1754018928,"connector_uid":"1599136090822611","logid":"************47BA7C5ECDEE24AD98494C","usage":{"input_count":0,"token_count":0,"output_count":0},"error_code":"","connector_id":"1024","create_time":1754018928,"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","bot_id":"0","is_output_trimmed":false,"execute_status":"Running","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533453925015584778&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","node_execute_status":[],"execute_id":"7533453925015584778","token":"0"}],"detail":{"logid":"20250801112917FFDF0B817D18FBDCF9F3"}}}
[2025-08-01T11:29:17+08:00][info] 2025-08-01 11:29:17-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:29:17+08:00][info] 2025-08-01 11:29:17-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=10, 状态=0, 用户ID=6750
[2025-08-01T11:29:20+08:00][info] 2025-08-01 11:29:20-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533453925015584778
[2025-08-01T11:29:20+08:00][info] 2025-08-01 11:29:20-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533453925015584778
[2025-08-01T11:29:20+08:00][info] 2025-08-01 11:29:20-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533453925015584778
[2025-08-01T11:29:20+08:00][info] 2025-08-01 11:29:20-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 110.29ms
[2025-08-01T11:29:20+08:00][info] 2025-08-01 11:29:20-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:29:20+08:00][info] 2025-08-01 11:29:20-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"bot_id":"0","connector_id":"1024","create_time":1754018928,"node_execute_status":[],"is_output_trimmed":false,"token":"0","run_mode":2,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533453925015584778&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","usage":{"token_count":0,"output_count":0,"input_count":0},"execute_id":"7533453925015584778","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","update_time":1754018928,"execute_status":"Running","connector_uid":"1599136090822611","logid":"************47BA7C5ECDEE24AD98494C","error_code":""}],"detail":{"logid":"202508011129206DCC3A624816607683BE"}}}
[2025-08-01T11:29:20+08:00][info] 2025-08-01 11:29:20-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:29:20+08:00][info] 2025-08-01 11:29:20-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=10, 状态=0, 用户ID=6750
[2025-08-01T11:29:23+08:00][info] 2025-08-01 11:29:23-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533453925015584778
[2025-08-01T11:29:23+08:00][info] 2025-08-01 11:29:23-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533453925015584778
[2025-08-01T11:29:23+08:00][info] 2025-08-01 11:29:23-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533453925015584778
[2025-08-01T11:29:23+08:00][info] 2025-08-01 11:29:23-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 111.37ms
[2025-08-01T11:29:23+08:00][info] 2025-08-01 11:29:23-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Success，数据库状态: completed
[2025-08-01T11:29:23+08:00][info] 2025-08-01 11:29:23-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"detail":{"logid":"2025080111292334B74295F0BCFDD6EF20"},"code":0,"msg":"","data":[{"node_execute_status":[],"error_code":"0","bot_id":"0","connector_id":"1024","create_time":1754018928,"update_time":1754018963,"connector_uid":"1599136090822611","logid":"************47BA7C5ECDEE24AD98494C","execute_id":"7533453925015584778","execute_status":"Success","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533453925015584778&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","token":"949","run_mode":2,"usage":{"token_count":949,"output_count":19,"input_count":930},"is_output_trimmed":false,"output":"{\"node_status\":\"{}\",\"Output\":\"{\\\"image\\\":\\\"https:\/\/s.coze.cn\/t\/Oni5ByU498U\/\\\"}\"}","error_message":""}]}}
[2025-08-01T11:29:23+08:00][info] 2025-08-01 11:29:23-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流完成，更新状态: 图片URL=https://s.coze.cn/t/Oni5ByU498U/, 记录ID=10
[2025-08-01T11:29:23+08:00][info] 2025-08-01 11:29:23-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=10, 状态=1, 用户ID=6750
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-DEBUG-[ApiDreamInspiration][executeWorkflow] 开始调用工作流: 记录ID=11, 工作流ID=7519352650686890038, 性别=0, 职业=医生
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流参数: {"BOT_USER_INPUT":"","gender":"女","image_url":"https:\/\/kuaifengimg.azheteng.cn\/upload\/62\/20250801\/3a1948cf48f5dde79353d3b87e3dcb4e.png","zhiye":"医生"}
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-INFO-[Coze][runWorkflow_001] 开始运行工作流，ID: 7519352650686890038, 异步: 是
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-INFO-[Coze][sendRequest_002] 准备发送请求: POST https://api.coze.cn/v1/workflow/run
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 177.59ms
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-DEBUG-[Coze][runWorkflow_debug] API响应结构: {"code":1,"msg":"请求成功","data":{"code":0,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","execute_id":"7533454************","msg":"Success"}}
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-INFO-[Coze][runWorkflow_002] 工作流执行成功，执行ID: 7533454************，模式: 异步
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流调用结果: code=1, msg=请求成功, has_data=yes
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流原始返回数据: {"code":0,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","execute_id":"7533454************","msg":"Success"}
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-DEBUG-[ApiDreamInspiration][executeWorkflow] 异步执行，保存执行ID: 7533454************
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533454************
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533454************
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533454************
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 109.35ms
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"data":[{"create_time":1754019043,"is_output_trimmed":false,"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","execute_id":"7533454************","bot_id":"0","execute_status":"Running","logid":"20250801113043ED8AD8461C1624E86CF3","node_execute_status":[],"connector_id":"1024","update_time":1754019043,"token":"0","connector_uid":"1599136090822611","run_mode":2,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","usage":{"token_count":0,"output_count":0,"input_count":0},"error_code":""}],"detail":{"logid":"20250801113043FE974F99D4BF12139DE1"},"code":0,"msg":""}}
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:30:43+08:00][info] 2025-08-01 11:30:43-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=11, 状态=0, 用户ID=6750
[2025-08-01T11:30:46+08:00][info] 2025-08-01 11:30:46-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533454************
[2025-08-01T11:30:47+08:00][info] 2025-08-01 11:30:46-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533454************
[2025-08-01T11:30:47+08:00][info] 2025-08-01 11:30:46-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533454************
[2025-08-01T11:30:47+08:00][info] 2025-08-01 11:30:46-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 246.79ms
[2025-08-01T11:30:47+08:00][info] 2025-08-01 11:30:46-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:30:47+08:00][info] 2025-08-01 11:30:46-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"bot_id":"0","update_time":1754019043,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","usage":{"input_count":0,"token_count":0,"output_count":0},"error_code":"","create_time":1754019043,"token":"0","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","execute_id":"7533454************","connector_id":"1024","connector_uid":"1599136090822611","run_mode":2,"logid":"20250801113043ED8AD8461C1624E86CF3","is_output_trimmed":false,"execute_status":"Running","node_execute_status":[]}],"detail":{"logid":"2025080111304685B0D891B300111C2E1C"}}}
[2025-08-01T11:30:47+08:00][info] 2025-08-01 11:30:46-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:30:47+08:00][info] 2025-08-01 11:30:47-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=11, 状态=0, 用户ID=6750
[2025-08-01T11:30:50+08:00][info] 2025-08-01 11:30:50-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533454************
[2025-08-01T11:30:50+08:00][info] 2025-08-01 11:30:50-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533454************
[2025-08-01T11:30:50+08:00][info] 2025-08-01 11:30:50-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533454************
[2025-08-01T11:30:50+08:00][info] 2025-08-01 11:30:50-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 109.99ms
[2025-08-01T11:30:50+08:00][info] 2025-08-01 11:30:50-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:30:50+08:00][info] 2025-08-01 11:30:50-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"connector_id":"1024","logid":"20250801113043ED8AD8461C1624E86CF3","usage":{"token_count":0,"output_count":0,"input_count":0},"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","is_output_trimmed":false,"connector_uid":"1599136090822611","run_mode":2,"execute_status":"Running","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","node_execute_status":[],"error_code":"","execute_id":"7533454************","create_time":1754019043,"token":"0","bot_id":"0","update_time":1754019043}],"detail":{"logid":"2025080111305032E923F04793AB11277C"}}}
[2025-08-01T11:30:50+08:00][info] 2025-08-01 11:30:50-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:30:50+08:00][info] 2025-08-01 11:30:50-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=11, 状态=0, 用户ID=6750
[2025-08-01T11:30:53+08:00][info] 2025-08-01 11:30:53-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533454************
[2025-08-01T11:30:53+08:00][info] 2025-08-01 11:30:53-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533454************
[2025-08-01T11:30:53+08:00][info] 2025-08-01 11:30:53-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533454************
[2025-08-01T11:30:53+08:00][info] 2025-08-01 11:30:53-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 102.93ms
[2025-08-01T11:30:53+08:00][info] 2025-08-01 11:30:53-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:30:53+08:00][info] 2025-08-01 11:30:53-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"execute_id":"7533454************","bot_id":"0","connector_id":"1024","execute_status":"Running","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","is_output_trimmed":false,"usage":{"token_count":0,"output_count":0,"input_count":0},"error_code":"","create_time":1754019043,"update_time":1754019043,"connector_uid":"1599136090822611","logid":"20250801113043ED8AD8461C1624E86CF3","token":"0","run_mode":2,"node_execute_status":[]}],"detail":{"logid":"202508011130536FF27BE7724CD21A3C46"}}}
[2025-08-01T11:30:53+08:00][info] 2025-08-01 11:30:53-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:30:53+08:00][info] 2025-08-01 11:30:53-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=11, 状态=0, 用户ID=6750
[2025-08-01T11:30:56+08:00][info] 2025-08-01 11:30:56-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533454************
[2025-08-01T11:30:56+08:00][info] 2025-08-01 11:30:56-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533454************
[2025-08-01T11:30:56+08:00][info] 2025-08-01 11:30:56-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533454************
[2025-08-01T11:30:56+08:00][info] 2025-08-01 11:30:56-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 100.45ms
[2025-08-01T11:30:56+08:00][info] 2025-08-01 11:30:56-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:30:56+08:00][info] 2025-08-01 11:30:56-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","create_time":1754019043,"is_output_trimmed":false,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","node_execute_status":[],"usage":{"token_count":0,"output_count":0,"input_count":0},"bot_id":"0","error_code":"","connector_id":"1024","update_time":1754019043,"token":"0","execute_status":"Running","connector_uid":"1599136090822611","run_mode":2,"logid":"20250801113043ED8AD8461C1624E86CF3","execute_id":"7533454************"}],"detail":{"logid":"2025080111305645D8F2653FB3BF854A2E"}}}
[2025-08-01T11:30:56+08:00][info] 2025-08-01 11:30:56-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:30:56+08:00][info] 2025-08-01 11:30:56-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=11, 状态=0, 用户ID=6750
[2025-08-01T11:31:01+08:00][info] 2025-08-01 11:31:01-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533454************
[2025-08-01T11:31:02+08:00][info] 2025-08-01 11:31:01-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533454************
[2025-08-01T11:31:02+08:00][info] 2025-08-01 11:31:01-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533454************
[2025-08-01T11:31:02+08:00][info] 2025-08-01 11:31:01-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 105.04ms
[2025-08-01T11:31:02+08:00][info] 2025-08-01 11:31:01-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:31:02+08:00][info] 2025-08-01 11:31:01-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"msg":"","data":[{"is_output_trimmed":false,"execute_status":"Running","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","error_code":"","usage":{"token_count":0,"output_count":0,"input_count":0},"connector_id":"1024","update_time":1754019043,"token":"0","logid":"20250801113043ED8AD8461C1624E86CF3","output":"{\"Output\":\"\",\"node_status\":\"{}\"}","connector_uid":"1599136090822611","node_execute_status":[],"execute_id":"7533454************","bot_id":"0","create_time":1754019043,"run_mode":2}],"detail":{"logid":"20250801113101B0F10CED7C56CD112B76"},"code":0}}
[2025-08-01T11:31:02+08:00][info] 2025-08-01 11:31:01-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:31:02+08:00][info] 2025-08-01 11:31:02-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=11, 状态=0, 用户ID=6750
[2025-08-01T11:31:05+08:00][info] 2025-08-01 11:31:05-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533454************
[2025-08-01T11:31:05+08:00][info] 2025-08-01 11:31:05-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533454************
[2025-08-01T11:31:05+08:00][info] 2025-08-01 11:31:05-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533454************
[2025-08-01T11:31:05+08:00][info] 2025-08-01 11:31:05-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 104.26ms
[2025-08-01T11:31:05+08:00][info] 2025-08-01 11:31:05-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:31:05+08:00][info] 2025-08-01 11:31:05-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"data":[{"is_output_trimmed":false,"token":"0","usage":{"token_count":0,"output_count":0,"input_count":0},"update_time":1754019043,"bot_id":"0","connector_uid":"1599136090822611","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","node_execute_status":[],"execute_id":"7533454************","run_mode":2,"output":"{\"Output\":\"\",\"node_status\":\"{}\"}","error_code":"","connector_id":"1024","execute_status":"Running","logid":"20250801113043ED8AD8461C1624E86CF3","create_time":1754019043}],"detail":{"logid":"20250801113105115D69B57072B10B2F95"},"code":0,"msg":""}}
[2025-08-01T11:31:05+08:00][info] 2025-08-01 11:31:05-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:31:05+08:00][info] 2025-08-01 11:31:05-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=11, 状态=0, 用户ID=6750
[2025-08-01T11:31:08+08:00][info] 2025-08-01 11:31:08-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533454************
[2025-08-01T11:31:08+08:00][info] 2025-08-01 11:31:08-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533454************
[2025-08-01T11:31:08+08:00][info] 2025-08-01 11:31:08-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533454************
[2025-08-01T11:31:08+08:00][info] 2025-08-01 11:31:08-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 111.6ms
[2025-08-01T11:31:08+08:00][info] 2025-08-01 11:31:08-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:31:08+08:00][info] 2025-08-01 11:31:08-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"detail":{"logid":"20250801113108585DFA5F9280BDFC99B9"},"code":0,"msg":"","data":[{"token":"0","node_execute_status":[],"usage":{"token_count":0,"output_count":0,"input_count":0},"bot_id":"0","create_time":1754019043,"update_time":1754019043,"is_output_trimmed":false,"connector_uid":"1599136090822611","logid":"20250801113043ED8AD8461C1624E86CF3","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","error_code":"","execute_id":"7533454************","connector_id":"1024","execute_status":"Running","run_mode":2}]}}
[2025-08-01T11:31:08+08:00][info] 2025-08-01 11:31:08-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:31:08+08:00][info] 2025-08-01 11:31:08-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=11, 状态=0, 用户ID=6750
[2025-08-01T11:31:11+08:00][info] 2025-08-01 11:31:11-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533454************
[2025-08-01T11:31:11+08:00][info] 2025-08-01 11:31:11-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533454************
[2025-08-01T11:31:11+08:00][info] 2025-08-01 11:31:11-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533454************
[2025-08-01T11:31:11+08:00][info] 2025-08-01 11:31:11-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 106.13ms
[2025-08-01T11:31:11+08:00][info] 2025-08-01 11:31:11-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:31:11+08:00][info] 2025-08-01 11:31:11-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"detail":{"logid":"20250801113111F08B3439D7D76616A4C5"},"code":0,"msg":"","data":[{"connector_id":"1024","is_output_trimmed":false,"connector_uid":"1599136090822611","logid":"20250801113043ED8AD8461C1624E86CF3","execute_status":"Running","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","error_code":"","create_time":1754019043,"update_time":1754019043,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","usage":{"token_count":0,"output_count":0,"input_count":0},"execute_id":"7533454************","bot_id":"0","token":"0","run_mode":2,"node_execute_status":[]}]}}
[2025-08-01T11:31:11+08:00][info] 2025-08-01 11:31:11-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:31:11+08:00][info] 2025-08-01 11:31:11-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=11, 状态=0, 用户ID=6750
[2025-08-01T11:31:15+08:00][info] 2025-08-01 11:31:15-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533454************
[2025-08-01T11:31:15+08:00][info] 2025-08-01 11:31:15-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533454************
[2025-08-01T11:31:15+08:00][info] 2025-08-01 11:31:15-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533454************
[2025-08-01T11:31:15+08:00][info] 2025-08-01 11:31:15-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 106.95ms
[2025-08-01T11:31:15+08:00][info] 2025-08-01 11:31:15-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:31:15+08:00][info] 2025-08-01 11:31:15-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"connector_id":"1024","token":"0","run_mode":2,"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","error_code":"","usage":{"input_count":0,"token_count":0,"output_count":0},"execute_id":"7533454************","bot_id":"0","update_time":1754019043,"connector_uid":"1599136090822611","logid":"20250801113043ED8AD8461C1624E86CF3","node_execute_status":[],"create_time":1754019043,"is_output_trimmed":false,"execute_status":"Running","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2"}],"detail":{"logid":"2025080111311578B044E49BFE74E2BDFE"}}}
[2025-08-01T11:31:15+08:00][info] 2025-08-01 11:31:15-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:31:15+08:00][info] 2025-08-01 11:31:15-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=11, 状态=0, 用户ID=6750
[2025-08-01T11:31:18+08:00][info] 2025-08-01 11:31:18-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533454************
[2025-08-01T11:31:18+08:00][info] 2025-08-01 11:31:18-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533454************
[2025-08-01T11:31:18+08:00][info] 2025-08-01 11:31:18-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533454************
[2025-08-01T11:31:18+08:00][info] 2025-08-01 11:31:18-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 104.91ms
[2025-08-01T11:31:18+08:00][info] 2025-08-01 11:31:18-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:31:18+08:00][info] 2025-08-01 11:31:18-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"msg":"","data":[{"logid":"20250801113043ED8AD8461C1624E86CF3","execute_id":"7533454************","update_time":1754019043,"is_output_trimmed":false,"token":"0","create_time":1754019043,"execute_status":"Running","node_execute_status":[],"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","usage":{"token_count":0,"output_count":0,"input_count":0},"error_code":"","bot_id":"0","connector_id":"1024","connector_uid":"1599136090822611","run_mode":2}],"detail":{"logid":"202508011131184BD5005D45D405E2EC42"},"code":0}}
[2025-08-01T11:31:18+08:00][info] 2025-08-01 11:31:18-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:31:18+08:00][info] 2025-08-01 11:31:18-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=11, 状态=0, 用户ID=6750
[2025-08-01T11:31:21+08:00][info] 2025-08-01 11:31:21-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533454************
[2025-08-01T11:31:21+08:00][info] 2025-08-01 11:31:21-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533454************
[2025-08-01T11:31:21+08:00][info] 2025-08-01 11:31:21-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533454************
[2025-08-01T11:31:21+08:00][info] 2025-08-01 11:31:21-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 121.96ms
[2025-08-01T11:31:21+08:00][info] 2025-08-01 11:31:21-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:31:21+08:00][info] 2025-08-01 11:31:21-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"data":[{"execute_id":"7533454************","execute_status":"Running","connector_uid":"1599136090822611","logid":"20250801113043ED8AD8461C1624E86CF3","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","is_output_trimmed":false,"token":"0","run_mode":2,"node_execute_status":[],"error_code":"","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","bot_id":"0","connector_id":"1024","create_time":1754019043,"update_time":1754019043,"usage":{"token_count":0,"output_count":0,"input_count":0}}],"detail":{"logid":"20250801113121795AE91CE8E95015A981"},"code":0,"msg":""}}
[2025-08-01T11:31:21+08:00][info] 2025-08-01 11:31:21-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:31:21+08:00][info] 2025-08-01 11:31:21-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=11, 状态=0, 用户ID=6750
[2025-08-01T11:31:25+08:00][info] 2025-08-01 11:31:25-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533454************
[2025-08-01T11:31:25+08:00][info] 2025-08-01 11:31:25-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533454************
[2025-08-01T11:31:25+08:00][info] 2025-08-01 11:31:25-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533454************
[2025-08-01T11:31:25+08:00][info] 2025-08-01 11:31:25-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 145.76ms
[2025-08-01T11:31:25+08:00][info] 2025-08-01 11:31:25-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:31:25+08:00][info] 2025-08-01 11:31:25-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"is_output_trimmed":false,"execute_status":"Running","node_execute_status":[],"usage":{"token_count":0,"output_count":0,"input_count":0},"execute_id":"7533454************","update_time":1754019043,"logid":"20250801113043ED8AD8461C1624E86CF3","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","error_code":"","create_time":1754019043,"token":"0","connector_uid":"1599136090822611","run_mode":2,"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","bot_id":"0","connector_id":"1024"}],"detail":{"logid":"2025080111312562260A8ADA0E821D051E"}}}
[2025-08-01T11:31:25+08:00][info] 2025-08-01 11:31:25-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:31:25+08:00][info] 2025-08-01 11:31:25-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=11, 状态=0, 用户ID=6750
[2025-08-01T11:31:28+08:00][info] 2025-08-01 11:31:28-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533454************
[2025-08-01T11:31:28+08:00][info] 2025-08-01 11:31:28-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533454************
[2025-08-01T11:31:28+08:00][info] 2025-08-01 11:31:28-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533454************
[2025-08-01T11:31:28+08:00][info] 2025-08-01 11:31:28-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 111.43ms
[2025-08-01T11:31:28+08:00][info] 2025-08-01 11:31:28-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Success，数据库状态: completed
[2025-08-01T11:31:28+08:00][info] 2025-08-01 11:31:28-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"execute_id":"7533454************","update_time":1754019088,"execute_status":"Success","error_code":"0","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533454************&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","error_message":"","token":"949","run_mode":2,"usage":{"token_count":949,"output_count":19,"input_count":930},"logid":"20250801113043ED8AD8461C1624E86CF3","output":"{\"Output\":\"{\\\"image\\\":\\\"https:\/\/s.coze.cn\/t\/ILnx1Ss-4FU\/\\\"}\",\"node_status\":\"{}\"}","connector_id":"1024","create_time":1754019043,"is_output_trimmed":false,"bot_id":"0","connector_uid":"1599136090822611","node_execute_status":[]}],"detail":{"logid":"20250801113128E2BF1C2273F15381AEB8"}}}
[2025-08-01T11:31:28+08:00][info] 2025-08-01 11:31:28-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流完成，更新状态: 图片URL=https://s.coze.cn/t/ILnx1Ss-4FU/, 记录ID=11
[2025-08-01T11:31:28+08:00][info] 2025-08-01 11:31:28-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=11, 状态=1, 用户ID=6750
[2025-08-01T11:52:11+08:00][info] 2025-08-01 11:52:11-DEBUG-[ApiDreamInspiration][executeWorkflow] 开始调用工作流: 记录ID=12, 工作流ID=7519352650686890038, 性别=0, 职业=医生
[2025-08-01T11:52:11+08:00][info] 2025-08-01 11:52:11-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流参数: {"BOT_USER_INPUT":"","gender":"女","image_url":"https:\/\/kuaifengimg.azheteng.cn\/upload\/62\/20250801\/e6a489381e6802a28f5717e498700509.png","zhiye":"医生"}
[2025-08-01T11:52:11+08:00][info] 2025-08-01 11:52:11-INFO-[Coze][runWorkflow_001] 开始运行工作流，ID: 7519352650686890038, 异步: 是
[2025-08-01T11:52:11+08:00][info] 2025-08-01 11:52:11-INFO-[Coze][sendRequest_002] 准备发送请求: POST https://api.coze.cn/v1/workflow/run
[2025-08-01T11:52:11+08:00][info] 2025-08-01 11:52:11-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 490.83ms
[2025-08-01T11:52:11+08:00][info] 2025-08-01 11:52:11-DEBUG-[Coze][runWorkflow_debug] API响应结构: {"code":1,"msg":"请求成功","data":{"code":0,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533459951676047399&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","execute_id":"7533459951676047399","msg":"Success"}}
[2025-08-01T11:52:11+08:00][info] 2025-08-01 11:52:11-INFO-[Coze][runWorkflow_002] 工作流执行成功，执行ID: 7533459951676047399，模式: 异步
[2025-08-01T11:52:11+08:00][info] 2025-08-01 11:52:11-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流调用结果: code=1, msg=请求成功, has_data=yes
[2025-08-01T11:52:11+08:00][info] 2025-08-01 11:52:11-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流原始返回数据: {"code":0,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533459951676047399&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","execute_id":"7533459951676047399","msg":"Success"}
[2025-08-01T11:52:11+08:00][info] 2025-08-01 11:52:11-DEBUG-[ApiDreamInspiration][executeWorkflow] 异步执行，保存执行ID: 7533459951676047399
[2025-08-01T11:52:12+08:00][info] 2025-08-01 11:52:12-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533459951676047399
[2025-08-01T11:52:12+08:00][info] 2025-08-01 11:52:12-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533459951676047399
[2025-08-01T11:52:12+08:00][info] 2025-08-01 11:52:12-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533459951676047399
[2025-08-01T11:52:12+08:00][info] 2025-08-01 11:52:12-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 130.95ms
[2025-08-01T11:52:12+08:00][info] 2025-08-01 11:52:12-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:52:12+08:00][info] 2025-08-01 11:52:12-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"create_time":1754020332,"run_mode":2,"logid":"20250801115211088D5950237D3EE50263","usage":{"token_count":0,"output_count":0,"input_count":0},"connector_id":"1024","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533459951676047399&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","execute_id":"7533459951676047399","bot_id":"0","update_time":1754020332,"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","node_execute_status":[],"is_output_trimmed":false,"token":"0","execute_status":"Running","connector_uid":"1599136090822611","error_code":""}],"detail":{"logid":"20250801115212DA5DCF4BA9ADEAE0D4DF"}}}
[2025-08-01T11:52:12+08:00][info] 2025-08-01 11:52:12-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:52:12+08:00][info] 2025-08-01 11:52:12-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=12, 状态=0, 用户ID=6750
[2025-08-01T11:52:15+08:00][info] 2025-08-01 11:52:15-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533459951676047399
[2025-08-01T11:52:15+08:00][info] 2025-08-01 11:52:15-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533459951676047399
[2025-08-01T11:52:15+08:00][info] 2025-08-01 11:52:15-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533459951676047399
[2025-08-01T11:52:15+08:00][info] 2025-08-01 11:52:15-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 131.55ms
[2025-08-01T11:52:15+08:00][info] 2025-08-01 11:52:15-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:52:15+08:00][info] 2025-08-01 11:52:15-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"msg":"","data":[{"token":"0","update_time":1754020332,"run_mode":2,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533459951676047399&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","usage":{"token_count":0,"output_count":0,"input_count":0},"error_code":"","create_time":1754020332,"is_output_trimmed":false,"execute_status":"Running","execute_id":"7533459951676047399","node_execute_status":[],"bot_id":"0","connector_id":"1024","connector_uid":"1599136090822611","logid":"20250801115211088D5950237D3EE50263","output":"{\"node_status\":\"{}\",\"Output\":\"\"}"}],"detail":{"logid":"202508011152159A322E6072D26BE73549"},"code":0}}
[2025-08-01T11:52:15+08:00][info] 2025-08-01 11:52:15-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:52:15+08:00][info] 2025-08-01 11:52:15-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=12, 状态=0, 用户ID=6750
[2025-08-01T11:52:18+08:00][info] 2025-08-01 11:52:18-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533459951676047399
[2025-08-01T11:52:18+08:00][info] 2025-08-01 11:52:18-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533459951676047399
[2025-08-01T11:52:18+08:00][info] 2025-08-01 11:52:18-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533459951676047399
[2025-08-01T11:52:18+08:00][info] 2025-08-01 11:52:18-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 115.71ms
[2025-08-01T11:52:18+08:00][info] 2025-08-01 11:52:18-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:52:18+08:00][info] 2025-08-01 11:52:18-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"execute_id":"7533459951676047399","update_time":1754020332,"is_output_trimmed":false,"logid":"20250801115211088D5950237D3EE50263","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","node_execute_status":[],"bot_id":"0","create_time":1754020332,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533459951676047399&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","error_code":"","execute_status":"Running","run_mode":2,"usage":{"token_count":0,"output_count":0,"input_count":0},"connector_id":"1024","token":"0","connector_uid":"1599136090822611"}],"detail":{"logid":"20250801115218E2DCECBF90D28A8ED7FC"}}}
[2025-08-01T11:52:18+08:00][info] 2025-08-01 11:52:18-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:52:18+08:00][info] 2025-08-01 11:52:18-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=12, 状态=0, 用户ID=6750
[2025-08-01T11:52:21+08:00][info] 2025-08-01 11:52:21-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533459951676047399
[2025-08-01T11:52:21+08:00][info] 2025-08-01 11:52:21-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533459951676047399
[2025-08-01T11:52:21+08:00][info] 2025-08-01 11:52:21-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533459951676047399
[2025-08-01T11:52:21+08:00][info] 2025-08-01 11:52:21-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 102.71ms
[2025-08-01T11:52:21+08:00][info] 2025-08-01 11:52:21-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:52:21+08:00][info] 2025-08-01 11:52:21-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"detail":{"logid":"20250801115221BFF69EAADC2856A58A23"},"code":0,"msg":"","data":[{"connector_id":"1024","create_time":1754020332,"update_time":1754020332,"usage":{"output_count":0,"input_count":0,"token_count":0},"execute_id":"7533459951676047399","logid":"20250801115211088D5950237D3EE50263","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533459951676047399&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","error_code":"","node_execute_status":[],"bot_id":"0","is_output_trimmed":false,"token":"0","execute_status":"Running","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","connector_uid":"1599136090822611","run_mode":2}]}}
[2025-08-01T11:52:21+08:00][info] 2025-08-01 11:52:21-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:52:21+08:00][info] 2025-08-01 11:52:21-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=12, 状态=0, 用户ID=6750
[2025-08-01T11:52:25+08:00][info] 2025-08-01 11:52:25-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533459951676047399
[2025-08-01T11:52:25+08:00][info] 2025-08-01 11:52:25-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533459951676047399
[2025-08-01T11:52:25+08:00][info] 2025-08-01 11:52:25-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533459951676047399
[2025-08-01T11:52:25+08:00][info] 2025-08-01 11:52:25-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 103.84ms
[2025-08-01T11:52:25+08:00][info] 2025-08-01 11:52:25-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:52:25+08:00][info] 2025-08-01 11:52:25-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"execute_id":"7533459951676047399","create_time":1754020332,"update_time":1754020332,"token":"0","execute_status":"Running","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533459951676047399&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","connector_uid":"1599136090822611","logid":"20250801115211088D5950237D3EE50263","usage":{"token_count":0,"output_count":0,"input_count":0},"error_code":"","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","is_output_trimmed":false,"run_mode":2,"bot_id":"0","connector_id":"1024","node_execute_status":[]}],"detail":{"logid":"2025080111522510D51402689DC196AE00"}}}
[2025-08-01T11:52:25+08:00][info] 2025-08-01 11:52:25-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:52:25+08:00][info] 2025-08-01 11:52:25-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=12, 状态=0, 用户ID=6750
[2025-08-01T11:52:28+08:00][info] 2025-08-01 11:52:28-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533459951676047399
[2025-08-01T11:52:28+08:00][info] 2025-08-01 11:52:28-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533459951676047399
[2025-08-01T11:52:28+08:00][info] 2025-08-01 11:52:28-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533459951676047399
[2025-08-01T11:52:28+08:00][info] 2025-08-01 11:52:28-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 120.17ms
[2025-08-01T11:52:28+08:00][info] 2025-08-01 11:52:28-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:52:28+08:00][info] 2025-08-01 11:52:28-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"data":[{"connector_id":"1024","create_time":1754020332,"is_output_trimmed":false,"token":"0","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","execute_id":"7533459951676047399","run_mode":2,"execute_status":"Running","connector_uid":"1599136090822611","logid":"20250801115211088D5950237D3EE50263","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533459951676047399&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","usage":{"token_count":0,"output_count":0,"input_count":0},"bot_id":"0","node_execute_status":[],"error_code":"","update_time":1754020332}],"detail":{"logid":"2025080111522801D36D4728B8C8F3ACAE"},"code":0,"msg":""}}
[2025-08-01T11:52:28+08:00][info] 2025-08-01 11:52:28-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:52:28+08:00][info] 2025-08-01 11:52:28-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=12, 状态=0, 用户ID=6750
[2025-08-01T11:52:31+08:00][info] 2025-08-01 11:52:31-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533459951676047399
[2025-08-01T11:52:31+08:00][info] 2025-08-01 11:52:31-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533459951676047399
[2025-08-01T11:52:31+08:00][info] 2025-08-01 11:52:31-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533459951676047399
[2025-08-01T11:52:31+08:00][info] 2025-08-01 11:52:31-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 260.11ms
[2025-08-01T11:52:31+08:00][info] 2025-08-01 11:52:31-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:52:31+08:00][info] 2025-08-01 11:52:31-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"msg":"","data":[{"node_execute_status":[],"usage":{"token_count":0,"output_count":0,"input_count":0},"create_time":1754020332,"is_output_trimmed":false,"execute_status":"Running","update_time":1754020332,"run_mode":2,"error_code":"","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533459951676047399&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","bot_id":"0","token":"0","connector_uid":"1599136090822611","execute_id":"7533459951676047399","connector_id":"1024","logid":"20250801115211088D5950237D3EE50263"}],"detail":{"logid":"202508011152318AA4187A8FB1971EDB3C"},"code":0}}
[2025-08-01T11:52:31+08:00][info] 2025-08-01 11:52:31-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:52:31+08:00][info] 2025-08-01 11:52:31-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=12, 状态=0, 用户ID=6750
[2025-08-01T11:52:34+08:00][info] 2025-08-01 11:52:34-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533459951676047399
[2025-08-01T11:52:35+08:00][info] 2025-08-01 11:52:34-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533459951676047399
[2025-08-01T11:52:35+08:00][info] 2025-08-01 11:52:34-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533459951676047399
[2025-08-01T11:52:35+08:00][info] 2025-08-01 11:52:34-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 120.73ms
[2025-08-01T11:52:35+08:00][info] 2025-08-01 11:52:34-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:52:35+08:00][info] 2025-08-01 11:52:34-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"data":[{"create_time":1754020332,"execute_status":"Running","connector_id":"1024","token":"0","connector_uid":"1599136090822611","node_execute_status":[],"error_code":"","execute_id":"7533459951676047399","bot_id":"0","update_time":1754020332,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533459951676047399&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","is_output_trimmed":false,"run_mode":2,"logid":"20250801115211088D5950237D3EE50263","usage":{"token_count":0,"output_count":0,"input_count":0}}],"detail":{"logid":"202508011152347B118C2623F37A82549C"},"code":0,"msg":""}}
[2025-08-01T11:52:35+08:00][info] 2025-08-01 11:52:34-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:52:35+08:00][info] 2025-08-01 11:52:35-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=12, 状态=0, 用户ID=6750
[2025-08-01T11:52:38+08:00][info] 2025-08-01 11:52:38-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533459951676047399
[2025-08-01T11:52:38+08:00][info] 2025-08-01 11:52:38-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533459951676047399
[2025-08-01T11:52:38+08:00][info] 2025-08-01 11:52:38-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533459951676047399
[2025-08-01T11:52:38+08:00][info] 2025-08-01 11:52:38-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 420.17ms
[2025-08-01T11:52:38+08:00][info] 2025-08-01 11:52:38-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:52:38+08:00][info] 2025-08-01 11:52:38-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"msg":"","data":[{"token":"0","run_mode":2,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533459951676047399&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","connector_id":"1024","logid":"20250801115211088D5950237D3EE50263","execute_id":"7533459951676047399","execute_status":"Running","connector_uid":"1599136090822611","usage":{"input_count":0,"token_count":0,"output_count":0},"update_time":1754020332,"create_time":1754020332,"is_output_trimmed":false,"node_execute_status":[],"error_code":"","bot_id":"0"}],"detail":{"logid":"20250801115238844C5775A87975D9668C"},"code":0}}
[2025-08-01T11:52:38+08:00][info] 2025-08-01 11:52:38-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:52:38+08:00][info] 2025-08-01 11:52:38-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=12, 状态=0, 用户ID=6750
[2025-08-01T11:52:41+08:00][info] 2025-08-01 11:52:41-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533459951676047399
[2025-08-01T11:52:41+08:00][info] 2025-08-01 11:52:41-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533459951676047399
[2025-08-01T11:52:41+08:00][info] 2025-08-01 11:52:41-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533459951676047399
[2025-08-01T11:52:41+08:00][info] 2025-08-01 11:52:41-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 104.01ms
[2025-08-01T11:52:41+08:00][info] 2025-08-01 11:52:41-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:52:41+08:00][info] 2025-08-01 11:52:41-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"msg":"","data":[{"execute_id":"7533459951676047399","run_mode":2,"is_output_trimmed":false,"token":"0","logid":"20250801115211088D5950237D3EE50263","usage":{"token_count":0,"output_count":0,"input_count":0},"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","bot_id":"0","connector_id":"1024","update_time":1754020332,"node_execute_status":[],"connector_uid":"1599136090822611","error_code":"","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533459951676047399&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","create_time":1754020332,"execute_status":"Running"}],"detail":{"logid":"202508011152416CE4EBB9D7AA491CFE74"},"code":0}}
[2025-08-01T11:52:41+08:00][info] 2025-08-01 11:52:41-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:52:41+08:00][info] 2025-08-01 11:52:41-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=12, 状态=0, 用户ID=6750
[2025-08-01T11:52:45+08:00][info] 2025-08-01 11:52:45-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533459951676047399
[2025-08-01T11:52:45+08:00][info] 2025-08-01 11:52:45-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533459951676047399
[2025-08-01T11:52:45+08:00][info] 2025-08-01 11:52:45-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533459951676047399
[2025-08-01T11:52:45+08:00][info] 2025-08-01 11:52:45-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 143.84ms
[2025-08-01T11:52:45+08:00][info] 2025-08-01 11:52:45-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T11:52:45+08:00][info] 2025-08-01 11:52:45-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"run_mode":2,"logid":"20250801115211088D5950237D3EE50263","node_execute_status":[],"connector_id":"1024","token":"0","connector_uid":"1599136090822611","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","error_code":"","bot_id":"0","update_time":1754020332,"usage":{"input_count":0,"token_count":0,"output_count":0},"execute_id":"7533459951676047399","create_time":1754020332,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533459951676047399&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","is_output_trimmed":false,"execute_status":"Running"}],"detail":{"logid":"2025080111524544757DB834AD1883C543"}}}
[2025-08-01T11:52:45+08:00][info] 2025-08-01 11:52:45-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T11:52:45+08:00][info] 2025-08-01 11:52:45-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=12, 状态=0, 用户ID=6750
[2025-08-01T11:52:48+08:00][info] 2025-08-01 11:52:48-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533459951676047399
[2025-08-01T11:52:48+08:00][info] 2025-08-01 11:52:48-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533459951676047399
[2025-08-01T11:52:48+08:00][info] 2025-08-01 11:52:48-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533459951676047399
[2025-08-01T11:52:48+08:00][info] 2025-08-01 11:52:48-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 129.04ms
[2025-08-01T11:52:48+08:00][info] 2025-08-01 11:52:48-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Success，数据库状态: completed
[2025-08-01T11:52:48+08:00][info] 2025-08-01 11:52:48-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"data":[{"logid":"20250801115211088D5950237D3EE50263","output":"{\"node_status\":\"{}\",\"Output\":\"{\\\"image\\\":\\\"https:\/\/s.coze.cn\/t\/tmC6m24_8aw\/\\\"}\"}","node_execute_status":[],"error_code":"0","error_message":"","create_time":1754020332,"is_output_trimmed":false,"token":"949","execute_id":"7533459951676047399","connector_id":"1024","update_time":1754020367,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533459951676047399&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","usage":{"output_count":19,"input_count":930,"token_count":949},"bot_id":"0","execute_status":"Success","connector_uid":"1599136090822611","run_mode":2}],"detail":{"logid":"2025080111524845EE9862CCC7B91FD887"},"code":0,"msg":""}}
[2025-08-01T11:52:48+08:00][info] 2025-08-01 11:52:48-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流完成，更新状态: 图片URL=https://s.coze.cn/t/tmC6m24_8aw/, 记录ID=12
[2025-08-01T11:52:48+08:00][info] 2025-08-01 11:52:48-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=12, 状态=1, 用户ID=6750
[2025-08-01T12:05:10+08:00][info] 2025-08-01 12:05:10-DEBUG-[ApiDreamInspiration][executeWorkflow] 开始调用工作流: 记录ID=13, 工作流ID=7519352650686890038, 性别=0, 职业=医生
[2025-08-01T12:05:10+08:00][info] 2025-08-01 12:05:10-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流参数: {"BOT_USER_INPUT":"","gender":"女","image_url":"https:\/\/kuaifengimg.azheteng.cn\/upload\/62\/20250801\/2e8904d7172f5e4b0335890b935ee6f5.png","zhiye":"医生"}
[2025-08-01T12:05:10+08:00][info] 2025-08-01 12:05:10-INFO-[Coze][runWorkflow_001] 开始运行工作流，ID: 7519352650686890038, 异步: 是
[2025-08-01T12:05:10+08:00][info] 2025-08-01 12:05:10-INFO-[Coze][sendRequest_002] 准备发送请求: POST https://api.coze.cn/v1/workflow/run
[2025-08-01T12:05:10+08:00][info] 2025-08-01 12:05:10-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 178.35ms
[2025-08-01T12:05:10+08:00][info] 2025-08-01 12:05:10-DEBUG-[Coze][runWorkflow_debug] API响应结构: {"code":1,"msg":"请求成功","data":{"code":0,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533463302991708199&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","execute_id":"7533463302991708199","msg":"Success"}}
[2025-08-01T12:05:10+08:00][info] 2025-08-01 12:05:10-INFO-[Coze][runWorkflow_002] 工作流执行成功，执行ID: 7533463302991708199，模式: 异步
[2025-08-01T12:05:10+08:00][info] 2025-08-01 12:05:10-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流调用结果: code=1, msg=请求成功, has_data=yes
[2025-08-01T12:05:10+08:00][info] 2025-08-01 12:05:10-DEBUG-[ApiDreamInspiration][executeWorkflow] 工作流原始返回数据: {"code":0,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533463302991708199&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","execute_id":"7533463302991708199","msg":"Success"}
[2025-08-01T12:05:10+08:00][info] 2025-08-01 12:05:10-DEBUG-[ApiDreamInspiration][executeWorkflow] 异步执行，保存执行ID: 7533463302991708199
[2025-08-01T12:05:11+08:00][info] 2025-08-01 12:05:11-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533463302991708199
[2025-08-01T12:05:11+08:00][info] 2025-08-01 12:05:11-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533463302991708199
[2025-08-01T12:05:11+08:00][info] 2025-08-01 12:05:11-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533463302991708199
[2025-08-01T12:05:11+08:00][info] 2025-08-01 12:05:11-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 125.71ms
[2025-08-01T12:05:11+08:00][info] 2025-08-01 12:05:11-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T12:05:11+08:00][info] 2025-08-01 12:05:11-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"bot_id":"0","token":"0","usage":{"output_count":0,"input_count":0,"token_count":0},"error_code":"","execute_id":"7533463302991708199","connector_id":"1024","update_time":1754021111,"is_output_trimmed":false,"create_time":1754021111,"connector_uid":"1599136090822611","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533463302991708199&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","execute_status":"Running","run_mode":2,"logid":"20250801120510B60CAD45F3E099E90802","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","node_execute_status":[]}],"detail":{"logid":"202508011205118CB8304650D20A1901E0"}}}
[2025-08-01T12:05:11+08:00][info] 2025-08-01 12:05:11-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T12:05:11+08:00][info] 2025-08-01 12:05:11-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=13, 状态=0, 用户ID=6750
[2025-08-01T12:05:14+08:00][info] 2025-08-01 12:05:14-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533463302991708199
[2025-08-01T12:05:14+08:00][info] 2025-08-01 12:05:14-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533463302991708199
[2025-08-01T12:05:14+08:00][info] 2025-08-01 12:05:14-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533463302991708199
[2025-08-01T12:05:14+08:00][info] 2025-08-01 12:05:14-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 101.05ms
[2025-08-01T12:05:14+08:00][info] 2025-08-01 12:05:14-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T12:05:14+08:00][info] 2025-08-01 12:05:14-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"error_code":"","execute_id":"7533463302991708199","create_time":1754021111,"logid":"20250801120510B60CAD45F3E099E90802","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","node_execute_status":[],"usage":{"output_count":0,"input_count":0,"token_count":0},"connector_id":"1024","is_output_trimmed":false,"token":"0","run_mode":2,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533463302991708199&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","bot_id":"0","execute_status":"Running","update_time":1754021111,"connector_uid":"1599136090822611"}],"detail":{"logid":"202508011205143207D31C067ECAE19585"}}}
[2025-08-01T12:05:14+08:00][info] 2025-08-01 12:05:14-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T12:05:14+08:00][info] 2025-08-01 12:05:14-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=13, 状态=0, 用户ID=6750
[2025-08-01T12:05:17+08:00][info] 2025-08-01 12:05:17-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533463302991708199
[2025-08-01T12:05:17+08:00][info] 2025-08-01 12:05:17-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533463302991708199
[2025-08-01T12:05:17+08:00][info] 2025-08-01 12:05:17-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533463302991708199
[2025-08-01T12:05:17+08:00][info] 2025-08-01 12:05:17-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 113.93ms
[2025-08-01T12:05:17+08:00][info] 2025-08-01 12:05:17-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T12:05:17+08:00][info] 2025-08-01 12:05:17-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"logid":"20250801120510B60CAD45F3E099E90802","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533463302991708199&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","node_execute_status":[],"execute_status":"Running","run_mode":2,"connector_id":"1024","connector_uid":"1599136090822611","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","usage":{"token_count":0,"output_count":0,"input_count":0},"error_code":"","execute_id":"7533463302991708199","bot_id":"0","create_time":1754021111,"update_time":1754021111,"is_output_trimmed":false,"token":"0"}],"detail":{"logid":"20250801120517C1F5EAAB80B2470FA422"}}}
[2025-08-01T12:05:17+08:00][info] 2025-08-01 12:05:17-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T12:05:17+08:00][info] 2025-08-01 12:05:17-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=13, 状态=0, 用户ID=6750
[2025-08-01T12:05:20+08:00][info] 2025-08-01 12:05:20-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533463302991708199
[2025-08-01T12:05:20+08:00][info] 2025-08-01 12:05:20-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533463302991708199
[2025-08-01T12:05:20+08:00][info] 2025-08-01 12:05:20-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533463302991708199
[2025-08-01T12:05:20+08:00][info] 2025-08-01 12:05:20-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 120.66ms
[2025-08-01T12:05:20+08:00][info] 2025-08-01 12:05:20-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T12:05:20+08:00][info] 2025-08-01 12:05:20-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"data":[{"create_time":1754021111,"run_mode":2,"connector_uid":"1599136090822611","logid":"20250801120510B60CAD45F3E099E90802","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533463302991708199&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","bot_id":"0","update_time":1754021111,"is_output_trimmed":false,"connector_id":"1024","execute_status":"Running","token":"0","usage":{"output_count":0,"input_count":0,"token_count":0},"error_code":"","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","node_execute_status":[],"execute_id":"7533463302991708199"}],"detail":{"logid":"20250801120520514384A1BE417C8F3E65"},"code":0,"msg":""}}
[2025-08-01T12:05:20+08:00][info] 2025-08-01 12:05:20-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T12:05:20+08:00][info] 2025-08-01 12:05:20-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=13, 状态=0, 用户ID=6750
[2025-08-01T12:05:24+08:00][info] 2025-08-01 12:05:24-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533463302991708199
[2025-08-01T12:05:24+08:00][info] 2025-08-01 12:05:24-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533463302991708199
[2025-08-01T12:05:24+08:00][info] 2025-08-01 12:05:24-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533463302991708199
[2025-08-01T12:05:24+08:00][info] 2025-08-01 12:05:24-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 98.94ms
[2025-08-01T12:05:24+08:00][info] 2025-08-01 12:05:24-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T12:05:24+08:00][info] 2025-08-01 12:05:24-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"msg":"","data":[{"bot_id":"0","execute_status":"Running","run_mode":2,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533463302991708199&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","node_execute_status":[],"connector_id":"1024","update_time":1754021111,"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","execute_id":"7533463302991708199","is_output_trimmed":false,"token":"0","connector_uid":"1599136090822611","usage":{"token_count":0,"output_count":0,"input_count":0},"create_time":1754021111,"error_code":"","logid":"20250801120510B60CAD45F3E099E90802"}],"detail":{"logid":"20250801120524CF0BBA4DD6A27BF43C6D"},"code":0}}
[2025-08-01T12:05:24+08:00][info] 2025-08-01 12:05:24-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T12:05:24+08:00][info] 2025-08-01 12:05:24-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=13, 状态=0, 用户ID=6750
[2025-08-01T12:05:27+08:00][info] 2025-08-01 12:05:27-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533463302991708199
[2025-08-01T12:05:27+08:00][info] 2025-08-01 12:05:27-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533463302991708199
[2025-08-01T12:05:27+08:00][info] 2025-08-01 12:05:27-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533463302991708199
[2025-08-01T12:05:27+08:00][info] 2025-08-01 12:05:27-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 138.02ms
[2025-08-01T12:05:27+08:00][info] 2025-08-01 12:05:27-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T12:05:27+08:00][info] 2025-08-01 12:05:27-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"data":[{"update_time":1754021111,"token":"0","execute_status":"Running","run_mode":2,"usage":{"token_count":0,"output_count":0,"input_count":0},"execute_id":"7533463302991708199","create_time":1754021111,"connector_uid":"1599136090822611","logid":"20250801120510B60CAD45F3E099E90802","node_execute_status":[],"bot_id":"0","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533463302991708199&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","is_output_trimmed":false,"output":"{\"Output\":\"\",\"node_status\":\"{}\"}","error_code":"","connector_id":"1024"}],"detail":{"logid":"202508011205273C0E3CC15429D01BE8E3"},"code":0,"msg":""}}
[2025-08-01T12:05:27+08:00][info] 2025-08-01 12:05:27-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T12:05:27+08:00][info] 2025-08-01 12:05:27-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=13, 状态=0, 用户ID=6750
[2025-08-01T12:05:30+08:00][info] 2025-08-01 12:05:30-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533463302991708199
[2025-08-01T12:05:30+08:00][info] 2025-08-01 12:05:30-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533463302991708199
[2025-08-01T12:05:30+08:00][info] 2025-08-01 12:05:30-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533463302991708199
[2025-08-01T12:05:30+08:00][info] 2025-08-01 12:05:30-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 95.63ms
[2025-08-01T12:05:30+08:00][info] 2025-08-01 12:05:30-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T12:05:30+08:00][info] 2025-08-01 12:05:30-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"is_output_trimmed":false,"logid":"20250801120510B60CAD45F3E099E90802","bot_id":"0","execute_status":"Running","connector_uid":"1599136090822611","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533463302991708199&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","node_execute_status":[],"update_time":1754021111,"token":"0","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","usage":{"input_count":0,"token_count":0,"output_count":0},"connector_id":"1024","create_time":1754021111,"run_mode":2,"error_code":"","execute_id":"7533463302991708199"}],"detail":{"logid":"20250801120530BF864820014715E548F9"}}}
[2025-08-01T12:05:30+08:00][info] 2025-08-01 12:05:30-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T12:05:30+08:00][info] 2025-08-01 12:05:30-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=13, 状态=0, 用户ID=6750
[2025-08-01T12:05:33+08:00][info] 2025-08-01 12:05:33-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533463302991708199
[2025-08-01T12:05:34+08:00][info] 2025-08-01 12:05:33-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533463302991708199
[2025-08-01T12:05:34+08:00][info] 2025-08-01 12:05:33-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533463302991708199
[2025-08-01T12:05:34+08:00][info] 2025-08-01 12:05:33-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 325.08ms
[2025-08-01T12:05:34+08:00][info] 2025-08-01 12:05:33-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T12:05:34+08:00][info] 2025-08-01 12:05:33-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"update_time":1754021111,"is_output_trimmed":false,"token":"0","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533463302991708199&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","error_code":"","execute_id":"7533463302991708199","connector_id":"1024","create_time":1754021111,"run_mode":2,"node_execute_status":[],"usage":{"token_count":0,"output_count":0,"input_count":0},"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","bot_id":"0","execute_status":"Running","connector_uid":"1599136090822611","logid":"20250801120510B60CAD45F3E099E90802"}],"detail":{"logid":"20250801120533F0CD983C10A5208B4290"}}}
[2025-08-01T12:05:34+08:00][info] 2025-08-01 12:05:33-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T12:05:34+08:00][info] 2025-08-01 12:05:34-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=13, 状态=0, 用户ID=6750
[2025-08-01T12:05:37+08:00][info] 2025-08-01 12:05:37-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533463302991708199
[2025-08-01T12:05:37+08:00][info] 2025-08-01 12:05:37-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533463302991708199
[2025-08-01T12:05:37+08:00][info] 2025-08-01 12:05:37-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533463302991708199
[2025-08-01T12:05:37+08:00][info] 2025-08-01 12:05:37-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 107.98ms
[2025-08-01T12:05:37+08:00][info] 2025-08-01 12:05:37-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T12:05:37+08:00][info] 2025-08-01 12:05:37-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"logid":"20250801120510B60CAD45F3E099E90802","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533463302991708199&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","bot_id":"0","is_output_trimmed":false,"connector_uid":"1599136090822611","error_code":"","connector_id":"1024","execute_status":"Running","node_execute_status":[],"execute_id":"7533463302991708199","token":"0","run_mode":2,"usage":{"token_count":0,"output_count":0,"input_count":0},"create_time":1754021111,"update_time":1754021111}],"detail":{"logid":"202508011205370075AE2FBDC8170D3003"}}}
[2025-08-01T12:05:37+08:00][info] 2025-08-01 12:05:37-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T12:05:37+08:00][info] 2025-08-01 12:05:37-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=13, 状态=0, 用户ID=6750
[2025-08-01T12:05:40+08:00][info] 2025-08-01 12:05:40-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533463302991708199
[2025-08-01T12:05:40+08:00][info] 2025-08-01 12:05:40-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533463302991708199
[2025-08-01T12:05:40+08:00][info] 2025-08-01 12:05:40-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533463302991708199
[2025-08-01T12:05:40+08:00][info] 2025-08-01 12:05:40-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 112.33ms
[2025-08-01T12:05:40+08:00][info] 2025-08-01 12:05:40-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T12:05:40+08:00][info] 2025-08-01 12:05:40-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"create_time":1754021111,"update_time":1754021111,"is_output_trimmed":false,"token":"0","execute_status":"Running","node_execute_status":[],"connector_id":"1024","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533463302991708199&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","usage":{"output_count":0,"input_count":0,"token_count":0},"run_mode":2,"error_code":"","bot_id":"0","connector_uid":"1599136090822611","logid":"20250801120510B60CAD45F3E099E90802","execute_id":"7533463302991708199"}],"detail":{"logid":"20250801120540A62126D704183DA507CA"}}}
[2025-08-01T12:05:40+08:00][info] 2025-08-01 12:05:40-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T12:05:40+08:00][info] 2025-08-01 12:05:40-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=13, 状态=0, 用户ID=6750
[2025-08-01T12:05:43+08:00][info] 2025-08-01 12:05:43-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533463302991708199
[2025-08-01T12:05:43+08:00][info] 2025-08-01 12:05:43-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533463302991708199
[2025-08-01T12:05:43+08:00][info] 2025-08-01 12:05:43-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533463302991708199
[2025-08-01T12:05:43+08:00][info] 2025-08-01 12:05:43-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 134.31ms
[2025-08-01T12:05:43+08:00][info] 2025-08-01 12:05:43-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T12:05:43+08:00][info] 2025-08-01 12:05:43-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"node_execute_status":[],"execute_status":"Running","debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533463302991708199&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","error_code":"","bot_id":"0","execute_id":"7533463302991708199","connector_id":"1024","create_time":1754021111,"update_time":1754021111,"connector_uid":"1599136090822611","output":"{\"node_status\":\"{}\",\"Output\":\"\"}","usage":{"token_count":0,"output_count":0,"input_count":0},"is_output_trimmed":false,"token":"0","run_mode":2,"logid":"20250801120510B60CAD45F3E099E90802"}],"detail":{"logid":"20250801120543549CD92EA0D597D59F30"}}}
[2025-08-01T12:05:43+08:00][info] 2025-08-01 12:05:43-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T12:05:43+08:00][info] 2025-08-01 12:05:43-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=13, 状态=0, 用户ID=6750
[2025-08-01T12:05:47+08:00][info] 2025-08-01 12:05:47-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533463302991708199
[2025-08-01T12:05:47+08:00][info] 2025-08-01 12:05:47-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533463302991708199
[2025-08-01T12:05:47+08:00][info] 2025-08-01 12:05:47-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533463302991708199
[2025-08-01T12:05:47+08:00][info] 2025-08-01 12:05:47-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 101.01ms
[2025-08-01T12:05:47+08:00][info] 2025-08-01 12:05:47-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Running，数据库状态: running
[2025-08-01T12:05:47+08:00][info] 2025-08-01 12:05:47-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"code":0,"msg":"","data":[{"logid":"20250801120510B60CAD45F3E099E90802","execute_id":"7533463302991708199","token":"0","connector_uid":"1599136090822611","execute_status":"Running","run_mode":2,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533463302991708199&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","node_execute_status":[],"usage":{"token_count":0,"output_count":0,"input_count":0},"bot_id":"0","update_time":1754021111,"is_output_trimmed":false,"output":"{\"node_status\":\"{}\",\"Output\":\"\"}","connector_id":"1024","create_time":1754021111,"error_code":""}],"detail":{"logid":"2025080112054710AD913925FB67183F82"}}}
[2025-08-01T12:05:47+08:00][info] 2025-08-01 12:05:47-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流执行中，当前状态: Running
[2025-08-01T12:05:47+08:00][info] 2025-08-01 12:05:47-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=13, 状态=0, 用户ID=6750
[2025-08-01T12:05:50+08:00][info] 2025-08-01 12:05:50-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 开始查询工作流结果: 执行ID=7533463302991708199
[2025-08-01T12:05:50+08:00][info] 2025-08-01 12:05:50-INFO-[Coze][queryWorkflowResult_001] 查询工作流执行结果，工作流ID: 7519352650686890038, 执行ID: 7533463302991708199
[2025-08-01T12:05:50+08:00][info] 2025-08-01 12:05:50-INFO-[Coze][sendRequest_002] 准备发送请求: GET https://api.coze.cn/v1/workflows/7519352650686890038/run_histories/7533463302991708199
[2025-08-01T12:05:50+08:00][info] 2025-08-01 12:05:50-INFO-[Coze][sendRequest_003] 请求完成，HTTP状态码: 200，耗时: 116.78ms
[2025-08-01T12:05:50+08:00][info] 2025-08-01 12:05:50-INFO-[Coze][queryWorkflowResult_002] 查询成功，原始状态: Success，数据库状态: completed
[2025-08-01T12:05:50+08:00][info] 2025-08-01 12:05:50-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 查询结果: {"code":1,"msg":"请求成功","data":{"detail":{"logid":"20250801120550EF4381D3D64798F9E1F3"},"code":0,"msg":"","data":[{"update_time":1754021150,"is_output_trimmed":false,"debug_url":"https:\/\/www.coze.cn\/work_flow?execute_id=7533463302991708199&space_id=7457443857832116261&workflow_id=7519352650686890038&execute_mode=2","output":"{\"node_status\":\"{}\",\"Output\":\"{\\\"image\\\":\\\"https:\/\/s.coze.cn\/t\/vVMw76VnPDg\/\\\"}\"}","execute_id":"7533463302991708199","bot_id":"0","error_code":"0","connector_uid":"1599136090822611","node_execute_status":[],"run_mode":2,"connector_id":"1024","execute_status":"Success","logid":"20250801120510B60CAD45F3E099E90802","usage":{"token_count":949,"output_count":19,"input_count":930},"error_message":"","create_time":1754021111,"token":"949"}]}}
[2025-08-01T12:05:50+08:00][info] 2025-08-01 12:05:50-DEBUG-[ApiDreamInspiration][queryAndUpdateWorkflowResult] 工作流完成，更新状态: 图片URL=https://s.coze.cn/t/vVMw76VnPDg/, 记录ID=13
[2025-08-01T12:05:50+08:00][info] 2025-08-01 12:05:50-DEBUG-[ApiDreamInspiration][checkGenerationStatus] 查询生成状态: 记录ID=13, 状态=1, 用户ID=6750
