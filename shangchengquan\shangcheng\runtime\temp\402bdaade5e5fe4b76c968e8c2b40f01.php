<?php /*a:3:{s:88:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\dream_inspiration\setting.html";i:1754021701;s:73:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\css.html";i:1745486434;s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\js.html";i:1745486434;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title><?php echo t('启蒙设置'); ?></title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" type="text/css" href="/static/admin/layui/css/layui.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/layui/css/modules/formSelects-v4.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css?v=20210826" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/font-awesome.min.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/webuploader/webuploader.css?v=<?php echo time(); ?>" media="all">
<link rel="stylesheet" type="text/css" href="/static/imgsrc/designer.css?v=20220803" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header"><i class="fa fa-cog"></i> <?php echo t('启蒙设置'); ?></div>
          <div class="layui-card-body" pad15>
            <form class="layui-form" action="<?php echo url('setting'); ?>" method="post">
              <div class="layui-row">
                <div class="layui-col-md6">
                  <div class="layui-card">
                    <div class="layui-card-header">基础设置</div>
                    <div class="layui-card-body">
                      <div class="layui-form-item">
                        <label class="layui-form-label">功能状态</label>
                        <div class="layui-input-block">
                          <input type="checkbox" name="is_enabled" value="1" lay-skin="switch" lay-text="启用|禁用" <?php if($setting['is_enabled']): ?>checked<?php endif; ?>>
                          <div class="layui-form-mid layui-word-aux">开启后用户可以使用梦想启蒙功能</div>
                        </div>
                      </div>
                      
                      <div class="layui-form-item">
                        <label class="layui-form-label">欢迎文案</label>
                        <div class="layui-input-block">
                          <textarea name="welcome_text" placeholder="请输入欢迎文案" class="layui-textarea" rows="3"><?php echo $setting['welcome_text']; ?></textarea>
                          <div class="layui-form-mid layui-word-aux">用户进入梦想启蒙页面时显示的欢迎文案</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="layui-col-md6">
                  <div class="layui-card">
                    <div class="layui-card-header">工作流配置</div>
                    <div class="layui-card-body">
                      <div class="layui-form-item">
                        <label class="layui-form-label">选择工作流</label>
                        <div class="layui-input-block">
                          <select name="workflow_id" lay-verify="required" lay-filter="workflow-select">
                            <option value="">请选择工作流</option>
                            <?php if(is_array($workflows) || $workflows instanceof \think\Collection || $workflows instanceof \think\Paginator): $i = 0; $__LIST__ = $workflows;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$workflow): $mod = ($i % 2 );++$i;?>
                            <option value="<?php echo $workflow['workflow_id']; ?>" data-name="<?php echo $workflow['name']; ?>" <?php if($setting['workflow_id'] == $workflow['workflow_id']): ?>selected<?php endif; ?>><?php echo $workflow['name']; ?></option>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                          </select>
                          <input type="hidden" name="workflow_name" value="<?php echo $setting['workflow_name']; ?>">
                          <div class="layui-form-mid layui-word-aux">选择用于生成梦想图片的扣子工作流</div>
                        </div>
                      </div>
                      
                      <div class="layui-form-item">
                        <label class="layui-form-label">提示词模板</label>
                        <div class="layui-input-block">
                          <textarea name="prompt_template" placeholder="请输入提示词模板" class="layui-textarea" rows="4"><?php echo $setting['prompt_template']; ?></textarea>
                          <div class="layui-form-mid layui-word-aux">
                            支持变量：{gender} - 用户性别，{dream} - 梦想内容<br>
                            示例：请根据用户性别：{gender}，梦想：{dream}，生成一张励志图片
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="layui-row" style="margin-top: 15px;">
                <div class="layui-col-md12">
                  <div class="layui-card">
                    <div class="layui-card-header">与未来对话设置</div>
                    <div class="layui-card-body">
                      <div class="layui-form-item">
                        <label class="layui-form-label">功能状态</label>
                        <div class="layui-input-block">
                          <input type="checkbox" name="future_talk_enabled" value="1" lay-skin="switch" lay-text="启用|禁用" <?php if($setting['future_talk_enabled']): ?>checked<?php endif; ?>>
                          <div class="layui-form-mid layui-word-aux">开启后用户可以使用与未来对话功能</div>
                        </div>
                      </div>

                      <div class="layui-form-item">
                        <label class="layui-form-label">按钮文字</label>
                        <div class="layui-input-block">
                          <input type="text" name="future_talk_button_text" placeholder="请输入按钮显示文字" class="layui-input" value="<?php echo $setting['future_talk_button_text']; ?>">
                          <div class="layui-form-mid layui-word-aux">在前端页面显示的按钮文字</div>
                        </div>
                      </div>

                      <div class="layui-form-item">
                        <label class="layui-form-label">男性用户链接</label>
                        <div class="layui-input-block">
                          <div class="layui-input-group">
                            <input type="text" name="future_talk_male_url" id="future_talk_male_url" placeholder="请输入男性用户的对话链接" class="layui-input" value="<?php echo $setting['future_talk_male_url']; ?>">
                            <div class="layui-input-split layui-input-suffix">
                              <button type="button" class="layui-btn layui-btn-primary" onclick="chooseUrl2('future_talk_male_url')">选择链接</button>
                            </div>
                          </div>
                          <div class="layui-form-mid layui-word-aux">男性用户点击"与未来对话"时跳转的链接</div>
                        </div>
                      </div>

                      <div class="layui-form-item">
                        <label class="layui-form-label">女性用户链接</label>
                        <div class="layui-input-block">
                          <div class="layui-input-group">
                            <input type="text" name="future_talk_female_url" id="future_talk_female_url" placeholder="请输入女性用户的对话链接" class="layui-input" value="<?php echo $setting['future_talk_female_url']; ?>">
                            <div class="layui-input-split layui-input-suffix">
                              <button type="button" class="layui-btn layui-btn-primary" onclick="chooseUrl2('future_talk_female_url')">选择链接</button>
                            </div>
                          </div>
                          <div class="layui-form-mid layui-word-aux">女性用户点击"与未来对话"时跳转的链接</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="layui-row" style="margin-top: 15px;">
                <div class="layui-col-md12">
                  <div class="layui-card">
                    <div class="layui-card-header">使用说明</div>
                    <div class="layui-card-body">
                      <div class="layui-text">
                        <h4>功能说明：</h4>
                        <ol>
                          <li>用户在前端页面输入性别和梦想内容</li>
                          <li>系统根据提示词模板生成完整的提示词</li>
                          <li>调用选定的扣子工作流生成励志图片</li>
                          <li>将生成的图片展示给用户并保存记录</li>
                        </ol>
                        
                        <h4>配置要求：</h4>
                        <ul>
                          <li>需要先在扣子管理中配置好API和工作流</li>
                          <li>工作流需要支持文本输入并返回图片</li>
                          <li>提示词模板中的变量会被自动替换</li>
                        </ul>
                        
                        <h4>前端页面：</h4>
                        <p>前端页面路径：/pagesExt/bpv/index</p>
                        <p>用户可以在此页面输入梦想并生成励志图片</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="layui-form-item" style="margin-top: 20px;">
                <div class="layui-input-block">
                  <button class="layui-btn" lay-submit lay-filter="formSubmit">保存设置</button>
                  <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
              </div>
            </form>
          </div>
        </div>
    </div>
  </div>

  <script type="text/javascript" src="/static/admin/layui/layui.all.js?v=20210222"></script>
<script type="text/javascript" src="/static/admin/layui/lay/modules/formSelects-v4.js"></script>
<script type="text/javascript" src="/static/admin/js/jquery-ui.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/ueditor/ueditor.js?v=20220707"></script>
<script type="text/javascript" src="/static/admin/ueditor/135editor.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/webuploader/webuploader.js?v=20200620"></script>
<script type="text/javascript" src="/static/admin/js/qrcode.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/js/dianda.js?v=2022"></script>
  <script>
    var form = layui.form;
    
    // 监听工作流选择
    form.on('select(workflow-select)', function(data){
      var selectedOption = $(data.elem).find('option:selected');
      var workflowName = selectedOption.data('name') || '';
      $('input[name="workflow_name"]').val(workflowName);
    });
    
    // 链接选择功能
    var chooseUrlField = '';
    function chooseUrl2(field){
      chooseUrlField = field;
      layer.open({type:2,shadeClose:true,area:['1100px', '650px'],'title':'选择链接',content:"<?php echo url('DesignerPage/chooseurl'); ?>&callback=chooseLink2"})
    }
    function chooseLink2(urlname,url){
      $("#"+chooseUrlField).val(url);
    }

    // 监听提交
    form.on('submit(formSubmit)', function(data){
      var field = data.field;
      var index = layer.load();
      $.post("<?php echo url('setting'); ?>", field, function(res){
        layer.close(index);
        if(res.code == 1){
          layer.msg(res.msg, {icon: 1});
          setTimeout(function(){
            location.reload();
          }, 1000);
        }else{
          layer.msg(res.msg, {icon: 2});
        }
      });
      return false;
    });
  </script>
</body>
</html>
