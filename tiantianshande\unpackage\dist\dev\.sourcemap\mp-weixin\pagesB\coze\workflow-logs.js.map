{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/workflow-logs.vue?433e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/workflow-logs.vue?cd95", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/workflow-logs.vue?1077", "uni-app:///pagesB/coze/workflow-logs.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/workflow-logs.vue?fef2", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/workflow-logs.vue?956f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "logsList", "workflowList", "selectedWorkflow", "currentLog", "pagenum", "nomore", "nodata", "onLoad", "onReachBottom", "onPullDownRefresh", "methods", "getWorkflowList", "app", "that", "getLogsList", "page", "limit", "params", "uni", "item", "title", "icon", "duration", "showWorkflowFilter", "closeWorkflowFilter", "selectWorkflowFilter", "showLogDetail", "closeLogDetail", "formatJson", "formatTime", "date"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjGA;AAAA;AAAA;AAAA;AAA0mB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmH9nB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;MACA;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAC;QACA;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MAEA;MACAD;MACAA;MACAA;MAEA;QACAE;QACAC;MACA;MAEA;QACAC;MACA;MAEAL;QACAC;QACAK;QAEA;UACA;;UAEA;UACArB;YACAsB;;YAEA;YACA;cACA;gBACA;gBACA;gBACA;kBACAA;oBAAA;kBAAA;kBACA;oBACAA;kBACA;gBACA;cACA;gBACAA;cACA;YACA;UACA;UAEA;YACAN;YACA;cACAA;YACA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;QACA;UACAK;YACAE;YACAC;YACAC;UACA;UACA;YACAT;UACA;QACA;QACAA;MACA;IACA;IAEA;IACAU;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA,kCACA,0DACAC,mDACAA,oDACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChSA;AAAA;AAAA;AAAA;AAA63B,CAAgB,42BAAG,EAAC,C;;;;;;;;;;;ACAj5B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesB/coze/workflow-logs.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesB/coze/workflow-logs.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./workflow-logs.vue?vue&type=template&id=a99a18d0&\"\nvar renderjs\nimport script from \"./workflow-logs.vue?vue&type=script&lang=js&\"\nexport * from \"./workflow-logs.vue?vue&type=script&lang=js&\"\nimport style0 from \"./workflow-logs.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesB/coze/workflow-logs.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./workflow-logs.vue?vue&type=template&id=a99a18d0&\"", "var components\ntry {\n  components = {\n    nomore: function () {\n      return import(\n        /* webpackChunkName: \"components/nomore/nomore\" */ \"@/components/nomore/nomore.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.logsList, function (log, index) {\n        var $orig = _vm.__get_orig(log)\n        var g0 = JSON.stringify(log)\n        return {\n          $orig: $orig,\n          g0: g0,\n        }\n      })\n    : null\n  var m0 = _vm.isload && !_vm.selectedWorkflow ? _vm.t(\"color1\") : null\n  var l1 = _vm.isload\n    ? _vm.__map(_vm.workflowList, function (workflow, index) {\n        var $orig = _vm.__get_orig(workflow)\n        var g1 = JSON.stringify(workflow)\n        var m1 =\n          _vm.selectedWorkflow &&\n          _vm.selectedWorkflow.workflow_id === workflow.workflow_id\n            ? _vm.t(\"color1\")\n            : null\n        return {\n          $orig: $orig,\n          g1: g1,\n          m1: m1,\n        }\n      })\n    : null\n  var m2 =\n    _vm.isload && _vm.currentLog.parameters\n      ? _vm.formatJson(_vm.currentLog.parameters)\n      : null\n  var m3 =\n    _vm.isload && _vm.currentLog.result\n      ? _vm.formatJson(_vm.currentLog.result)\n      : null\n  var m4 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        m0: m0,\n        l1: l1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./workflow-logs.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./workflow-logs.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<!-- 顶部导航 -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header-title\">工作流执行记录</view>\n\t\t</view>\n\n\t\t<!-- 筛选条件 -->\n\t\t<view class=\"filter-bar\">\n\t\t\t<view class=\"filter-item\" @tap=\"showWorkflowFilter\">\n\t\t\t\t<text>{{selectedWorkflow ? selectedWorkflow.name : '全部工作流'}}</text>\n\t\t\t\t<text class=\"iconfont iconxiala\"></text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 执行记录列表 -->\n\t\t<view class=\"logs-list\">\n\t\t\t<block v-for=\"(log, index) in logsList\" :key=\"index\">\n\t\t\t\t<view class=\"log-item\" @tap=\"showLogDetail\" :data-log=\"JSON.stringify(log)\">\n\t\t\t\t\t<view class=\"log-header\">\n\t\t\t\t\t\t<view class=\"log-workflow\">{{log.workflow_name || log.workflow_id}}</view>\n\t\t\t\t\t\t<view class=\"log-status\" :class=\"log.status ? 'success' : 'failed'\">\n\t\t\t\t\t\t\t<text class=\"iconfont\" :class=\"log.status ? 'iconzhengchang' : 'iconcuowu'\"></text>\n\t\t\t\t\t\t\t<text>{{log.status ? '成功' : '失败'}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"log-time\">{{log.create_time_text}}</view>\n\t\t\t\t\t<view class=\"log-params\" v-if=\"log.parameters_preview\">\n\t\t\t\t\t\t<text class=\"label\">参数：</text>\n\t\t\t\t\t\t<text class=\"value\">{{log.parameters_preview}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t\t<nomore v-if=\"nomore\"></nomore>\n\t\t\t<nodata v-if=\"nodata\"></nodata>\n\t\t</view>\n\n\t\t<!-- 工作流筛选弹窗 -->\n\t\t<uni-popup ref=\"workflowFilterPopup\" type=\"bottom\">\n\t\t\t<view class=\"filter-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<view class=\"popup-title\">选择工作流</view>\n\t\t\t\t\t<view class=\"popup-close\" @tap=\"closeWorkflowFilter\">\n\t\t\t\t\t\t<text class=\"iconfont iconguanbi\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<scroll-view class=\"filter-options\" scroll-y=\"true\">\n\t\t\t\t\t<view class=\"filter-option\" @tap=\"selectWorkflowFilter\" data-workflow=\"\">\n\t\t\t\t\t\t<text>全部工作流</text>\n\t\t\t\t\t\t<text v-if=\"!selectedWorkflow\" class=\"iconfont iconduihao\" :style=\"{color: t('color1')}\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<block v-for=\"(workflow, index) in workflowList\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"filter-option\" @tap=\"selectWorkflowFilter\" :data-workflow=\"JSON.stringify(workflow)\">\n\t\t\t\t\t\t\t<text>{{workflow.name}}</text>\n\t\t\t\t\t\t\t<text v-if=\"selectedWorkflow && selectedWorkflow.workflow_id === workflow.workflow_id\" class=\"iconfont iconduihao\" :style=\"{color: t('color1')}\"></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</uni-popup>\n\n\t\t<!-- 日志详情弹窗 -->\n\t\t<uni-popup ref=\"logDetailPopup\" type=\"center\">\n\t\t\t<view class=\"detail-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<view class=\"popup-title\">执行详情</view>\n\t\t\t\t\t<view class=\"popup-close\" @tap=\"closeLogDetail\">\n\t\t\t\t\t\t<text class=\"iconfont iconguanbi\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<scroll-view class=\"detail-content\" scroll-y=\"true\">\n\t\t\t\t\t<view class=\"detail-section\">\n\t\t\t\t\t\t<view class=\"section-title\">工作流信息</view>\n\t\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t\t<text class=\"label\">工作流ID：</text>\n\t\t\t\t\t\t\t<text class=\"value\">{{currentLog.workflow_id}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t\t<text class=\"label\">执行状态：</text>\n\t\t\t\t\t\t\t<text class=\"value\" :class=\"currentLog.status ? 'success' : 'failed'\">\n\t\t\t\t\t\t\t\t{{currentLog.status ? '成功' : '失败'}}\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t\t<text class=\"label\">执行时间：</text>\n\t\t\t\t\t\t\t<text class=\"value\">{{currentLog.create_time_text}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"detail-section\" v-if=\"currentLog.parameters\">\n\t\t\t\t\t\t<view class=\"section-title\">输入参数</view>\n\t\t\t\t\t\t<view class=\"code-block\">\n\t\t\t\t\t\t\t<text>{{formatJson(currentLog.parameters)}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"detail-section\" v-if=\"currentLog.result\">\n\t\t\t\t\t\t<view class=\"section-title\">执行结果</view>\n\t\t\t\t\t\t<view class=\"code-block\">\n\t\t\t\t\t\t\t<text>{{formatJson(currentLog.result)}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t\t<view class=\"popup-footer\">\n\t\t\t\t\t<view class=\"btn-confirm\" :style=\"{background: t('color1')}\" @tap=\"closeLogDetail\">确定</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tisload: false,\n\t\t\tloading: false,\n\t\t\tlogsList: [],\n\t\t\tworkflowList: [],\n\t\t\tselectedWorkflow: null,\n\t\t\tcurrentLog: {},\n\t\t\tpagenum: 1,\n\t\t\tnomore: false,\n\t\t\tnodata: false\n\t\t};\n\t},\n\n\tonLoad: function(opt) {\n\t\tthis.getWorkflowList();\n\t\tthis.getLogsList();\n\t},\n\n\tonReachBottom: function() {\n\t\tif (!this.nodata && !this.nomore) {\n\t\t\tthis.pagenum = this.pagenum + 1;\n\t\t\tthis.getLogsList(true);\n\t\t}\n\t},\n\n\tonPullDownRefresh: function() {\n\t\tthis.getLogsList();\n\t},\n\n\tmethods: {\n\t\t// 获取工作流列表\n\t\tgetWorkflowList: function() {\n\t\t\tvar that = this;\n\t\t\tapp.post('ApiCoze/getWorkflowList', {}, function(res) {\n\t\t\t\tif (res.code === 1) {\n\t\t\t\t\tthat.workflowList = res.data || [];\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 获取执行记录列表\n\t\tgetLogsList: function(loadmore) {\n\t\t\tif (!loadmore) {\n\t\t\t\tthis.pagenum = 1;\n\t\t\t\tthis.logsList = [];\n\t\t\t}\n\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tthat.nodata = false;\n\t\t\tthat.nomore = false;\n\n\t\t\tvar params = {\n\t\t\t\tpage: that.pagenum,\n\t\t\t\tlimit: 20\n\t\t\t};\n\n\t\t\tif (that.selectedWorkflow) {\n\t\t\t\tparams.workflow_id = that.selectedWorkflow.workflow_id;\n\t\t\t}\n\n\t\t\tapp.post('ApiCoze/getWorkflowLogs', params, function(res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tuni.stopPullDownRefresh();\n\n\t\t\t\tif (res.code === 1) {\n\t\t\t\t\tvar data = res.data.list || [];\n\t\t\t\t\t\n\t\t\t\t\t// 处理数据\n\t\t\t\t\tdata.forEach(function(item) {\n\t\t\t\t\t\titem.create_time_text = that.formatTime(item.create_time);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 处理参数预览\n\t\t\t\t\t\tif (item.parameters) {\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tvar params = JSON.parse(item.parameters);\n\t\t\t\t\t\t\t\tvar keys = Object.keys(params);\n\t\t\t\t\t\t\t\tif (keys.length > 0) {\n\t\t\t\t\t\t\t\t\titem.parameters_preview = keys.slice(0, 2).map(key => key + ': ' + params[key]).join(', ');\n\t\t\t\t\t\t\t\t\tif (keys.length > 2) {\n\t\t\t\t\t\t\t\t\t\titem.parameters_preview += '...';\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\titem.parameters_preview = '参数解析失败';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (that.pagenum === 1) {\n\t\t\t\t\t\tthat.logsList = data;\n\t\t\t\t\t\tif (data.length === 0) {\n\t\t\t\t\t\t\tthat.nodata = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (data.length === 0) {\n\t\t\t\t\t\t\tthat.nomore = true;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthat.logsList = that.logsList.concat(data);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\tif (that.pagenum === 1) {\n\t\t\t\t\t\tthat.nodata = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\n\t\t// 显示工作流筛选\n\t\tshowWorkflowFilter: function() {\n\t\t\tthis.$refs.workflowFilterPopup.open();\n\t\t},\n\n\t\t// 关闭工作流筛选\n\t\tcloseWorkflowFilter: function() {\n\t\t\tthis.$refs.workflowFilterPopup.close();\n\t\t},\n\n\t\t// 选择工作流筛选\n\t\tselectWorkflowFilter: function(e) {\n\t\t\tvar workflowData = e.currentTarget.dataset.workflow;\n\t\t\tif (workflowData) {\n\t\t\t\tthis.selectedWorkflow = JSON.parse(workflowData);\n\t\t\t} else {\n\t\t\t\tthis.selectedWorkflow = null;\n\t\t\t}\n\t\t\tthis.getLogsList();\n\t\t\tthis.closeWorkflowFilter();\n\t\t},\n\n\t\t// 显示日志详情\n\t\tshowLogDetail: function(e) {\n\t\t\tvar log = JSON.parse(e.currentTarget.dataset.log);\n\t\t\tthis.currentLog = log;\n\t\t\tthis.$refs.logDetailPopup.open();\n\t\t},\n\n\t\t// 关闭日志详情\n\t\tcloseLogDetail: function() {\n\t\t\tthis.$refs.logDetailPopup.close();\n\t\t},\n\n\t\t// 格式化JSON\n\t\tformatJson: function(jsonStr) {\n\t\t\ttry {\n\t\t\t\tvar obj = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;\n\t\t\t\treturn JSON.stringify(obj, null, 2);\n\t\t\t} catch (e) {\n\t\t\t\treturn jsonStr;\n\t\t\t}\n\t\t},\n\n\t\t// 格式化时间\n\t\tformatTime: function(timestamp) {\n\t\t\tvar date = new Date(timestamp * 1000);\n\t\t\treturn date.getFullYear() + '-' + \n\t\t\t\t   (date.getMonth() + 1).toString().padStart(2, '0') + '-' + \n\t\t\t\t   date.getDate().toString().padStart(2, '0') + ' ' +\n\t\t\t\t   date.getHours().toString().padStart(2, '0') + ':' + \n\t\t\t\t   date.getMinutes().toString().padStart(2, '0');\n\t\t}\n\t}\n};\n</script>\n\n<style>\n.container {\n\tbackground: #f5f5f5;\n\tmin-height: 100vh;\n}\n\n.header {\n\tbackground: #fff;\n\tpadding: 20rpx 30rpx;\n\tborder-bottom: 1rpx solid #eee;\n\tposition: fixed;\n\ttop: var(--window-top);\n\tleft: 0;\n\tright: 0;\n\tz-index: 100;\n}\n\n.header-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\ttext-align: center;\n}\n\n.filter-bar {\n\tbackground: #fff;\n\tpadding: 20rpx 30rpx;\n\tborder-bottom: 1rpx solid #eee;\n\tmargin-top: 120rpx;\n}\n\n.filter-item {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 20rpx;\n\tbackground: #f5f5f5;\n\tborder-radius: 12rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.logs-list {\n\tpadding: 30rpx;\n}\n\n.log-item {\n\tbackground: #fff;\n\tborder-radius: 16rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.log-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.log-workflow {\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tflex: 1;\n}\n\n.log-status {\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 24rpx;\n}\n\n.log-status.success {\n\tcolor: #52c41a;\n}\n\n.log-status.failed {\n\tcolor: #ff4d4f;\n}\n\n.log-status .iconfont {\n\tmargin-right: 8rpx;\n\tfont-size: 28rpx;\n}\n\n.log-time {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tmargin-bottom: 15rpx;\n}\n\n.log-params {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tline-height: 1.4;\n}\n\n.log-params .label {\n\tcolor: #999;\n}\n\n.log-params .value {\n\tcolor: #333;\n}\n\n.filter-popup {\n\tbackground: #fff;\n\tborder-radius: 20rpx 20rpx 0 0;\n\tmax-height: 60vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.popup-header {\n\tpadding: 30rpx;\n\tborder-bottom: 1rpx solid #eee;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.popup-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.popup-close {\n\tfont-size: 32rpx;\n\tcolor: #999;\n}\n\n.filter-options {\n\tflex: 1;\n\tpadding: 0 30rpx;\n}\n\n.filter-option {\n\tpadding: 30rpx 0;\n\tborder-bottom: 1rpx solid #f5f5f5;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.detail-popup {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\twidth: 90vw;\n\tmax-height: 80vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.detail-content {\n\tflex: 1;\n\tpadding: 30rpx;\n\tmax-height: 60vh;\n}\n\n.detail-section {\n\tmargin-bottom: 40rpx;\n}\n\n.section-title {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n\tpadding-bottom: 10rpx;\n\tborder-bottom: 1rpx solid #eee;\n}\n\n.detail-item {\n\tdisplay: flex;\n\tmargin-bottom: 15rpx;\n\tfont-size: 26rpx;\n}\n\n.detail-item .label {\n\tcolor: #999;\n\twidth: 160rpx;\n\tflex-shrink: 0;\n}\n\n.detail-item .value {\n\tcolor: #333;\n\tflex: 1;\n\tword-break: break-all;\n}\n\n.detail-item .value.success {\n\tcolor: #52c41a;\n}\n\n.detail-item .value.failed {\n\tcolor: #ff4d4f;\n}\n\n.code-block {\n\tbackground: #f5f5f5;\n\tborder-radius: 8rpx;\n\tpadding: 20rpx;\n\tfont-size: 22rpx;\n\tcolor: #333;\n\tline-height: 1.5;\n\twhite-space: pre-wrap;\n\tword-break: break-all;\n\tmax-height: 400rpx;\n\toverflow-y: auto;\n}\n\n.popup-footer {\n\tpadding: 30rpx;\n\tborder-top: 1rpx solid #eee;\n}\n\n.btn-confirm {\n\twidth: 100%;\n\theight: 80rpx;\n\tline-height: 80rpx;\n\ttext-align: center;\n\tborder-radius: 12rpx;\n\tfont-size: 28rpx;\n\tcolor: #fff;\n}\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./workflow-logs.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./workflow-logs.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753981238494\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}