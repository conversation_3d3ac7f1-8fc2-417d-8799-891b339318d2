<?php
/**
 * 梦想启蒙数据库更新脚本
 * 用于添加缺失的 user_image_url 字段
 */

// 数据库配置
$host = 'localhost';
$username = 'root';
$password = 'root123';
$database = 'qixian_zhongheng';

try {
    // 连接数据库
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "连接数据库成功！\n";
    
    // 检查表是否存在
    $checkTable = $pdo->query("SHOW TABLES LIKE 'ddwx_dream_inspiration_records'");
    if ($checkTable->rowCount() == 0) {
        echo "表 ddwx_dream_inspiration_records 不存在，需要先创建表！\n";
        echo "请先执行完整的 dream_inspiration.sql 脚本。\n";
        exit(1);
    }
    
    // 检查字段是否存在
    $checkColumn = $pdo->query("SHOW COLUMNS FROM ddwx_dream_inspiration_records LIKE 'user_image_url'");
    if ($checkColumn->rowCount() > 0) {
        echo "字段 user_image_url 已存在，无需更新。\n";
    } else {
        echo "字段 user_image_url 不存在，正在添加...\n";
        
        // 添加字段
        $sql = "ALTER TABLE `ddwx_dream_inspiration_records` 
                ADD COLUMN `user_image_url` varchar(500) NOT NULL DEFAULT '' COMMENT '用户上传的图片URL' 
                AFTER `dream_content`";
        
        $pdo->exec($sql);
        echo "字段 user_image_url 添加成功！\n";
    }
    
    // 检查设置表是否存在
    $checkSettingsTable = $pdo->query("SHOW TABLES LIKE 'ddwx_dream_inspiration_settings'");
    if ($checkSettingsTable->rowCount() == 0) {
        echo "创建设置表 ddwx_dream_inspiration_settings...\n";
        
        $createSettingsTable = "
        CREATE TABLE `ddwx_dream_inspiration_settings` (
          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '设置ID',
          `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
          `workflow_id` varchar(100) NOT NULL DEFAULT '' COMMENT '工作流ID',
          `workflow_name` varchar(100) NOT NULL DEFAULT '' COMMENT '工作流名称',
          `is_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用：0禁用 1启用',
          `welcome_text` text NOT NULL COMMENT '欢迎文案',
          `prompt_template` text NOT NULL COMMENT '提示词模板',
          `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
          `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
          PRIMARY KEY (`id`),
          UNIQUE KEY `aid` (`aid`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='梦想启蒙设置表'";
        
        $pdo->exec($createSettingsTable);
        echo "设置表创建成功！\n";
        
        // 插入默认设置
        $insertSettings = "
        INSERT INTO `ddwx_dream_inspiration_settings` 
        (`aid`, `workflow_id`, `workflow_name`, `is_enabled`, `welcome_text`, `prompt_template`, `create_time`, `update_time`) 
        VALUES (1, '', '梦想启蒙工作流', 1, '欢迎使用梦想启蒙功能！上传您的照片，告诉我们您的梦想，我们将为您生成专属的梦想图片。', '请为一个{gender}性生成梦想图片，梦想内容：{dream}', " . time() . ", " . time() . ")";
        
        $pdo->exec($insertSettings);
        echo "默认设置插入成功！\n";
    } else {
        echo "设置表已存在。\n";
    }
    
    // 检查扣子工作流表
    $checkCozeTable = $pdo->query("SHOW TABLES LIKE 'ddwx_coze_workflow'");
    if ($checkCozeTable->rowCount() == 0) {
        echo "创建扣子工作流表...\n";
        
        $createCozeTable = "
        CREATE TABLE `ddwx_coze_workflow` (
          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
          `aid` int(11) NOT NULL COMMENT '应用ID',
          `name` varchar(100) NOT NULL COMMENT '工作流名称',
          `workflow_id` varchar(100) NOT NULL COMMENT '工作流ID',
          `description` text COMMENT '工作流描述',
          `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0=禁用,1=启用)',
          `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间戳',
          `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间戳',
          PRIMARY KEY (`id`),
          KEY `idx_aid` (`aid`),
          KEY `idx_workflow_id` (`workflow_id`),
          KEY `idx_status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子工作流配置表'";
        
        $pdo->exec($createCozeTable);
        echo "扣子工作流表创建成功！\n";
    } else {
        echo "扣子工作流表已存在。\n";
    }
    
    echo "\n=== 数据库更新完成！===\n";
    echo "现在可以正常使用梦想启蒙功能了。\n";
    
} catch (PDOException $e) {
    echo "数据库错误: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}
?>
