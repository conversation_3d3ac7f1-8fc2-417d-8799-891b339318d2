<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

// +----------------------------------------------------------------------
// | 梦想启蒙管理
// +----------------------------------------------------------------------
namespace app\controller;
use think\facade\Db;
use think\facade\View;

class DreamInspiration extends Common
{
    //梦想记录列表
    public function index(){
        if(request()->isAjax()){
            $page = input('param.page');
            $limit = input('param.limit');
            if(input('param.field') && input('param.order')){
                $order = input('param.field').' '.input('param.order');
            }else{
                $order = 'id desc';
            }
            $where = array();
            $where[] = ['aid','=',aid];
            
            if(input('param.nickname')) $where[] = ['nickname','like','%'.input('param.nickname').'%'];
            if(input('param.gender')) $where[] = ['gender','=',input('param.gender')];
            if(input('param.status')) $where[] = ['status','=',input('param.status')];
            
            $count = 0 + Db::name('dream_inspiration_records')->where($where)->count();
            $data = Db::name('dream_inspiration_records')->where($where)->page($page,$limit)->order($order)->select()->toArray();
            
            foreach($data as $k=>$v){
                $data[$k]['gender_text'] = $v['gender'] == 1 ? '男' : '女';
                $data[$k]['status_text'] = $v['status'] == 1 ? '已生成' : '生成中';
                $data[$k]['create_time_text'] = date('Y-m-d H:i:s', $v['create_time']);
                $data[$k]['update_time_text'] = $v['update_time'] ? date('Y-m-d H:i:s', $v['update_time']) : '';
            }
            return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
        }
        return View::fetch();
    }
    
    //查看详情
    public function detail(){
        $id = input('id');
        if(!$id) return json(['code'=>0,'msg'=>'参数错误']);
        
        $data = Db::name('dream_inspiration_records')->where(['id'=>$id,'aid'=>aid])->find();
        if(!$data) return json(['code'=>0,'msg'=>'记录不存在']);
        
        $data['gender_text'] = $data['gender'] == 1 ? '男' : '女';
        $data['status_text'] = $data['status'] == 1 ? '已生成' : '生成中';
        $data['create_time_text'] = date('Y-m-d H:i:s', $data['create_time']);
        $data['update_time_text'] = $data['update_time'] ? date('Y-m-d H:i:s', $data['update_time']) : '';
        
        return View::fetch('detail', ['data' => $data]);
    }
    
    //删除记录
    public function del(){
        $id = input('id');
        if(!$id) return json(['code'=>0,'msg'=>'参数错误']);
        
        $data = Db::name('dream_inspiration_records')->where(['id'=>$id,'aid'=>aid])->find();
        if(!$data) return json(['code'=>0,'msg'=>'记录不存在']);
        
        Db::name('dream_inspiration_records')->where(['id'=>$id,'aid'=>aid])->delete();
        \app\common\System::plog('删除梦想启蒙记录：'.$data['nickname'].'的梦想');
        
        return json(['code'=>1,'msg'=>'删除成功']);
    }
    
    //批量删除
    public function delall(){
        $ids = input('ids');
        if(!$ids) return json(['code'=>0,'msg'=>'请选择要删除的记录']);
        
        $ids = explode(',', $ids);
        $count = Db::name('dream_inspiration_records')->where(['aid'=>aid])->whereIn('id', $ids)->delete();
        \app\common\System::plog('批量删除梦想启蒙记录：'.$count.'条');
        
        return json(['code'=>1,'msg'=>'删除成功，共删除'.$count.'条记录']);
    }
    
    //启蒙设置
    public function setting(){
        if(request()->isPost()){
            $data = input('post.');
            
            // 验证必填字段
            if(empty($data['workflow_id'])) return json(['code'=>0,'msg'=>'请选择工作流']);
            
            // 保存设置
            $setting = [
                'aid' => aid,
                'workflow_id' => $data['workflow_id'],
                'workflow_name' => $data['workflow_name'],
                'is_enabled' => $data['is_enabled'] ?? 0,
                'welcome_text' => $data['welcome_text'] ?? '欢迎使用梦想启蒙功能',
                'prompt_template' => $data['prompt_template'] ?? '请根据用户性别：{gender}，梦想：{dream}，生成一张励志图片',
                'future_talk_enabled' => $data['future_talk_enabled'] ?? 0,
                'future_talk_male_url' => $data['future_talk_male_url'] ?? '',
                'future_talk_female_url' => $data['future_talk_female_url'] ?? '',
                'future_talk_button_text' => $data['future_talk_button_text'] ?? '与未来对话',
                'update_time' => time()
            ];
            
            // 检查是否已存在设置
            $exists = Db::name('dream_inspiration_settings')->where(['aid'=>aid])->find();
            if($exists){
                Db::name('dream_inspiration_settings')->where(['aid'=>aid])->update($setting);
                \app\common\System::plog('更新梦想启蒙设置');
            }else{
                $setting['create_time'] = time();
                Db::name('dream_inspiration_settings')->insert($setting);
                \app\common\System::plog('创建梦想启蒙设置');
            }
            
            return json(['code'=>1,'msg'=>'设置保存成功']);
        }
        
        // 获取当前设置
        $setting = Db::name('dream_inspiration_settings')->where(['aid'=>aid])->find();
        if(!$setting){
            $setting = [
                'workflow_id' => '',
                'workflow_name' => '',
                'is_enabled' => 0,
                'welcome_text' => '欢迎使用梦想启蒙功能',
                'prompt_template' => '请根据用户性别：{gender}，梦想：{dream}，生成一张励志图片',
                'future_talk_enabled' => 0,
                'future_talk_male_url' => '',
                'future_talk_female_url' => '',
                'future_talk_button_text' => '与未来对话'
            ];
        }
        
        // 获取可用的工作流列表
        $workflows = Db::name('coze_workflow')->where(['aid'=>aid,'status'=>1])->select()->toArray();
        
        return View::fetch('setting', ['setting' => $setting, 'workflows' => $workflows]);
    }
}
