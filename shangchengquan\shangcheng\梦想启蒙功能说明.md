# 梦想启蒙功能说明

## 功能概述

梦想启蒙功能是一个基于扣子(Coze) AI工作流的图片生成功能，用户可以输入自己的性别和梦想内容，系统会调用AI工作流生成专属的励志图片。

## 功能特点

1. **智能图片生成**：基于用户性别和梦想内容，通过AI工作流生成个性化励志图片
2. **异步处理**：支持异步生成，提升用户体验
3. **记录管理**：完整的生成记录管理，包括成功和失败记录
4. **灵活配置**：支持自定义提示词模板和工作流选择
5. **前后端分离**：提供完整的前端界面和后端管理功能

## 系统架构

### 后端管理
- **控制器**：`app/controller/DreamInspiration.php`
- **视图文件**：`app/home/<USER>/`
- **菜单配置**：已集成到扩展功能菜单中

### 前端接口
- **API控制器**：`app/controller/ApiDreamInspiration.php`
- **自动处理**：`app/controller/ApiAuto.php` 中的 `processDreamInspiration` 方法

### 数据库表
- **记录表**：`ddwx_dream_inspiration_records` - 存储用户的梦想记录和生成结果
- **设置表**：`ddwx_dream_inspiration_settings` - 存储功能配置信息

### 前端页面
- **主页面**：`/pagesExt/bpv/index` - 用户输入梦想和查看结果的页面

## 配置步骤

### 1. 数据库初始化
执行 `dream_inspiration.sql` 文件中的SQL语句创建相关数据表：

```sql
-- 创建梦想启蒙记录表
CREATE TABLE `ddwx_dream_inspiration_records` (...)

-- 创建梦想启蒙设置表  
CREATE TABLE `ddwx_dream_inspiration_settings` (...)
```

### 2. 扣子工作流配置
1. 在后台 "扩展 -> Coze API -> API配置" 中配置扣子API
2. 在 "工作流管理" 中添加用于图片生成的工作流
3. 确保工作流支持文本输入并返回图片URL

### 3. 梦想启蒙设置
1. 进入后台 "扩展 -> 梦想启蒙 -> 启蒙设置"
2. 启用功能开关
3. 选择对应的工作流
4. 配置欢迎文案和提示词模板

### 4. 自动处理任务（可选）
如需启用自动处理，可设置定时任务：
```bash
# 每分钟执行一次梦想启蒙自动处理
*/1 * * * * curl "https://您的域名/?s=/ApiAuto/processDreamInspiration/aid/1"
```

## API接口说明

### 1. 获取设置信息
- **接口**：`/ApiDreamInspiration/getSetting`
- **方法**：GET
- **返回**：功能开关状态、欢迎文案等配置信息

### 2. 生成梦想图片
- **接口**：`/ApiDreamInspiration/generateImage`
- **方法**：POST
- **参数**：
  - `gender`: 性别（0女 1男）
  - `dream_content`: 梦想内容
- **返回**：提交成功信息和记录ID

### 3. 获取我的记录
- **接口**：`/ApiDreamInspiration/getMyRecords`
- **方法**：GET
- **参数**：
  - `page`: 页码（可选，默认1）
  - `limit`: 每页数量（可选，默认10）
- **返回**：用户的梦想记录列表

### 4. 获取记录详情
- **接口**：`/ApiDreamInspiration/getRecordDetail`
- **方法**：GET
- **参数**：
  - `record_id`: 记录ID
- **返回**：记录的详细信息

## 后台管理功能

### 1. 梦想记录管理
- **路径**：扩展 -> 梦想启蒙 -> 梦想记录
- **功能**：
  - 查看所有用户的梦想记录
  - 按昵称、性别、状态筛选
  - 查看记录详情
  - 删除记录

### 2. 启蒙设置
- **路径**：扩展 -> 梦想启蒙 -> 启蒙设置
- **功能**：
  - 开启/关闭功能
  - 选择工作流
  - 配置欢迎文案
  - 设置提示词模板

## 提示词模板说明

提示词模板支持以下变量：
- `{gender}`: 用户性别（男/女）
- `{dream}`: 用户输入的梦想内容

示例模板：
```
请根据用户性别：{gender}，梦想：{dream}，生成一张励志图片
```

## 工作流要求

扣子工作流需要满足以下要求：
1. 支持文本输入参数（通常命名为 `prompt`）
2. 返回结果中包含图片URL
3. 返回格式建议为JSON，包含 `image_url` 字段

## 前端集成

前端页面位于 `/pagesExt/bpv/index`，需要：
1. 调用 `getSetting` 接口检查功能是否开启
2. 提供性别选择和梦想输入界面
3. 调用 `generateImage` 接口提交梦想
4. 通过 `getMyRecords` 和 `getRecordDetail` 展示历史记录

## 注意事项

1. **权限控制**：所有接口都需要用户登录
2. **异步处理**：图片生成是异步的，需要轮询或推送来更新状态
3. **错误处理**：需要处理工作流执行失败的情况
4. **资源管理**：注意图片存储和带宽消耗
5. **用户体验**：建议在生成过程中显示加载状态

## 扩展功能

可以考虑的扩展功能：
1. 图片风格选择
2. 批量生成
3. 社交分享
4. 收藏功能
5. 评论系统

## 技术支持

如有问题，请检查：
1. 扣子API配置是否正确
2. 工作流是否正常运行
3. 数据库表是否创建成功
4. 日志文件中的错误信息
