# 梦想启蒙工作流参数格式说明

## 📋 **工作流参数格式**

根据您提供的参数格式，梦想启蒙功能现在使用以下参数结构调用Coze工作流：

### **标准参数格式**
```json
{
  "BOT_USER_INPUT": "",
  "gender": "男",
  "image_url": "https://p9-bot-workflow-sign.byteimg.com/tos-cn-i-mdko3gqilj/3a2b90b00c3847b789552acd74616d11.jpg~tplv-mdko3gqilj-image.image?rk3s=81d4c505&x-expires=1781840170&x-signature=MpZX%2FHwnljK5BWi025xiPjzXEqM%3D&x-wf-file_name=%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250526170910.jpg",
  "zhiye": "医生"
}
```

### **参数说明**

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `BOT_USER_INPUT` | string | 机器人用户输入（固定为空字符串） | `""` |
| `gender` | string | 用户性别（中文） | `"男"` 或 `"女"` |
| `image_url` | string | 用户上传的图片URL | 完整的图片链接 |
| `zhiye` | string | 用户职业 | `"医生"`、`"教师"`、`"工程师"` 等 |

## 🔧 **后端实现**

### **参数构建逻辑**
```php
// 获取用户职业信息
$userInfo = $this->member;
$profession = $userInfo['zhiye'] ?? '医生'; // 默认职业为医生

// 按照标准格式准备工作流参数
$parameters = [
    'BOT_USER_INPUT' => '', // 空字符串
    'gender' => $gender == 1 ? '男' : '女', // 转换为中文
    'image_url' => $imageUrl,
    'zhiye' => $profession // 职业信息
];
```

### **性别转换**
- 前端传递：`gender: 1`（男）或 `gender: 0`（女）
- 工作流参数：`"gender": "男"` 或 `"gender": "女"`

### **职业获取**
- 从用户信息中获取：`$userInfo['zhiye']`
- 默认值：`"医生"`
- 可扩展支持更多职业类型

## 📝 **前端调用示例**

### **API调用**
```javascript
const requestData = {
    image_url: this.capturedImageUrl,
    dream_content: this.dreamContent,
    gender: this.userGender === '男' ? 1 : 0
};

app.post('ApiDreamInspiration/generateImage', requestData, (res) => {
    if (res.code == 1) {
        // 开始轮询查询状态
        this.checkGenerationStatus(res.data.record_id);
    }
});
```

## 🔍 **调试信息**

### **日志记录**
系统会记录以下调试信息：
1. **工作流调用参数**：完整的参数结构
2. **用户信息**：性别、职业等
3. **调用结果**：工作流返回的结果

### **日志查看**
```php
\app\common\System::plog('梦想启蒙开始调用工作流', [
    'record_id' => $recordId,
    'workflow_id' => $workflowId,
    'image_url' => $imageUrl,
    'dream_content' => $dreamContent,
    'gender' => $gender,
    'profession' => $profession
]);
```

## ⚠️ **注意事项**

### **1. 参数格式严格匹配**
- 必须使用中文性别：`"男"`、`"女"`
- `BOT_USER_INPUT` 必须为空字符串
- 职业字段名为 `zhiye`

### **2. 用户信息依赖**
- 需要用户表中有 `zhiye` 字段存储职业信息
- 如果用户未设置职业，使用默认值 `"医生"`

### **3. 图片URL格式**
- 必须是完整的可访问URL
- 支持Coze工作流的图片格式要求

## 🚀 **测试验证**

### **测试步骤**
1. 确保用户已设置性别和职业信息
2. 上传图片并输入梦想内容
3. 调用生成接口
4. 查看后台日志确认参数格式正确
5. 验证工作流调用成功

### **预期结果**
- 工作流接收到正确格式的参数
- 根据用户性别和职业生成个性化图片
- 前端能够正常轮询并获取结果

## 📊 **参数映射对照表**

| 前端输入 | 数据库存储 | 工作流参数 |
|----------|------------|------------|
| `gender: 1` | `gender: 1` | `"gender": "男"` |
| `gender: 0` | `gender: 0` | `"gender": "女"` |
| 用户上传图片 | `user_image_url` | `"image_url": "完整URL"` |
| 用户职业设置 | `zhiye` | `"zhiye": "医生"` |
| 固定值 | - | `"BOT_USER_INPUT": ""` |

现在梦想启蒙功能完全按照您提供的参数格式调用工作流，确保与Coze工作流的完美兼容！🎯
