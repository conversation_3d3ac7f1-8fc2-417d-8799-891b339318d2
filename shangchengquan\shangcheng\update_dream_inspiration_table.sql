-- 梦想启蒙数据库表更新脚本
-- 添加缺失的 user_image_url 字段

-- 检查表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS `ddwx_dream_inspiration_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `mid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `nickname` varchar(100) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '用户头像',
  `gender` tinyint(1) NOT NULL DEFAULT '0' COMMENT '性别：0女 1男',
  `dream_content` text NOT NULL COMMENT '梦想内容',
  `user_image_url` varchar(500) NOT NULL DEFAULT '' COMMENT '用户上传的图片URL',
  `workflow_id` varchar(100) NOT NULL DEFAULT '' COMMENT '工作流ID',
  `workflow_name` varchar(100) NOT NULL DEFAULT '' COMMENT '工作流名称',
  `execute_id` varchar(100) NOT NULL DEFAULT '' COMMENT '工作流执行ID',
  `prompt_text` text NOT NULL COMMENT '发送给工作流的提示词',
  `result_image` varchar(500) NOT NULL DEFAULT '' COMMENT '生成的图片URL',
  `workflow_result` text COMMENT '工作流返回的完整结果',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0生成中 1已生成',
  `error_msg` varchar(500) NOT NULL DEFAULT '' COMMENT '错误信息',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_mid` (`mid`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='梦想启蒙记录表';

-- 添加 user_image_url 字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'ddwx_dream_inspiration_records' 
     AND COLUMN_NAME = 'user_image_url') = 0,
    'ALTER TABLE `ddwx_dream_inspiration_records` ADD COLUMN `user_image_url` varchar(500) NOT NULL DEFAULT \'\' COMMENT \'用户上传的图片URL\' AFTER `dream_content`;',
    'SELECT \'Column user_image_url already exists\' as message;'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 execute_id 字段（如果不存在）
SET @sql2 = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'ddwx_dream_inspiration_records'
     AND COLUMN_NAME = 'execute_id') = 0,
    'ALTER TABLE `ddwx_dream_inspiration_records` ADD COLUMN `execute_id` varchar(100) NOT NULL DEFAULT \'\' COMMENT \'工作流执行ID\' AFTER `workflow_name`;',
    'SELECT \'Column execute_id already exists\' as message;'
));

PREPARE stmt2 FROM @sql2;
EXECUTE stmt2;
DEALLOCATE PREPARE stmt2;

-- 创建设置表（如果不存在）
CREATE TABLE IF NOT EXISTS `ddwx_dream_inspiration_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `workflow_id` varchar(100) NOT NULL DEFAULT '' COMMENT '工作流ID',
  `workflow_name` varchar(100) NOT NULL DEFAULT '' COMMENT '工作流名称',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用：0禁用 1启用',
  `welcome_text` text NOT NULL COMMENT '欢迎文案',
  `prompt_template` text NOT NULL COMMENT '提示词模板',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `aid` (`aid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='梦想启蒙设置表';

-- 插入默认设置数据（如果不存在）
INSERT IGNORE INTO `ddwx_dream_inspiration_settings` (`aid`, `workflow_id`, `workflow_name`, `is_enabled`, `welcome_text`, `prompt_template`, `create_time`, `update_time`) VALUES
(1, '', '梦想启蒙工作流', 1, '欢迎使用梦想启蒙功能！上传您的照片，告诉我们您的梦想，我们将为您生成专属的梦想图片。', '请为一个{gender}性生成梦想图片，梦想内容：{dream}', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 创建扣子工作流表（如果不存在）
CREATE TABLE IF NOT EXISTS `ddwx_coze_workflow` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL COMMENT '应用ID',
  `name` varchar(100) NOT NULL COMMENT '工作流名称',
  `workflow_id` varchar(100) NOT NULL COMMENT '工作流ID',
  `description` text COMMENT '工作流描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0=禁用,1=启用)',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间戳',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_workflow_id` (`workflow_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子工作流配置表';

-- 创建扣子工作流执行日志表（如果不存在）
CREATE TABLE IF NOT EXISTS `ddwx_coze_workflow_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `mid` int(11) DEFAULT '0' COMMENT '会员ID',
  `workflow_id` varchar(100) NOT NULL COMMENT '工作流ID',
  `parameters` text COMMENT '执行参数',
  `result` text COMMENT '执行结果',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态(0=失败,1=成功)',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_mid` (`mid`),
  KEY `idx_workflow_id` (`workflow_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扣子工作流执行日志表';

-- 显示更新完成信息
SELECT '梦想启蒙数据库表更新完成！' as message;
