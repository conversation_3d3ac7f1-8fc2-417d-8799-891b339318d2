<template>
<view class="container">
	<block v-if="isload">
		<!-- 顶部导航 -->
		<view class="header">
			<view class="header-title">扣子AI助手</view>
			<view class="header-actions">
				<view class="action-btn" @tap="goto" data-url="/pagesB/coze/history">
					<text class="iconfont iconlishi"></text>
					<text>历史记录</text>
				</view>
			</view>
		</view>

		<!-- 功能卡片 -->
		<view class="feature-cards">
			<!-- AI聊天卡片 -->
			<view class="feature-card chat-card" @tap="goto" data-url="/pagesB/coze/chat">
				<view class="card-icon">
					<text class="iconfont iconliaotian" :style="{color: t('color1')}"></text>
				</view>
				<view class="card-content">
					<view class="card-title">AI智能聊天</view>
					<view class="card-desc">与AI机器人进行智能对话，获取专业解答</view>
				</view>
				<view class="card-arrow">
					<text class="iconfont iconjiantou-you"></text>
				</view>
			</view>

			<!-- 工作流卡片 -->
			<view class="feature-card workflow-card" @tap="goto" data-url="/pagesB/coze/workflow">
				<view class="card-icon">
					<text class="iconfont icongongzuoliu" :style="{color: '#52c41a'}"></text>
				</view>
				<view class="card-content">
					<view class="card-title">智能工作流</view>
					<view class="card-desc">运行自动化工作流，提升工作效率</view>
				</view>
				<view class="card-arrow">
					<text class="iconfont iconjiantou-you"></text>
				</view>
			</view>

			<!-- 文件处理卡片 -->
			<view class="feature-card file-card" @tap="goto" data-url="/pagesB/coze/file-upload">
				<view class="card-icon">
					<text class="iconfont iconwenjian" :style="{color: '#1890ff'}"></text>
				</view>
				<view class="card-content">
					<view class="card-title">文件处理</view>
					<view class="card-desc">上传文件进行AI分析和处理</view>
				</view>
				<view class="card-arrow">
					<text class="iconfont iconjiantou-you"></text>
				</view>
			</view>

			<!-- 使用统计卡片 -->
			<view class="feature-card stats-card" @tap="showStats">
				<view class="card-icon">
					<text class="iconfont icontongji" :style="{color: '#722ed1'}"></text>
				</view>
				<view class="card-content">
					<view class="card-title">使用统计</view>
					<view class="card-desc">查看API调用统计和使用情况</view>
				</view>
				<view class="card-arrow">
					<text class="iconfont iconjiantou-you"></text>
				</view>
			</view>
		</view>

		<!-- 快速入口 -->
		<view class="quick-actions">
			<view class="section-title">快速入口</view>
			<view class="action-grid">
				<view class="action-item" @tap="goto" data-url="/pagesB/coze/chat">
					<view class="action-icon">
						<text class="iconfont iconkuaisuliaotian"></text>
					</view>
					<view class="action-text">快速聊天</view>
				</view>
				<view class="action-item" @tap="goto" data-url="/pagesB/coze/workflow-logs">
					<view class="action-icon">
						<text class="iconfont iconjilu"></text>
					</view>
					<view class="action-text">执行记录</view>
				</view>
				<view class="action-item" @tap="showHelp">
					<view class="action-icon">
						<text class="iconfont iconbangzhu"></text>
					</view>
					<view class="action-text">使用帮助</view>
				</view>
				<view class="action-item" @tap="showSettings">
					<view class="action-icon">
						<text class="iconfont iconshezhi"></text>
					</view>
					<view class="action-text">设置</view>
				</view>
			</view>
		</view>

		<!-- 使用统计弹窗 -->
		<uni-popup ref="statsPopup" type="center">
			<view class="stats-popup">
				<view class="popup-header">
					<view class="popup-title">使用统计</view>
					<view class="popup-close" @tap="closeStats">
						<text class="iconfont iconguanbi"></text>
					</view>
				</view>
				<view class="stats-content">
					<view class="stats-item">
						<view class="stats-label">总请求次数</view>
						<view class="stats-value" :style="{color: t('color1')}">{{statsData.total_requests || 0}}</view>
					</view>
					<view class="stats-item">
						<view class="stats-label">成功次数</view>
						<view class="stats-value" style="color: #52c41a">{{statsData.success_requests || 0}}</view>
					</view>
					<view class="stats-item">
						<view class="stats-label">失败次数</view>
						<view class="stats-value" style="color: #ff4d4f">{{statsData.failed_requests || 0}}</view>
					</view>
					<view class="stats-item">
						<view class="stats-label">成功率</view>
						<view class="stats-value" :style="{color: t('color1')}">{{statsData.success_rate || 0}}%</view>
					</view>
				</view>
				<view class="popup-footer">
					<view class="btn-confirm" :style="{background: t('color1')}" @tap="closeStats">确定</view>
				</view>
			</view>
		</uni-popup>

		<!-- 帮助弹窗 -->
		<uni-popup ref="helpPopup" type="bottom">
			<view class="help-popup">
				<view class="popup-header">
					<view class="popup-title">使用帮助</view>
					<view class="popup-close" @tap="closeHelp">
						<text class="iconfont iconguanbi"></text>
					</view>
				</view>
				<scroll-view class="help-content" scroll-y="true">
					<view class="help-section">
						<view class="help-title">AI智能聊天</view>
						<view class="help-text">• 选择合适的AI机器人进行对话</view>
						<view class="help-text">• 输入问题或需求，获取智能回答</view>
						<view class="help-text">• 支持上下文对话，记忆对话历史</view>
					</view>
					<view class="help-section">
						<view class="help-title">智能工作流</view>
						<view class="help-text">• 选择预设的工作流模板</view>
						<view class="help-text">• 配置工作流参数</view>
						<view class="help-text">• 支持同步和异步执行</view>
					</view>
					<view class="help-section">
						<view class="help-title">文件处理</view>
						<view class="help-text">• 上传文档、图片等文件</view>
						<view class="help-text">• AI自动分析文件内容</view>
						<view class="help-text">• 获取处理结果和建议</view>
					</view>
				</scroll-view>
			</view>
		</uni-popup>
	</block>
	<loading v-if="loading"></loading>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			isload: false,
			loading: false,
			statsData: {}
		};
	},

	onLoad: function(opt) {
		this.loaded();
	},

	onShow: function() {
		// 页面显示时可以刷新统计数据
		this.getStats();
	},

	methods: {
		// 获取使用统计
		getStats: function() {
			var that = this;
			app.post('ApiCoze/getstats', {}, function(res) {
				if (res.code === 1) {
					that.statsData = res.data || {};
				}
			});
		},

		// 显示统计
		showStats: function() {
			this.getStats();
			this.$refs.statsPopup.open();
		},

		// 关闭统计
		closeStats: function() {
			this.$refs.statsPopup.close();
		},

		// 显示帮助
		showHelp: function() {
			this.$refs.helpPopup.open();
		},

		// 关闭帮助
		closeHelp: function() {
			this.$refs.helpPopup.close();
		},

		// 显示设置
		showSettings: function() {
			uni.showToast({
				title: '设置功能开发中...',
				icon: 'none',
				duration: 2000
			});
		}
	}
};
</script>

<style>
.container {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
}

.header {
	background: rgba(255,255,255,0.1);
	backdrop-filter: blur(10px);
	padding: 20rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: fixed;
	top: var(--window-top);
	left: 0;
	right: 0;
	z-index: 100;
}

.header-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #fff;
}

.header-actions {
	display: flex;
	align-items: center;
}

.action-btn {
	display: flex;
	align-items: center;
	padding: 10rpx 20rpx;
	background: rgba(255,255,255,0.2);
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #fff;
}

.action-btn .iconfont {
	margin-right: 10rpx;
	font-size: 28rpx;
}

.feature-cards {
	padding: 30rpx;
	margin-top: 120rpx;
}

.feature-card {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 8rpx 30rpx rgba(0,0,0,0.1);
}

.card-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 30rpx;
}

.card-icon .iconfont {
	font-size: 40rpx;
}

.card-content {
	flex: 1;
}

.card-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.card-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.card-arrow {
	color: #ccc;
	font-size: 28rpx;
}

.quick-actions {
	background: #fff;
	margin: 0 30rpx;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0,0,0,0.1);
}

.section-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.action-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 30rpx;
}

.action-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.action-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 15rpx;
}

.action-icon .iconfont {
	font-size: 36rpx;
	color: #666;
}

.action-text {
	font-size: 22rpx;
	color: #666;
}

.stats-popup {
	background: #fff;
	border-radius: 20rpx;
	width: 80vw;
	max-width: 600rpx;
}

.popup-header {
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 32rpx;
	color: #999;
}

.stats-content {
	padding: 30rpx;
}

.stats-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}

.stats-item:last-child {
	border-bottom: none;
}

.stats-label {
	font-size: 28rpx;
	color: #666;
}

.stats-value {
	font-size: 32rpx;
	font-weight: bold;
}

.popup-footer {
	padding: 30rpx;
	border-top: 1rpx solid #eee;
}

.btn-confirm {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #fff;
}

.help-popup {
	background: #fff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 70vh;
	display: flex;
	flex-direction: column;
}

.help-content {
	flex: 1;
	padding: 30rpx;
}

.help-section {
	margin-bottom: 40rpx;
}

.help-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.help-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 10rpx;
}
</style>
