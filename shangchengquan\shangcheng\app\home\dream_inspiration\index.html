<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>{:t('梦想记录')}</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  {include file="public/css"/}
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header"><i class="fa fa-magic"></i> {:t('梦想记录')}</div>
          <div class="layui-card-body" pad15>
							<div class="layui-col-md4" style="padding-bottom:10px">
								<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="datadel(0)">删除</button>
								<button class="layui-btn layui-btn-normal layuiadmin-btn-list" onclick="location.reload()">刷新</button>
							</div>
							<div class="layui-form layui-col-md8" style="text-align:right;padding-bottom:10px">
								<div class="layui-inline layuiadmin-input-useradmin">
									<label class="layui-form-label" style="width:60px">昵称</label>
									<div class="layui-input-block" style="width:120px;margin-left:90px">
										<input type="text" name="nickname" autocomplete="off" class="layui-input" value="">
									</div>
								</div>
								<div class="layui-inline">
									<label class="layui-form-label" style="width:30px">性别</label>
									<div class="layui-input-block" style="width:120px;margin-left:60px;text-align:left">
										<select name="gender">
											<option value="">全部</option>
											<option value="1">男</option>
											<option value="0">女</option>
										</select>
									</div>
								</div>
								<div class="layui-inline">
									<label class="layui-form-label" style="width:30px">状态</label>
									<div class="layui-input-block" style="width:120px;margin-left:60px;text-align:left">
										<select name="status">
											<option value="">全部</option>
											<option value="1">已生成</option>
											<option value="0">生成中</option>
										</select>
									</div>
								</div>
								<div class="layui-inline">
									<button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="LAY-user-front-search">
										<i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
									</button>
								</div>
							</div>
							<table class="layui-hide" id="LAY-user-manage" lay-filter="LAY-user-manage"></table>
							<script type="text/html" id="table-useradmin-admin">
								<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="detail"><i class="layui-icon layui-icon-about"></i>详情</a>
								<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
							</script>
							<script type="text/html" id="table-useradmin-status">
								{{# if(d.status == 1){ }}
									<span class="layui-badge layui-bg-green">已生成</span>
								{{# } else { }}
									<span class="layui-badge layui-bg-orange">生成中</span>
								{{# } }}
							</script>
							<script type="text/html" id="table-useradmin-gender">
								{{# if(d.gender == 1){ }}
									<span class="layui-badge layui-bg-blue">男</span>
								{{# } else { }}
									<span class="layui-badge layui-bg-red">女</span>
								{{# } }}
							</script>
							<script type="text/html" id="table-useradmin-image">
								{{# if(d.result_image){ }}
									<img src="{{d.result_image}}" style="width:60px;height:60px;object-fit:cover;border-radius:4px;" onclick="layer.photos({photos: {data: [{src: '{{d.result_image}}'}]}, anim: 5});">
								{{# } else { }}
									<span class="layui-badge layui-bg-gray">暂无图片</span>
								{{# } }}
							</script>
          </div>
        </div>
    </div>
  </div>

  {include file="public/js"/}
  <script>
    var table = layui.table;
    var form = layui.form;

    table.render({
      elem: '#LAY-user-manage'
      ,url: '{:url("index")}' //模拟接口
      ,cols: [[
        {type: 'checkbox', fixed: 'left'}
        ,{field: 'id', width: 80, title: 'ID', sort: true}
        ,{field: 'nickname', title: '昵称', minWidth: 100}
        ,{field: 'gender', title: '性别', width: 80, templet: '#table-useradmin-gender'}
        ,{field: 'dream_content', title: '梦想内容', minWidth: 200}
        ,{field: 'result_image', title: '生成图片', width: 120, templet: '#table-useradmin-image'}
        ,{field: 'status', title: '状态', width: 100, templet: '#table-useradmin-status'}
        ,{field: 'create_time_text', title: '创建时间', width: 160}
        ,{title: '操作', width: 150, align:'center', fixed: 'right', toolbar: '#table-useradmin-admin'}
      ]]
      ,page: true
      ,limit: 30
      ,height: 'full-220'
      ,text: '对不起，加载出现异常！'
    });
    
    //监听搜索
    form.on('submit(LAY-user-front-search)', function(data){
      var field = data.field;
      //执行重载
      table.reload('LAY-user-manage', {
        where: field
      });
    });
    
    //监听工具条
    table.on('tool(LAY-user-manage)', function(obj){
      var data = obj.data;
      if(obj.event === 'del'){
        layer.confirm('真的删除这条记录吗？', function(index){
          $.post('{:url("del")}', {id: data.id}, function(res){
            if(res.code == 1){
              layer.msg(res.msg, {icon: 1});
              obj.del();
            }else{
              layer.msg(res.msg, {icon: 2});
            }
          });
          layer.close(index);
        });
      } else if(obj.event === 'detail'){
        openmax('{:url("detail")}?id=' + data.id);
      }
    });
  
  function datadel(id){
    var checkStatus = layui.table.checkStatus('LAY-user-manage');
    var data = checkStatus.data;
    if(data.length === 0){
      layer.msg('请选择要删除的数据');
      return;
    }
    var ids = [];
    for(var i = 0; i < data.length; i++){
      ids.push(data[i].id);
    }
    layer.confirm('确定删除选中的记录吗？', function(index){
      $.post('{:url("delall")}', {ids: ids.join(',')}, function(res){
        if(res.code == 1){
          layer.msg(res.msg, {icon: 1});
          layui.table.reload('LAY-user-manage');
        }else{
          layer.msg(res.msg, {icon: 2});
        }
      });
      layer.close(index);
    });
  }
  </script>
</body>
</html>
