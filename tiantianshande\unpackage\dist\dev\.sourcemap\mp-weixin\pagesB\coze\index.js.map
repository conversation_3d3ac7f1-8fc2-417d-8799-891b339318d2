{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/index.vue?d5be", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/index.vue?9e17", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/index.vue?662a", "uni-app:///pagesB/coze/index.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/index.vue?5ec9", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/index.vue?d24b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "statsData", "onLoad", "onShow", "methods", "getStats", "app", "that", "showStats", "closeStats", "showHelp", "closeHelp", "showSettings", "uni", "title", "icon", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8KtnB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAC;QACA;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3OA;AAAA;AAAA;AAAA;AAAq3B,CAAgB,o2BAAG,EAAC,C;;;;;;;;;;;ACAz4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesB/coze/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesB/coze/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=563c4ded&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesB/coze/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=563c4ded&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.isload ? _vm.t(\"color1\") : null\n  var m1 = _vm.isload ? _vm.t(\"color1\") : null\n  var m2 = _vm.isload ? _vm.t(\"color1\") : null\n  var m3 = _vm.isload ? _vm.t(\"color1\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<!-- 顶部导航 -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header-title\">扣子AI助手</view>\n\t\t\t<view class=\"header-actions\">\n\t\t\t\t<view class=\"action-btn\" @tap=\"goto\" data-url=\"/pagesB/coze/history\">\n\t\t\t\t\t<text class=\"iconfont iconlishi\"></text>\n\t\t\t\t\t<text>历史记录</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 功能卡片 -->\n\t\t<view class=\"feature-cards\">\n\t\t\t<!-- AI聊天卡片 -->\n\t\t\t<view class=\"feature-card chat-card\" @tap=\"goto\" data-url=\"/pagesB/coze/chat\">\n\t\t\t\t<view class=\"card-icon\">\n\t\t\t\t\t<text class=\"iconfont iconliaotian\" :style=\"{color: t('color1')}\"></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t<view class=\"card-title\">AI智能聊天</view>\n\t\t\t\t\t<view class=\"card-desc\">与AI机器人进行智能对话，获取专业解答</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"card-arrow\">\n\t\t\t\t\t<text class=\"iconfont iconjiantou-you\"></text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 工作流卡片 -->\n\t\t\t<view class=\"feature-card workflow-card\" @tap=\"goto\" data-url=\"/pagesB/coze/workflow\">\n\t\t\t\t<view class=\"card-icon\">\n\t\t\t\t\t<text class=\"iconfont icongongzuoliu\" :style=\"{color: '#52c41a'}\"></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t<view class=\"card-title\">智能工作流</view>\n\t\t\t\t\t<view class=\"card-desc\">运行自动化工作流，提升工作效率</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"card-arrow\">\n\t\t\t\t\t<text class=\"iconfont iconjiantou-you\"></text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 文件处理卡片 -->\n\t\t\t<view class=\"feature-card file-card\" @tap=\"goto\" data-url=\"/pagesB/coze/file-upload\">\n\t\t\t\t<view class=\"card-icon\">\n\t\t\t\t\t<text class=\"iconfont iconwenjian\" :style=\"{color: '#1890ff'}\"></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t<view class=\"card-title\">文件处理</view>\n\t\t\t\t\t<view class=\"card-desc\">上传文件进行AI分析和处理</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"card-arrow\">\n\t\t\t\t\t<text class=\"iconfont iconjiantou-you\"></text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 使用统计卡片 -->\n\t\t\t<view class=\"feature-card stats-card\" @tap=\"showStats\">\n\t\t\t\t<view class=\"card-icon\">\n\t\t\t\t\t<text class=\"iconfont icontongji\" :style=\"{color: '#722ed1'}\"></text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t<view class=\"card-title\">使用统计</view>\n\t\t\t\t\t<view class=\"card-desc\">查看API调用统计和使用情况</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"card-arrow\">\n\t\t\t\t\t<text class=\"iconfont iconjiantou-you\"></text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 快速入口 -->\n\t\t<view class=\"quick-actions\">\n\t\t\t<view class=\"section-title\">快速入口</view>\n\t\t\t<view class=\"action-grid\">\n\t\t\t\t<view class=\"action-item\" @tap=\"goto\" data-url=\"/pagesB/coze/chat\">\n\t\t\t\t\t<view class=\"action-icon\">\n\t\t\t\t\t\t<text class=\"iconfont iconkuaisuliaotian\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-text\">快速聊天</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"action-item\" @tap=\"goto\" data-url=\"/pagesB/coze/workflow-logs\">\n\t\t\t\t\t<view class=\"action-icon\">\n\t\t\t\t\t\t<text class=\"iconfont iconjilu\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-text\">执行记录</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"action-item\" @tap=\"showHelp\">\n\t\t\t\t\t<view class=\"action-icon\">\n\t\t\t\t\t\t<text class=\"iconfont iconbangzhu\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-text\">使用帮助</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"action-item\" @tap=\"showSettings\">\n\t\t\t\t\t<view class=\"action-icon\">\n\t\t\t\t\t\t<text class=\"iconfont iconshezhi\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-text\">设置</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 使用统计弹窗 -->\n\t\t<uni-popup ref=\"statsPopup\" type=\"center\">\n\t\t\t<view class=\"stats-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<view class=\"popup-title\">使用统计</view>\n\t\t\t\t\t<view class=\"popup-close\" @tap=\"closeStats\">\n\t\t\t\t\t\t<text class=\"iconfont iconguanbi\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stats-content\">\n\t\t\t\t\t<view class=\"stats-item\">\n\t\t\t\t\t\t<view class=\"stats-label\">总请求次数</view>\n\t\t\t\t\t\t<view class=\"stats-value\" :style=\"{color: t('color1')}\">{{statsData.total_requests || 0}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"stats-item\">\n\t\t\t\t\t\t<view class=\"stats-label\">成功次数</view>\n\t\t\t\t\t\t<view class=\"stats-value\" style=\"color: #52c41a\">{{statsData.success_requests || 0}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"stats-item\">\n\t\t\t\t\t\t<view class=\"stats-label\">失败次数</view>\n\t\t\t\t\t\t<view class=\"stats-value\" style=\"color: #ff4d4f\">{{statsData.failed_requests || 0}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"stats-item\">\n\t\t\t\t\t\t<view class=\"stats-label\">成功率</view>\n\t\t\t\t\t\t<view class=\"stats-value\" :style=\"{color: t('color1')}\">{{statsData.success_rate || 0}}%</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup-footer\">\n\t\t\t\t\t<view class=\"btn-confirm\" :style=\"{background: t('color1')}\" @tap=\"closeStats\">确定</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\n\t\t<!-- 帮助弹窗 -->\n\t\t<uni-popup ref=\"helpPopup\" type=\"bottom\">\n\t\t\t<view class=\"help-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<view class=\"popup-title\">使用帮助</view>\n\t\t\t\t\t<view class=\"popup-close\" @tap=\"closeHelp\">\n\t\t\t\t\t\t<text class=\"iconfont iconguanbi\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<scroll-view class=\"help-content\" scroll-y=\"true\">\n\t\t\t\t\t<view class=\"help-section\">\n\t\t\t\t\t\t<view class=\"help-title\">AI智能聊天</view>\n\t\t\t\t\t\t<view class=\"help-text\">• 选择合适的AI机器人进行对话</view>\n\t\t\t\t\t\t<view class=\"help-text\">• 输入问题或需求，获取智能回答</view>\n\t\t\t\t\t\t<view class=\"help-text\">• 支持上下文对话，记忆对话历史</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"help-section\">\n\t\t\t\t\t\t<view class=\"help-title\">智能工作流</view>\n\t\t\t\t\t\t<view class=\"help-text\">• 选择预设的工作流模板</view>\n\t\t\t\t\t\t<view class=\"help-text\">• 配置工作流参数</view>\n\t\t\t\t\t\t<view class=\"help-text\">• 支持同步和异步执行</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"help-section\">\n\t\t\t\t\t\t<view class=\"help-title\">文件处理</view>\n\t\t\t\t\t\t<view class=\"help-text\">• 上传文档、图片等文件</view>\n\t\t\t\t\t\t<view class=\"help-text\">• AI自动分析文件内容</view>\n\t\t\t\t\t\t<view class=\"help-text\">• 获取处理结果和建议</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tisload: false,\n\t\t\tloading: false,\n\t\t\tstatsData: {}\n\t\t};\n\t},\n\n\tonLoad: function(opt) {\n\t\tthis.loaded();\n\t},\n\n\tonShow: function() {\n\t\t// 页面显示时可以刷新统计数据\n\t\tthis.getStats();\n\t},\n\n\tmethods: {\n\t\t// 获取使用统计\n\t\tgetStats: function() {\n\t\t\tvar that = this;\n\t\t\tapp.post('ApiCoze/getstats', {}, function(res) {\n\t\t\t\tif (res.code === 1) {\n\t\t\t\t\tthat.statsData = res.data || {};\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 显示统计\n\t\tshowStats: function() {\n\t\t\tthis.getStats();\n\t\t\tthis.$refs.statsPopup.open();\n\t\t},\n\n\t\t// 关闭统计\n\t\tcloseStats: function() {\n\t\t\tthis.$refs.statsPopup.close();\n\t\t},\n\n\t\t// 显示帮助\n\t\tshowHelp: function() {\n\t\t\tthis.$refs.helpPopup.open();\n\t\t},\n\n\t\t// 关闭帮助\n\t\tcloseHelp: function() {\n\t\t\tthis.$refs.helpPopup.close();\n\t\t},\n\n\t\t// 显示设置\n\t\tshowSettings: function() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '设置功能开发中...',\n\t\t\t\ticon: 'none',\n\t\t\t\tduration: 2000\n\t\t\t});\n\t\t}\n\t}\n};\n</script>\n\n<style>\n.container {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tmin-height: 100vh;\n}\n\n.header {\n\tbackground: rgba(255,255,255,0.1);\n\tbackdrop-filter: blur(10px);\n\tpadding: 20rpx 30rpx;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tposition: fixed;\n\ttop: var(--window-top);\n\tleft: 0;\n\tright: 0;\n\tz-index: 100;\n}\n\n.header-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #fff;\n}\n\n.header-actions {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.action-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 10rpx 20rpx;\n\tbackground: rgba(255,255,255,0.2);\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n\tcolor: #fff;\n}\n\n.action-btn .iconfont {\n\tmargin-right: 10rpx;\n\tfont-size: 28rpx;\n}\n\n.feature-cards {\n\tpadding: 30rpx;\n\tmargin-top: 120rpx;\n}\n\n.feature-card {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tbox-shadow: 0 8rpx 30rpx rgba(0,0,0,0.1);\n}\n\n.card-icon {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tbackground: #f5f5f5;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 30rpx;\n}\n\n.card-icon .iconfont {\n\tfont-size: 40rpx;\n}\n\n.card-content {\n\tflex: 1;\n}\n\n.card-title {\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 10rpx;\n}\n\n.card-desc {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tline-height: 1.4;\n}\n\n.card-arrow {\n\tcolor: #ccc;\n\tfont-size: 28rpx;\n}\n\n.quick-actions {\n\tbackground: #fff;\n\tmargin: 0 30rpx;\n\tborder-radius: 20rpx;\n\tpadding: 30rpx;\n\tbox-shadow: 0 8rpx 30rpx rgba(0,0,0,0.1);\n}\n\n.section-title {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 30rpx;\n}\n\n.action-grid {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(4, 1fr);\n\tgap: 30rpx;\n}\n\n.action-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\ttext-align: center;\n}\n\n.action-icon {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tbackground: #f5f5f5;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-bottom: 15rpx;\n}\n\n.action-icon .iconfont {\n\tfont-size: 36rpx;\n\tcolor: #666;\n}\n\n.action-text {\n\tfont-size: 22rpx;\n\tcolor: #666;\n}\n\n.stats-popup {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\twidth: 80vw;\n\tmax-width: 600rpx;\n}\n\n.popup-header {\n\tpadding: 30rpx;\n\tborder-bottom: 1rpx solid #eee;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.popup-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.popup-close {\n\tfont-size: 32rpx;\n\tcolor: #999;\n}\n\n.stats-content {\n\tpadding: 30rpx;\n}\n\n.stats-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1rpx solid #f5f5f5;\n}\n\n.stats-item:last-child {\n\tborder-bottom: none;\n}\n\n.stats-label {\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\n.stats-value {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n}\n\n.popup-footer {\n\tpadding: 30rpx;\n\tborder-top: 1rpx solid #eee;\n}\n\n.btn-confirm {\n\twidth: 100%;\n\theight: 80rpx;\n\tline-height: 80rpx;\n\ttext-align: center;\n\tborder-radius: 12rpx;\n\tfont-size: 28rpx;\n\tcolor: #fff;\n}\n\n.help-popup {\n\tbackground: #fff;\n\tborder-radius: 20rpx 20rpx 0 0;\n\tmax-height: 70vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.help-content {\n\tflex: 1;\n\tpadding: 30rpx;\n}\n\n.help-section {\n\tmargin-bottom: 40rpx;\n}\n\n.help-title {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n}\n\n.help-text {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.5;\n\tmargin-bottom: 10rpx;\n}\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753981196557\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}