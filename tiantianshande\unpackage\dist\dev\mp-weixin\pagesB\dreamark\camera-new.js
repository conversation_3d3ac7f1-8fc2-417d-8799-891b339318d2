require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesB/dreamark/camera-new"],{

/***/ 7258:
/*!***********************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/main.js?{"page":"pagesB%2Fdreamark%2Fcamera-new"} ***!
  \***********************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _cameraNew = _interopRequireDefault(__webpack_require__(/*! ./pagesB/dreamark/camera-new.vue */ 7259));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_cameraNew.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 7259:
/*!****************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue ***!
  \****************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./camera-new.vue?vue&type=template&id=368f168d&scoped=true& */ 7260);
/* harmony import */ var _camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./camera-new.vue?vue&type=script&lang=js& */ 7262);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _camera_new_vue_vue_type_style_index_0_id_368f168d_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./camera-new.vue?vue&type=style&index=0&id=368f168d&scoped=true&lang=css& */ 7264);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 34);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "368f168d",
  null,
  false,
  _camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesB/dreamark/camera-new.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 7260:
/*!***********************************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?vue&type=template&id=368f168d&scoped=true& ***!
  \***********************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./camera-new.vue?vue&type=template&id=368f168d&scoped=true& */ 7261);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_template_id_368f168d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 7261:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?vue&type=template&id=368f168d&scoped=true& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.currentStep === "camera" ? _vm.t("color1") : null
  var m1 = _vm.currentStep === "camera" ? _vm.t("color1") : null
  var m2 = _vm.currentStep === "camera" ? _vm.t("color1") : null
  var m3 = _vm.currentStep === "camera" ? _vm.t("color1") : null
  var m4 = _vm.currentStep === "camera" ? _vm.t("color1") : null
  var m5 = _vm.currentStep === "preview" ? _vm.t("color1") : null
  var m6 = _vm.currentStep === "preview" ? _vm.t("color1rgb") : null
  var m7 = _vm.currentStep === "preview" ? _vm.t("color1") : null
  var m8 = _vm.currentStep === "processing" ? _vm.t("color1") : null
  var m9 = _vm.currentStep === "processing" ? _vm.t("color1rgb") : null
  var m10 = _vm.currentStep === "processing" ? _vm.t("color1") : null
  var m11 = _vm.currentStep === "processing" ? _vm.t("color1") : null
  var m12 = _vm.currentStep === "processing" ? _vm.t("color1") : null
  var l0 =
    _vm.currentStep === "processing"
      ? _vm.__map(_vm.processingSteps, function (step, index) {
          var $orig = _vm.__get_orig(step)
          var m13 = _vm.t("color1")
          var m14 = index <= _vm.currentProcessStep ? _vm.t("color1") : null
          var m15 = index <= _vm.currentProcessStep ? _vm.t("color1") : null
          return {
            $orig: $orig,
            m13: m13,
            m14: m14,
            m15: m15,
          }
        })
      : null
  var m16 = _vm.currentStep === "processing" ? _vm.t("color1") : null
  var m17 = _vm.currentStep === "processing" ? _vm.t("color1") : null
  var m18 = _vm.currentStep === "processing" ? _vm.t("color1rgb") : null
  var m19 = _vm.currentStep === "processing" ? _vm.t("color1") : null
  var m20 = _vm.currentStep === "result" ? _vm.t("color1") : null
  var m21 = _vm.currentStep === "result" ? _vm.t("color1rgb") : null
  var m22 = _vm.currentStep === "result" ? _vm.t("color1") : null
  var m23 = _vm.currentStep === "result" ? _vm.t("color1") : null
  var m24 = _vm.currentStep === "result" ? _vm.t("color1") : null
  var m25 = _vm.currentStep === "result" ? _vm.t("color1") : null
  var m26 =
    _vm.currentStep === "result" && !_vm.predictedImageLoaded
      ? _vm.t("color1")
      : null
  var m27 = _vm.currentStep === "result" ? _vm.t("color1") : null
  var m28 = _vm.currentStep === "result" ? _vm.t("color1") : null
  var m29 = _vm.currentStep === "result" ? _vm.t("color1") : null
  var m30 = _vm.currentStep === "result" ? _vm.t("color1") : null
  var l1 =
    _vm.currentStep === "result"
      ? _vm.__map(5, function (i, __i2__) {
          var $orig = _vm.__get_orig(i)
          var m31 = _vm.t("color1")
          return {
            $orig: $orig,
            m31: m31,
          }
        })
      : null
  var m32 = _vm.showConfig ? _vm.t("color1") : null
  var m33 = _vm.showConfig && _vm.showConfigTip ? _vm.t("color1") : null
  var m34 = _vm.showConfig ? _vm.t("color1") : null
  var l2 = _vm.showConfig
    ? _vm.__map(_vm.genderOptions, function (option, index) {
        var $orig = _vm.__get_orig(option)
        var m35 =
          index > 0 && _vm.genderIndex === index ? _vm.t("color1") : null
        var m36 =
          index > 0 && _vm.genderIndex === index ? _vm.t("color1rgb") : null
        var m37 =
          index > 0 && _vm.genderIndex === index ? _vm.t("color1") : null
        return {
          $orig: $orig,
          m35: m35,
          m36: m36,
          m37: m37,
        }
      })
    : null
  var m38 = _vm.showConfig ? _vm.t("color1") : null
  var m39 = _vm.showConfig && _vm.userProfession ? _vm.t("color1") : null
  var m40 = _vm.showConfig && _vm.userProfession ? _vm.t("color1") : null
  var m41 = _vm.showConfig && _vm.userProfession ? _vm.t("color1") : null
  var m42 = _vm.showConfig ? _vm.t("color1") : null
  var m43 = _vm.showConfig ? _vm.t("color1rgb") : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        m2: m2,
        m3: m3,
        m4: m4,
        m5: m5,
        m6: m6,
        m7: m7,
        m8: m8,
        m9: m9,
        m10: m10,
        m11: m11,
        m12: m12,
        l0: l0,
        m16: m16,
        m17: m17,
        m18: m18,
        m19: m19,
        m20: m20,
        m21: m21,
        m22: m22,
        m23: m23,
        m24: m24,
        m25: m25,
        m26: m26,
        m27: m27,
        m28: m28,
        m29: m29,
        m30: m30,
        l1: l1,
        m32: m32,
        m33: m33,
        m34: m34,
        l2: l2,
        m38: m38,
        m39: m39,
        m40: m40,
        m41: m41,
        m42: m42,
        m43: m43,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 7262:
/*!*****************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./camera-new.vue?vue&type=script&lang=js& */ 7263);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 7263:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?vue&type=script&lang=js& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      currentStep: 'camera',
      // camera, preview, processing, result
      currentStatus: '准备拍照',
      showCamera: false,
      cameraReady: false,
      cameraStatusText: '正在启动摄像头...',
      capturedImageUrl: '',
      predictedImageUrl: '',
      progressPercent: 0,
      currentProcessStep: 0,
      processingSteps: [{
        icon: '🔍',
        text: '面部识别'
      }, {
        icon: '🧠',
        text: 'AI分析'
      }, {
        icon: '✨',
        text: '预测生成'
      }],
      // 配置相关
      showConfig: false,
      showConfigTip: false,
      genderIndex: -1,
      genderOptions: ['请选择性别', '男', '女', '其他'],
      userProfession: '',
      // 图片加载状态
      imageLoaded: false,
      currentImageLoaded: false,
      predictedImageLoaded: false,
      // 定时器
      processingTimer: null,
      progressTimer: null,
      // 音频相关
      pre_url: '',
      // 云资源域名前缀
      processingAudio: null // 处理中音频对象
    };
  },
  onLoad: function onLoad() {
    var _this = this;
    console.log('=== 摄像头页面加载开始 ===');

    // 初始化云资源前缀URL
    var app = getApp();
    this.pre_url = app.globalData.pre_url || '';
    setTimeout(function () {
      _this.loadUserConfig();
      _this.initCamera();
      _this.checkDreamInspirationSettings();
    }, 500);
  },
  onUnload: function onUnload() {
    this.clearTimers();
    this.stopProcessingAudio();
  },
  methods: {
    // 加载用户配置
    loadUserConfig: function loadUserConfig() {
      console.log('=== 开始加载用户配置 ===');

      // 尝试加载已保存的配置
      try {
        var savedGender = uni.getStorageSync('user_gender');
        var savedProfession = uni.getStorageSync('user_profession');
        if (savedGender) {
          var genderIndex = this.genderOptions.indexOf(savedGender);
          if (genderIndex > 0) {
            this.genderIndex = genderIndex;
          }
        }
        if (savedProfession) {
          this.userProfession = savedProfession;
        }
        console.log('加载已保存配置:', {
          gender: savedGender,
          profession: savedProfession,
          genderIndex: this.genderIndex
        });
      } catch (e) {
        console.error('加载配置失败:', e);
      }
      this.showConfigTip = true;
      this.showConfig = true;
      console.log('显示配置弹窗');
    },
    // 初始化摄像头
    initCamera: function initCamera() {
      console.log('=== 开始初始化摄像头 ===');
      this.showCamera = true;
      this.cameraReady = true;
      this.cameraStatusText = '摄像头已就绪';
      console.log('摄像头状态设置完成');
    },
    // 摄像头就绪
    onCameraReady: function onCameraReady() {
      this.cameraReady = true;
      this.cameraStatusText = '摄像头已就绪';
      this.currentStatus = '准备拍照';
      console.log('摄像头初始化完成');
    },
    // 摄像头错误
    onCameraError: function onCameraError(e) {
      console.error('摄像头错误:', e);
      this.cameraStatusText = '摄像头启动失败';
    },
    // 拍照
    capturePhoto: function capturePhoto() {
      console.log('开始拍照');
      this.uploadImage('camera');
    },
    // 选择图片
    chooseImage: function chooseImage() {
      console.log('选择图片');
      this.uploadImage('album');
    },
    // 统一的图片上传方法
    uploadImage: function uploadImage() {
      var sourceType = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'album';
      var that = this;
      var app = getApp();

      // 显示上传提示
      uni.showLoading({
        title: '正在上传图片...',
        mask: true
      });

      // 使用统一的图片上传接口
      app.chooseImage(function (urls) {
        uni.hideLoading();
        if (urls && urls.length > 0) {
          // 获取上传后的图片URL
          that.capturedImageUrl = urls[0];
          that.currentStep = 'preview';

          // 重置图片加载状态
          that.imageLoaded = false;
          that.currentImageLoaded = false;
          that.predictedImageLoaded = false;
          console.log('图片上传成功:', urls[0]);
          console.log('切换到预览模式，当前步骤:', that.currentStep);
          console.log('图片URL设置为:', that.capturedImageUrl);
          uni.showToast({
            title: '图片上传成功',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: '图片上传失败',
            icon: 'error'
          });
        }
      }, 1, sourceType === 'camera' ? ['camera'] : ['album', 'camera']);
    },
    // 切换摄像头
    switchCamera: function switchCamera() {
      console.log('切换摄像头');
      // 这里可以实现前后摄像头切换逻辑
    },
    // 重新拍照
    retakePhoto: function retakePhoto() {
      this.currentStep = 'camera';
      this.capturedImageUrl = '';
      this.predictedImageUrl = '';
      // 重置图片加载状态
      this.imageLoaded = false;
      this.currentImageLoaded = false;
      this.predictedImageLoaded = false;
    },
    // 图片加载事件处理
    onImageLoad: function onImageLoad() {
      this.imageLoaded = true;
      console.log('预览图片加载完成');
    },
    onImageError: function onImageError(e) {
      console.error('预览图片加载失败:', e);
      uni.showToast({
        title: '图片加载失败',
        icon: 'none'
      });
    },
    onCurrentImageLoad: function onCurrentImageLoad() {
      this.currentImageLoaded = true;
      console.log('当前图片加载完成');
    },
    onCurrentImageError: function onCurrentImageError(e) {
      console.error('当前图片加载失败:', e);
      this.currentImageLoaded = false;
    },
    onPredictedImageLoad: function onPredictedImageLoad() {
      this.predictedImageLoaded = true;
      console.log('预测图片加载完成');
    },
    onPredictedImageError: function onPredictedImageError(e) {
      console.error('预测图片加载失败:', e);
      this.predictedImageLoaded = false;
      uni.showToast({
        title: '预测图片加载失败',
        icon: 'none'
      });
    },
    // 开始处理
    startProcessing: function startProcessing() {
      this.currentStep = 'processing';
      this.progressPercent = 0;
      this.currentProcessStep = 0;

      // 播放处理中音频
      this.playProcessingAudio();

      // 模拟AI处理过程
      this.simulateAIProcessing();
    },
    // 播放处理中音频
    playProcessingAudio: function playProcessingAudio() {
      var _this2 = this;
      try {
        // 停止之前的音频
        this.stopProcessingAudio();

        // 创建音频上下文
        this.processingAudio = uni.createInnerAudioContext();

        // 设置音频源（使用云端资源）
        this.processingAudio.src = this.pre_url + '/static/MP3/耐心等待20秒.mp3';

        // 音频播放事件
        this.processingAudio.onPlay(function () {
          console.log('开始播放处理中音频');
        });

        // 音频播放完成事件
        this.processingAudio.onEnded(function () {
          console.log('处理中音频播放完成');
          _this2.processingAudio.destroy();
          _this2.processingAudio = null;
        });

        // 音频播放错误事件
        this.processingAudio.onError(function (res) {
          console.error('处理中音频播放错误:', res);
          _this2.processingAudio.destroy();
          _this2.processingAudio = null;
        });

        // 开始播放
        this.processingAudio.play();

        // 设置定时器，10-15秒后停止播放
        setTimeout(function () {
          _this2.stopProcessingAudio();
        }, 12000); // 12秒后停止
      } catch (error) {
        console.error('播放处理中音频时发生错误:', error);
      }
    },
    // 停止处理中音频
    stopProcessingAudio: function stopProcessingAudio() {
      if (this.processingAudio) {
        try {
          this.processingAudio.stop();
          this.processingAudio.destroy();
          this.processingAudio = null;
          console.log('处理中音频已停止');
        } catch (error) {
          console.error('停止处理中音频时发生错误:', error);
        }
      }
    },
    // 模拟AI处理过程
    simulateAIProcessing: function simulateAIProcessing() {
      var _this3 = this;
      // 处理步骤动画
      var stepInterval = setInterval(function () {
        if (_this3.currentProcessStep < _this3.processingSteps.length - 1) {
          _this3.currentProcessStep++;
        }
      }, 1500);

      // 进度条动画
      this.processingTimer = setInterval(function () {
        _this3.progressPercent += 1;
        if (_this3.progressPercent >= 100) {
          clearInterval(stepInterval);
          _this3.completeProcessing();
        }
      }, 50);
    },
    // 完成处理
    completeProcessing: function completeProcessing() {
      this.clearTimers();

      // 停止处理中音频
      this.stopProcessingAudio();

      // 调用AI接口生成20年后的图片
      this.generateFutureImage();
    },
    // 生成梦想启蒙图片
    generateFutureImage: function generateFutureImage() {
      var _this4 = this;
      // 重置预测图片加载状态
      this.predictedImageLoaded = false;

      // 检查是否已配置性别和职业
      var userGender = uni.getStorageSync('user_gender');
      var userProfession = uni.getStorageSync('user_profession');
      console.log('检查用户配置:', {
        userGender: userGender,
        userProfession: userProfession,
        capturedImageUrl: this.capturedImageUrl
      });
      if (!userGender || !userProfession || userGender === '未设置' || userProfession === '未设置') {
        console.log('用户配置不完整，显示配置提醒');
        uni.showModal({
          title: '配置提醒',
          content: '请先完成性别和职业配置，以便生成个性化的梦想图片',
          showCancel: false,
          confirmText: '去配置',
          success: function success() {
            _this4.showConfig = true;
          }
        });
        return;
      }

      // 检查是否有图片URL
      if (!this.capturedImageUrl) {
        console.log('没有图片URL，无法调用API');
        uni.showToast({
          title: '请先上传照片',
          icon: 'none'
        });
        return;
      }
      console.log('配置检查通过，开始调用梦想启蒙API');
      // 调用真实的梦想启蒙API
      this.callAIAPI();
    },
    // 调用梦想启蒙AI接口
    callAIAPI: function callAIAPI() {
      var _this5 = this;
      console.log('=== 开始调用梦想启蒙API ===');

      // 获取用户配置信息
      var userGender = uni.getStorageSync('user_gender') || '未设置';
      var userProfession = uni.getStorageSync('user_profession') || '未设置';

      // 构建梦想内容
      var dreamContent = "\u6211\u662F\u4E00\u4E2A".concat(userGender, "\uFF0C\u804C\u4E1A\u662F").concat(userProfession, "\uFF0C\u5E0C\u671B\u5728\u672A\u6765\u80FD\u591F\u5B9E\u73B0\u81EA\u5DF1\u7684\u68A6\u60F3\uFF0C\u53D8\u5F97\u66F4\u52A0\u4F18\u79C0\u548C\u6210\u529F\u3002");

      // 准备请求参数
      var requestData = {
        gender: userGender,
        dream_content: dreamContent,
        image_url: this.capturedImageUrl // 通过统一上传接口获取的图片URL
      };

      console.log('API请求参数:', requestData);
      console.log('即将调用接口: ApiDreamInspiration/generateImage');

      // 获取全局app对象
      var app = getApp();

      // 调用梦想启蒙API
      app.post('ApiDreamInspiration/generateImage', requestData, function (res) {
        console.log('API调用成功，返回结果:', res);
        if (res.code == 1) {
          // 生成成功，获取记录ID
          var recordId = res.data.record_id;

          // 显示生成中状态
          uni.showLoading({
            title: '正在生成梦想图片...',
            mask: true
          });

          // 轮询检查生成状态
          _this5.checkGenerationStatus(recordId);
        } else {
          _this5.handleAPIError(res.msg || '生成请求失败');
        }
      }, function (error) {
        console.error('API调用失败:', error);
        _this5.handleAPIError('网络请求失败，请检查网络连接');
      });
    },
    // 检查图片生成状态
    checkGenerationStatus: function checkGenerationStatus(recordId) {
      var _this6 = this;
      var app = getApp();
      var pollCount = 0;
      var maxPolls = 60; // 最多轮询60次（约2分钟）

      var checkStatus = function checkStatus() {
        pollCount++;
        console.log("\u8F6E\u8BE2\u67E5\u8BE2\u72B6\u6001 - \u7B2C".concat(pollCount, "\u6B21\uFF0C\u8BB0\u5F55ID: ").concat(recordId));
        app.post('ApiDreamInspiration/checkGenerationStatus', {
          record_id: recordId
        }, function (res) {
          console.log('状态查询结果:', res);
          if (res.code == 1) {
            var record = res.data;
            if (record.status == 1) {
              // 生成成功 (状态1表示已生成)
              uni.hideLoading();

              // 调试信息：打印获取到的图片URL
              console.log('梦想图片生成成功，图片URL:', record.result_image);
              _this6.predictedImageUrl = record.result_image;
              _this6.currentStep = 'result';

              // 重置图片加载状态，确保图片能正常显示
              _this6.predictedImageLoaded = false;

              // 强制触发页面更新
              _this6.$forceUpdate();

              // 保存记录ID用于后续查看
              uni.setStorageSync('current_dream_record_id', recordId);
              uni.showToast({
                title: '梦想图片生成完成！',
                icon: 'success'
              });

              // 调试信息：确认状态设置
              console.log('页面切换到结果展示，预测图片URL:', _this6.predictedImageUrl);
            } else if (record.status == 2) {
              // 生成失败 (状态2表示生成失败)
              uni.hideLoading();
              _this6.handleAPIError(record.error_msg || '图片生成失败');
            } else if (record.status == 0) {
              // 仍在生成中 (状态0表示生成中)
              if (pollCount >= maxPolls) {
                // 超过最大轮询次数
                uni.hideLoading();
                _this6.handleAPIError('生成超时，请稍后查看记录');
              } else {
                // 继续轮询，每3秒查询一次
                setTimeout(checkStatus, 3000);
              }
            } else {
              // 未知状态
              uni.hideLoading();
              _this6.handleAPIError('未知的生成状态');
            }
          } else {
            uni.hideLoading();
            _this6.handleAPIError(res.msg || '获取生成状态失败');
          }
        }, function (error) {
          console.error('状态查询失败:', error);
          uni.hideLoading();
          _this6.handleAPIError('检查生成状态失败');
        });
      };

      // 开始检查状态
      checkStatus();
    },
    // 处理API错误
    handleAPIError: function handleAPIError(message) {
      var _this7 = this;
      uni.showModal({
        title: '预测失败',
        content: message + '，是否重试？',
        success: function success(res) {
          if (res.confirm) {
            _this7.startProcessing();
          } else {
            _this7.currentStep = 'preview';
          }
        }
      });
    },
    // 与未来对话
    startFutureChat: function startFutureChat() {
      console.log('开始与未来对话');

      // 显示加载状态
      uni.showLoading({
        title: '正在获取对话链接...',
        mask: true
      });

      // 获取全局app对象
      var app = getApp();

      // 调用API获取与未来对话的链接
      app.post('ApiDreamInspiration/getFutureTalkUrl', {}, function (res) {
        uni.hideLoading();
        console.log('获取与未来对话链接结果:', res);
        if (res.code == 1) {
          var data = res.data;
          var futureTalkUrl = data.url;
          if (futureTalkUrl) {
            console.log('准备跳转到链接:', futureTalkUrl);

            // 判断是否为外部链接
            if (futureTalkUrl.startsWith('http://') || futureTalkUrl.startsWith('https://')) {
              // 外部链接，使用webview打开

              var webviewUrl = '/pages/index/webView3?url=' + encodeURIComponent(futureTalkUrl);
              uni.navigateTo({
                url: webviewUrl,
                success: function success() {
                  console.log('跳转到WebView成功');
                },
                fail: function fail(err) {
                  console.error('跳转到WebView失败', err);
                  uni.showToast({
                    title: '跳转失败，请稍后再试',
                    icon: 'none'
                  });
                }
              });
            } else {
              // 内部页面链接
              uni.navigateTo({
                url: futureTalkUrl,
                success: function success() {
                  console.log('跳转到内部页面成功');
                },
                fail: function fail(err) {
                  console.error('跳转到内部页面失败', err);
                  uni.showToast({
                    title: '跳转失败，请稍后再试',
                    icon: 'none'
                  });
                }
              });
            }
          } else {
            uni.showToast({
              title: '未配置对话链接',
              icon: 'none'
            });
          }
        } else {
          uni.showToast({
            title: res.msg || '获取对话链接失败',
            icon: 'none'
          });
        }
      }, function (error) {
        uni.hideLoading();
        console.error('获取与未来对话链接失败:', error);
        uni.showToast({
          title: '网络请求失败，请检查网络连接',
          icon: 'none'
        });
      });
    },
    // 获取我的梦想记录
    getMyDreamRecords: function getMyDreamRecords() {
      var app = getApp();
      app.post('ApiDreamInspiration/getMyRecords', {
        page: 1,
        limit: 10
      }, function (res) {
        if (res.code == 1) {
          console.log('我的梦想记录:', res.data);
          // 可以在这里处理历史记录显示
        } else {
          console.error('获取记录失败:', res.msg);
        }
      }, function (error) {
        console.error('获取记录失败:', error);
      });
    },
    // 查看历史记录
    viewHistory: function viewHistory() {
      // 可以跳转到历史记录页面或显示历史记录弹窗
      this.getMyDreamRecords();
    },
    // 检查梦想启蒙设置
    checkDreamInspirationSettings: function checkDreamInspirationSettings() {
      var app = getApp();
      app.post('ApiDreamInspiration/getSetting', {}, function (res) {
        if (res.code == 1) {
          var settings = res.data;
          if (!settings.is_enabled) {
            uni.showModal({
              title: '功能未开启',
              content: '梦想启蒙功能暂未开启，请联系管理员',
              showCancel: false,
              confirmText: '知道了',
              success: function success() {
                uni.navigateBack();
              }
            });
          }
        } else {
          console.error('获取设置失败:', res.msg);
        }
      }, function (error) {
        console.error('检查设置失败:', error);
      });
    },
    // 返回
    goBack: function goBack() {
      uni.navigateBack();
    },
    // 配置相关方法
    selectGender: function selectGender(index) {
      if (index === 0) return;
      this.genderIndex = index;
    },
    onInputFocus: function onInputFocus() {
      console.log('职业输入框获得焦点');
    },
    onInputBlur: function onInputBlur() {
      console.log('职业输入框失去焦点');
    },
    onProfessionInput: function onProfessionInput(e) {
      this.userProfession = e.detail.value;
      console.log('职业输入内容:', this.userProfession);
    },
    onInputConfirm: function onInputConfirm(e) {
      this.userProfession = e.detail.value;
      console.log('职业输入确认:', this.userProfession);
      // 可以在这里添加输入验证逻辑
    },
    saveConfig: function saveConfig() {
      var _this8 = this;
      // 验证性别选择
      if (this.genderIndex <= 0) {
        uni.showToast({
          title: '请选择性别',
          icon: 'none'
        });
        return;
      }

      // 验证职业输入（可选，但建议填写）
      if (!this.userProfession || this.userProfession.trim() === '') {
        uni.showModal({
          title: '提示',
          content: '建议填写职业信息以获得更准确的预测结果，是否继续？',
          success: function success(res) {
            if (res.confirm) {
              _this8.doSaveConfig();
            }
          }
        });
        return;
      }
      this.doSaveConfig();
    },
    doSaveConfig: function doSaveConfig() {
      try {
        var profession = this.userProfession.trim();
        uni.setStorageSync('user_gender', this.genderOptions[this.genderIndex]);
        uni.setStorageSync('user_profession', profession);
        console.log('保存配置:', {
          gender: this.genderOptions[this.genderIndex],
          profession: profession
        });
        uni.showToast({
          title: '配置已保存',
          icon: 'success'
        });
        this.hideConfigModal();
      } catch (e) {
        console.error('保存配置失败:', e);
        uni.showToast({
          title: '保存失败，请重试',
          icon: 'error'
        });
      }
    },
    // 跳过配置
    skipConfig: function skipConfig() {
      var _this9 = this;
      uni.showModal({
        title: '跳过配置',
        content: '跳过配置可能影响AI预测准确性，确定要跳过吗？',
        success: function success(res) {
          if (res.confirm) {
            // 设置默认值
            uni.setStorageSync('user_gender', '未设置');
            uni.setStorageSync('user_profession', '未设置');
            uni.showToast({
              title: '已跳过配置',
              icon: 'none'
            });
            _this9.hideConfigModal();
          }
        }
      });
    },
    hideConfigModal: function hideConfigModal() {
      if (this.showConfigTip && this.genderIndex <= 0) {
        uni.showToast({
          title: '请先选择性别信息',
          icon: 'none'
        });
        return;
      }
      this.showConfig = false;
      this.showConfigTip = false;
    },
    // 强制显示配置
    forceShowConfig: function forceShowConfig() {
      this.showConfig = true;
      this.showConfigTip = true;
    },
    // 清除定时器
    clearTimers: function clearTimers() {
      if (this.processingTimer) {
        clearInterval(this.processingTimer);
        this.processingTimer = null;
      }
      if (this.progressTimer) {
        clearInterval(this.progressTimer);
        this.progressTimer = null;
      }
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 7264:
/*!*************************************************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?vue&type=style&index=0&id=368f168d&scoped=true&lang=css& ***!
  \*************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_style_index_0_id_368f168d_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./camera-new.vue?vue&type=style&index=0&id=368f168d&scoped=true&lang=css& */ 7265);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_style_index_0_id_368f168d_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_style_index_0_id_368f168d_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_style_index_0_id_368f168d_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_style_index_0_id_368f168d_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_camera_new_vue_vue_type_style_index_0_id_368f168d_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 7265:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?vue&type=style&index=0&id=368f168d&scoped=true&lang=css& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[7258,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesB/dreamark/camera-new.js.map