-- 为梦想启蒙设置表添加"与未来对话"相关字段
-- 全局前缀：ddwx

-- 添加与未来对话相关字段
ALTER TABLE `ddwx_dream_inspiration_settings` 
ADD COLUMN `future_talk_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用与未来对话：0禁用 1启用',
ADD COLUMN `future_talk_male_url` varchar(500) NOT NULL DEFAULT '' COMMENT '男性用户与未来对话链接',
ADD COLUMN `future_talk_female_url` varchar(500) NOT NULL DEFAULT '' COMMENT '女性用户与未来对话链接',
ADD COLUMN `future_talk_button_text` varchar(100) NOT NULL DEFAULT '与未来对话' COMMENT '按钮显示文字';

-- 更新现有记录，添加默认值
UPDATE `ddwx_dream_inspiration_settings` 
SET `future_talk_enabled` = 0,
    `future_talk_male_url` = '',
    `future_talk_female_url` = '',
    `future_talk_button_text` = '与未来对话'
WHERE `future_talk_enabled` IS NULL;
