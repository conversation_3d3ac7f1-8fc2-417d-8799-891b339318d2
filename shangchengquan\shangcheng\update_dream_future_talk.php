<?php
// 更新梦想启蒙数据库结构 - 添加与未来对话功能
require_once 'vendor/autoload.php';

// 数据库配置
$host = 'localhost';
$username = 'root';
$password = 'root123';
$database = 'qixian_zhongheng';

try {
    // 创建数据库连接
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "数据库连接成功\n";
    
    // 检查表是否存在
    $checkTable = $pdo->query("SHOW TABLES LIKE 'ddwx_dream_inspiration_settings'");
    if ($checkTable->rowCount() == 0) {
        echo "错误：表 ddwx_dream_inspiration_settings 不存在\n";
        exit(1);
    }
    
    echo "开始更新数据库结构...\n";
    
    // 检查字段是否已存在
    $checkColumns = $pdo->query("SHOW COLUMNS FROM ddwx_dream_inspiration_settings LIKE 'future_talk_enabled'");
    if ($checkColumns->rowCount() > 0) {
        echo "字段已存在，跳过添加\n";
    } else {
        // 添加与未来对话相关字段
        $sql = "ALTER TABLE `ddwx_dream_inspiration_settings` 
                ADD COLUMN `future_talk_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用与未来对话：0禁用 1启用',
                ADD COLUMN `future_talk_male_url` varchar(500) NOT NULL DEFAULT '' COMMENT '男性用户与未来对话链接',
                ADD COLUMN `future_talk_female_url` varchar(500) NOT NULL DEFAULT '' COMMENT '女性用户与未来对话链接',
                ADD COLUMN `future_talk_button_text` varchar(100) NOT NULL DEFAULT '与未来对话' COMMENT '按钮显示文字'";
        
        $pdo->exec($sql);
        echo "成功添加与未来对话相关字段\n";
    }
    
    // 更新现有记录，添加默认值
    $updateSql = "UPDATE `ddwx_dream_inspiration_settings` 
                  SET `future_talk_enabled` = 0,
                      `future_talk_male_url` = '',
                      `future_talk_female_url` = '',
                      `future_talk_button_text` = '与未来对话'
                  WHERE `future_talk_button_text` = '' OR `future_talk_button_text` IS NULL";
    
    $result = $pdo->exec($updateSql);
    echo "更新了 $result 条记录的默认值\n";
    
    echo "数据库结构更新完成！\n";
    
} catch (PDOException $e) {
    echo "数据库错误: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}
?>
