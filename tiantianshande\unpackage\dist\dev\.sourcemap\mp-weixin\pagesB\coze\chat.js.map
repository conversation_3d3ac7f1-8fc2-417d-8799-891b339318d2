{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/chat.vue?b9d8", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/chat.vue?91dd", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/chat.vue?341b", "uni-app:///pagesB/coze/chat.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/chat.vue?7aad", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/coze/chat.vue?ab9b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isload", "loading", "userInfo", "currentBot", "botList", "messageList", "inputMessage", "conversationId", "isTyping", "scrollTop", "pagenum", "nomore", "onLoad", "onShow", "methods", "getBotList", "that", "app", "uni", "title", "icon", "duration", "showBotList", "closeBotList", "selectBot", "getConversationHistory", "pagesize", "getMessages", "conversation_id", "sendMessage", "role", "content", "create_time_text", "bot_id", "message", "scrollToBottom", "formatTime", "date"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAAimB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuGrnB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAC;MACAC;QACAD;QACA;UACAA;UACA;YACAA;YACAA;UACA;QACA;UACAE;YACAC;YACAC;YACAC;UACA;QACA;QACAL;MACA;IACA;IAEA;IACAM;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACAR;QACAP;QACAgB;MACA;QACA;UACAV;UACAA;QACA;MACA;IACA;IAEA;IACAW;MACA;MAEA;MACAV;QACAW;QACAlB;QACAgB;MACA;QACA;UACAV;UACAA;QACA;MACA;IACA;IAEA;IACAa;MACA;MACA;QACAX;UACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;MACA;;MAEA;MACA;QACAS;QACAC;QACAC;MACA;MAEA;MACA;MAEA;MACAf;QACAgB;QACAC;QACAN;MACA;QACAZ;QACA;UACA;UACAA;YACAc;YACAC;YACAC;UACA;UAEA;YACAhB;UACA;QACA;UACAE;YACAC;YACAC;YACAC;UACA;QACA;QACAL;MACA;IACA;IAEA;IACAmB;MAAA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA;QAAA;QACA;MACA;QAAA;QACA;MACA;QAAA;QACA;MACA;QACA,kCACA,0DACAC,mDACAA,oDACAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClSA;AAAA;AAAA;AAAA;AAAo3B,CAAgB,m2BAAG,EAAC,C;;;;;;;;;;;ACAx4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesB/coze/chat.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesB/coze/chat.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./chat.vue?vue&type=template&id=30a7b20d&\"\nvar renderjs\nimport script from \"./chat.vue?vue&type=script&lang=js&\"\nexport * from \"./chat.vue?vue&type=script&lang=js&\"\nimport style0 from \"./chat.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesB/coze/chat.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat.vue?vue&type=template&id=30a7b20d&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.inputMessage.trim() : null\n  var l0 = _vm.isload\n    ? _vm.__map(_vm.botList, function (bot, index) {\n        var $orig = _vm.__get_orig(bot)\n        var g1 = JSON.stringify(bot)\n        var m0 = _vm.currentBot.bot_id === bot.bot_id ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          g1: g1,\n          m0: m0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<!-- 顶部导航 -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header-title\">扣子AI聊天</view>\n\t\t\t<view class=\"header-actions\">\n\t\t\t\t<view class=\"action-btn\" @tap=\"showBotList\">\n\t\t\t\t\t<text class=\"iconfont iconjiqiren\"></text>\n\t\t\t\t\t<text>选择机器人</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 机器人信息 -->\n\t\t<view class=\"bot-info\" v-if=\"currentBot.bot_id\">\n\t\t\t<image class=\"bot-avatar\" :src=\"currentBot.icon_url || '/static/img/default-bot.png'\"></image>\n\t\t\t<view class=\"bot-details\">\n\t\t\t\t<view class=\"bot-name\">{{currentBot.name}}</view>\n\t\t\t\t<view class=\"bot-desc\">{{currentBot.description}}</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 聊天消息列表 -->\n\t\t<scroll-view class=\"chat-messages\" scroll-y=\"true\" :scroll-top=\"scrollTop\" scroll-with-animation=\"true\">\n\t\t\t<view class=\"message-list\">\n\t\t\t\t<block v-for=\"(message, index) in messageList\" :key=\"index\">\n\t\t\t\t\t<view class=\"message-item\" :class=\"message.role === 'user' ? 'user-message' : 'bot-message'\">\n\t\t\t\t\t\t<view class=\"message-avatar\">\n\t\t\t\t\t\t\t<image v-if=\"message.role === 'user'\" :src=\"userInfo.avatar || '/static/img/default-user.png'\"></image>\n\t\t\t\t\t\t\t<image v-else :src=\"currentBot.icon_url || '/static/img/default-bot.png'\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"message-content\">\n\t\t\t\t\t\t\t<view class=\"message-text\">{{message.content}}</view>\n\t\t\t\t\t\t\t<view class=\"message-time\">{{message.create_time_text}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</block>\n\t\t\t\t<view v-if=\"isTyping\" class=\"message-item bot-message\">\n\t\t\t\t\t<view class=\"message-avatar\">\n\t\t\t\t\t\t<image :src=\"currentBot.icon_url || '/static/img/default-bot.png'\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"message-content\">\n\t\t\t\t\t\t<view class=\"typing-indicator\">\n\t\t\t\t\t\t\t<view class=\"typing-dot\"></view>\n\t\t\t\t\t\t\t<view class=\"typing-dot\"></view>\n\t\t\t\t\t\t\t<view class=\"typing-dot\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</scroll-view>\n\n\t\t<!-- 输入框 -->\n\t\t<view class=\"input-area\">\n\t\t\t<view class=\"input-container\">\n\t\t\t\t<view class=\"input-box\">\n\t\t\t\t\t<input \n\t\t\t\t\t\tv-model=\"inputMessage\" \n\t\t\t\t\t\tplaceholder=\"请输入消息...\" \n\t\t\t\t\t\tplaceholder-style=\"color:#999\"\n\t\t\t\t\t\t@confirm=\"sendMessage\"\n\t\t\t\t\t\tconfirm-type=\"send\"\n\t\t\t\t\t\t:disabled=\"isTyping\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"send-btn\" :class=\"inputMessage.trim() ? 'active' : ''\" @tap=\"sendMessage\">\n\t\t\t\t\t<text class=\"iconfont iconfasong\"></text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 机器人选择弹窗 -->\n\t\t<uni-popup ref=\"botListPopup\" type=\"bottom\">\n\t\t\t<view class=\"bot-list-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<view class=\"popup-title\">选择机器人</view>\n\t\t\t\t\t<view class=\"popup-close\" @tap=\"closeBotList\">\n\t\t\t\t\t\t<text class=\"iconfont iconguanbi\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<scroll-view class=\"bot-list\" scroll-y=\"true\">\n\t\t\t\t\t<block v-for=\"(bot, index) in botList\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"bot-item\" @tap=\"selectBot\" :data-bot=\"JSON.stringify(bot)\">\n\t\t\t\t\t\t\t<image class=\"bot-item-avatar\" :src=\"bot.icon_url || '/static/img/default-bot.png'\"></image>\n\t\t\t\t\t\t\t<view class=\"bot-item-info\">\n\t\t\t\t\t\t\t\t<view class=\"bot-item-name\">{{bot.name}}</view>\n\t\t\t\t\t\t\t\t<view class=\"bot-item-desc\">{{bot.description}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"bot-item-check\" v-if=\"currentBot.bot_id === bot.bot_id\">\n\t\t\t\t\t\t\t\t<text class=\"iconfont iconduihao\" :style=\"{color:t('color1')}\"></text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</block>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</block>\n\t<loading v-if=\"loading\"></loading>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tisload: false,\n\t\t\tloading: false,\n\t\t\tuserInfo: {},\n\t\t\tcurrentBot: {},\n\t\t\tbotList: [],\n\t\t\tmessageList: [],\n\t\t\tinputMessage: '',\n\t\t\tconversationId: '',\n\t\t\tisTyping: false,\n\t\t\tscrollTop: 0,\n\t\t\tpagenum: 1,\n\t\t\tnomore: false\n\t\t};\n\t},\n\n\tonLoad: function(opt) {\n\t\tthis.userInfo = app.getUserInfo();\n\t\tthis.getBotList();\n\t},\n\n\tonShow: function() {\n\t\tthis.scrollToBottom();\n\t},\n\n\tmethods: {\n\t\t// 获取机器人列表\n\t\tgetBotList: function() {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\tapp.post('ApiCoze/getbotlist', {}, function(res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tif (res.code === 1) {\n\t\t\t\t\tthat.botList = res.data || [];\n\t\t\t\t\tif (that.botList.length > 0 && !that.currentBot.bot_id) {\n\t\t\t\t\t\tthat.currentBot = that.botList[0];\n\t\t\t\t\t\tthat.getConversationHistory();\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tthat.loaded();\n\t\t\t});\n\t\t},\n\n\t\t// 显示机器人列表\n\t\tshowBotList: function() {\n\t\t\tthis.$refs.botListPopup.open();\n\t\t},\n\n\t\t// 关闭机器人列表\n\t\tcloseBotList: function() {\n\t\t\tthis.$refs.botListPopup.close();\n\t\t},\n\n\t\t// 选择机器人\n\t\tselectBot: function(e) {\n\t\t\tvar bot = JSON.parse(e.currentTarget.dataset.bot);\n\t\t\tthis.currentBot = bot;\n\t\t\tthis.messageList = [];\n\t\t\tthis.conversationId = '';\n\t\t\tthis.getConversationHistory();\n\t\t\tthis.closeBotList();\n\t\t},\n\n\t\t// 获取对话历史\n\t\tgetConversationHistory: function() {\n\t\t\tif (!this.currentBot.bot_id) return;\n\t\t\t\n\t\t\tvar that = this;\n\t\t\tapp.post('ApiCoze/getconversations', {\n\t\t\t\tpagenum: 1,\n\t\t\t\tpagesize: 1\n\t\t\t}, function(res) {\n\t\t\t\tif (res.code === 1 && res.data.length > 0) {\n\t\t\t\t\tthat.conversationId = res.data[0].conversation_id;\n\t\t\t\t\tthat.getMessages();\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 获取消息列表\n\t\tgetMessages: function() {\n\t\t\tif (!this.conversationId) return;\n\t\t\t\n\t\t\tvar that = this;\n\t\t\tapp.post('ApiCoze/getmessages', {\n\t\t\t\tconversation_id: that.conversationId,\n\t\t\t\tpagenum: that.pagenum,\n\t\t\t\tpagesize: 50\n\t\t\t}, function(res) {\n\t\t\t\tif (res.code === 1) {\n\t\t\t\t\tthat.messageList = res.data || [];\n\t\t\t\t\tthat.scrollToBottom();\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 发送消息\n\t\tsendMessage: function() {\n\t\t\tif (!this.inputMessage.trim() || this.isTyping) return;\n\t\t\tif (!this.currentBot.bot_id) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先选择机器人',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tvar message = this.inputMessage.trim();\n\t\t\tthis.inputMessage = '';\n\n\t\t\t// 添加用户消息到列表\n\t\t\tthis.messageList.push({\n\t\t\t\trole: 'user',\n\t\t\t\tcontent: message,\n\t\t\t\tcreate_time_text: this.formatTime(new Date())\n\t\t\t});\n\n\t\t\tthis.isTyping = true;\n\t\t\tthis.scrollToBottom();\n\n\t\t\tvar that = this;\n\t\t\tapp.post('ApiCoze/chat', {\n\t\t\t\tbot_id: that.currentBot.bot_id,\n\t\t\t\tmessage: message,\n\t\t\t\tconversation_id: that.conversationId\n\t\t\t}, function(res) {\n\t\t\t\tthat.isTyping = false;\n\t\t\t\tif (res.code === 1) {\n\t\t\t\t\t// 添加机器人回复到列表\n\t\t\t\t\tthat.messageList.push({\n\t\t\t\t\t\trole: 'assistant',\n\t\t\t\t\t\tcontent: res.data.content || res.msg,\n\t\t\t\t\t\tcreate_time_text: that.formatTime(new Date())\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (res.data.conversation_id) {\n\t\t\t\t\t\tthat.conversationId = res.data.conversation_id;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tthat.scrollToBottom();\n\t\t\t});\n\t\t},\n\n\t\t// 滚动到底部\n\t\tscrollToBottom: function() {\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.scrollTop = 999999;\n\t\t\t});\n\t\t},\n\n\t\t// 格式化时间\n\t\tformatTime: function(date) {\n\t\t\tvar now = new Date();\n\t\t\tvar diff = now - date;\n\t\t\t\n\t\t\tif (diff < 60000) { // 1分钟内\n\t\t\t\treturn '刚刚';\n\t\t\t} else if (diff < 3600000) { // 1小时内\n\t\t\t\treturn Math.floor(diff / 60000) + '分钟前';\n\t\t\t} else if (diff < 86400000) { // 24小时内\n\t\t\t\treturn Math.floor(diff / 3600000) + '小时前';\n\t\t\t} else {\n\t\t\t\treturn date.getFullYear() + '-' + \n\t\t\t\t\t   (date.getMonth() + 1).toString().padStart(2, '0') + '-' + \n\t\t\t\t\t   date.getDate().toString().padStart(2, '0') + ' ' +\n\t\t\t\t\t   date.getHours().toString().padStart(2, '0') + ':' + \n\t\t\t\t\t   date.getMinutes().toString().padStart(2, '0');\n\t\t\t}\n\t\t}\n\t}\n};\n</script>\n\n<style>\n.container {\n\theight: 100vh;\n\tdisplay: flex;\n\tflex-direction: column;\n\tbackground: #f5f5f5;\n}\n\n.header {\n\tbackground: #fff;\n\tpadding: 20rpx 30rpx;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tborder-bottom: 1rpx solid #eee;\n\tposition: fixed;\n\ttop: var(--window-top);\n\tleft: 0;\n\tright: 0;\n\tz-index: 100;\n}\n\n.header-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.header-actions {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.action-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 10rpx 20rpx;\n\tbackground: #f0f0f0;\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n.action-btn .iconfont {\n\tmargin-right: 10rpx;\n\tfont-size: 28rpx;\n}\n\n.bot-info {\n\tbackground: #fff;\n\tpadding: 20rpx 30rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tborder-bottom: 1rpx solid #eee;\n\tmargin-top: 120rpx;\n}\n\n.bot-avatar {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tmargin-right: 20rpx;\n}\n\n.bot-details {\n\tflex: 1;\n}\n\n.bot-name {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 10rpx;\n}\n\n.bot-desc {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.chat-messages {\n\tflex: 1;\n\tpadding: 20rpx 30rpx;\n}\n\n.message-item {\n\tdisplay: flex;\n\tmargin-bottom: 30rpx;\n}\n\n.user-message {\n\tflex-direction: row-reverse;\n}\n\n.message-avatar {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tmargin: 0 20rpx;\n}\n\n.message-avatar image {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: 30rpx;\n}\n\n.message-content {\n\tmax-width: 70%;\n}\n\n.user-message .message-content {\n\ttext-align: right;\n}\n\n.message-text {\n\tbackground: #fff;\n\tpadding: 20rpx;\n\tborder-radius: 20rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tword-wrap: break-word;\n}\n\n.user-message .message-text {\n\tbackground: #007aff;\n\tcolor: #fff;\n}\n\n.message-time {\n\tfont-size: 20rpx;\n\tcolor: #999;\n\tmargin-top: 10rpx;\n}\n\n.typing-indicator {\n\tbackground: #fff;\n\tpadding: 20rpx;\n\tborder-radius: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.typing-dot {\n\twidth: 8rpx;\n\theight: 8rpx;\n\tborder-radius: 50%;\n\tbackground: #999;\n\tmargin-right: 8rpx;\n\tanimation: typing 1.4s infinite ease-in-out;\n}\n\n.typing-dot:nth-child(1) { animation-delay: -0.32s; }\n.typing-dot:nth-child(2) { animation-delay: -0.16s; }\n\n@keyframes typing {\n\t0%, 80%, 100% { transform: scale(0); }\n\t40% { transform: scale(1); }\n}\n\n.input-area {\n\tbackground: #fff;\n\tpadding: 20rpx 30rpx;\n\tborder-top: 1rpx solid #eee;\n}\n\n.input-container {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.input-box {\n\tflex: 1;\n\tbackground: #f5f5f5;\n\tborder-radius: 30rpx;\n\tpadding: 0 30rpx;\n\tmargin-right: 20rpx;\n}\n\n.input-box input {\n\theight: 60rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.send-btn {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tborder-radius: 30rpx;\n\tbackground: #ccc;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tcolor: #fff;\n\tfont-size: 28rpx;\n}\n\n.send-btn.active {\n\tbackground: #007aff;\n}\n\n.bot-list-popup {\n\tbackground: #fff;\n\tborder-radius: 20rpx 20rpx 0 0;\n\tmax-height: 80vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.popup-header {\n\tpadding: 30rpx;\n\tborder-bottom: 1rpx solid #eee;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.popup-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.popup-close {\n\tfont-size: 32rpx;\n\tcolor: #999;\n}\n\n.bot-list {\n\tflex: 1;\n\tpadding: 0 30rpx;\n}\n\n.bot-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 30rpx 0;\n\tborder-bottom: 1rpx solid #f5f5f5;\n}\n\n.bot-item-avatar {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tmargin-right: 20rpx;\n}\n\n.bot-item-info {\n\tflex: 1;\n}\n\n.bot-item-name {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 10rpx;\n}\n\n.bot-item-desc {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.bot-item-check {\n\tfont-size: 32rpx;\n}\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753981067210\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}