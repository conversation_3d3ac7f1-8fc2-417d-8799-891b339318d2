# 梦想启蒙"与未来对话"功能开发说明

## 功能概述

在梦想启蒙功能的基础上，新增了"与未来对话"功能，允许用户根据性别跳转到不同的外部链接进行对话。

## 功能特点

1. **性别区分跳转**：根据用户性别（男/女）跳转到不同的对话链接
2. **灵活配置**：后台可以配置男性和女性用户的不同链接
3. **按钮文字自定义**：可以自定义按钮显示的文字
4. **多平台支持**：支持小程序、APP、H5等多个平台的链接跳转

## 数据库更新

### 新增字段

为 `ddwx_dream_inspiration_settings` 表添加了以下字段：

```sql
-- 是否启用与未来对话功能
future_talk_enabled tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用与未来对话：0禁用 1启用'

-- 男性用户链接
future_talk_male_url varchar(500) NOT NULL DEFAULT '' COMMENT '男性用户与未来对话链接'

-- 女性用户链接  
future_talk_female_url varchar(500) NOT NULL DEFAULT '' COMMENT '女性用户与未来对话链接'

-- 按钮显示文字
future_talk_button_text varchar(100) NOT NULL DEFAULT '与未来对话' COMMENT '按钮显示文字'
```

### 执行更新

数据库更新文件：`update_dream_inspiration_future_talk.sql`

## 后端实现

### 1. 控制器更新

#### DreamInspiration.php (后台设置)
- 在 `setting()` 方法中添加了对新字段的保存处理
- 在获取设置时返回新字段的默认值

#### ApiDreamInspiration.php (前端API)
- 在 `getSetting()` 方法中返回与未来对话相关配置
- 新增 `getFutureTalkUrl()` 方法，根据用户性别返回对应链接

### 2. 核心API接口

#### 获取与未来对话链接
```
接口地址：ApiDreamInspiration/getFutureTalkUrl
请求方式：POST
参数：无（自动获取当前用户信息）

返回格式：
{
    "code": 1,
    "msg": "获取成功", 
    "data": {
        "url": "https://example.com/male-chat",
        "button_text": "与未来对话",
        "user_gender": 1
    }
}
```

#### 性别判断逻辑
- `sex = 1`：男性，使用 `future_talk_male_url`
- `sex = 0`：女性，使用 `future_talk_female_url`  
- `sex = 3` 或其他：未知性别，默认使用男性链接

## 前端实现

### 1. 设置页面更新

在 `app/home/<USER>/setting.html` 中添加了：

- 与未来对话功能开关
- 按钮文字设置
- 男性用户链接设置（支持链接选择器）
- 女性用户链接设置（支持链接选择器）

### 2. 前端页面更新

在 `pagesB/dreamark/camera-new.vue` 中：

- 修改了 `startFutureChat()` 方法
- 调用 `ApiDreamInspiration/getFutureTalkUrl` 获取链接
- 支持内部页面和外部链接的跳转
- 支持多平台跳转（小程序WebView、APP、H5）

### 3. 跳转逻辑

```javascript
// 外部链接跳转
if (futureTalkUrl.startsWith('http://') || futureTalkUrl.startsWith('https://')) {
    // 小程序：使用WebView页面
    // APP：使用plus.runtime.openURL
    // H5：使用window.open
} else {
    // 内部页面：使用uni.navigateTo
}
```

## 配置说明

### 1. 后台配置步骤

1. 访问：`http://localhost/?s=/DreamInspiration/setting`
2. 在"与未来对话设置"区域进行配置：
   - 开启功能开关
   - 设置按钮文字（默认：与未来对话）
   - 配置男性用户链接
   - 配置女性用户链接
3. 点击"选择链接"按钮可以选择系统内置链接
4. 保存设置

### 2. 链接配置建议

- **外部链接**：`https://example.com/chat`
- **内部页面**：`/pagesB/chat/index`
- **带参数**：`/pagesB/chat/index?type=future&gender=male`

## 使用流程

1. 用户在梦想启蒙页面生成图片后
2. 在结果页面点击"与未来对话"按钮
3. 系统获取用户性别信息
4. 根据性别选择对应的链接
5. 跳转到配置的对话页面

## 注意事项

1. **数据库更新**：需要先执行SQL更新脚本
2. **权限配置**：确保用户有访问对话链接的权限
3. **链接有效性**：定期检查配置的外部链接是否有效
4. **性别信息**：确保用户资料中有正确的性别信息
5. **平台兼容**：不同平台的跳转方式可能有差异

## 测试建议

1. 测试不同性别用户的跳转
2. 测试内部链接和外部链接
3. 测试不同平台（小程序、APP、H5）
4. 测试功能开关的启用/禁用
5. 测试链接选择器功能

## 扩展可能

1. 支持更多性别选项
2. 支持根据年龄、地区等条件跳转
3. 支持多个链接随机选择
4. 支持链接访问统计
5. 支持对话记录保存
