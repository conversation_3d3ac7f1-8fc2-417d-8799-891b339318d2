{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?8659", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?b225", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?583b", "uni-app:///pagesB/dreamark/camera-new.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?ddb7", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/dreamark/camera-new.vue?9a24"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentStep", "currentStatus", "showCamera", "camera<PERSON><PERSON>y", "cameraStatusText", "capturedImageUrl", "predictedImageUrl", "progressPercent", "currentProcessStep", "processingSteps", "icon", "text", "showConfig", "showConfigTip", "genderIndex", "genderOptions", "userProfession", "imageLoaded", "currentImageLoaded", "predictedImageLoaded", "processingTimer", "progressTimer", "pre_url", "processingAudio", "onLoad", "console", "setTimeout", "onUnload", "methods", "loadUserConfig", "gender", "profession", "initCamera", "onCameraReady", "onCameraError", "capturePhoto", "chooseImage", "uploadImage", "uni", "title", "mask", "app", "that", "switchCamera", "retakePhoto", "onImageLoad", "onImageError", "onCurrentImageLoad", "onCurrentImageError", "onPredictedImageLoad", "onPredictedImageError", "startProcessing", "playProcessingAudio", "stopProcessingAudio", "simulateAIProcessing", "clearInterval", "completeProcessing", "generateFutureImage", "userGender", "content", "showCancel", "confirmText", "success", "callAIAPI", "dream_content", "image_url", "checkGenerationStatus", "pollCount", "record_id", "checkStatus", "handleAPIError", "startFutureChat", "url", "fail", "getMyDreamRecords", "page", "limit", "viewHistory", "checkDreamInspirationSettings", "goBack", "selectGender", "onInputFocus", "onInputBlur", "onProfessionInput", "onInputConfirm", "saveConfig", "doSaveConfig", "skipConfig", "hideConfigModal", "forceShowConfig", "clearTimers"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACqC;;;AAG9F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1IA;AAAA;AAAA;AAAA;AAAumB,CAAgB,onBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8Z3nB;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,kBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MAAA;MACAC;IACA;EACA;EAEAC;IAAA;IACAC;;IAEA;IACA;IACA;IAEAC;MACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACAC;MACAJ;;MAEA;MACA;QACA;QACA;QAEA;UACA;UACA;YACA;UACA;QACA;QAEA;UACA;QACA;QAEAA;UACAK;UACAC;UACAjB;QACA;MACA;QACAW;MACA;MAEA;MACA;MACAA;IACA;IAEA;IACAO;MACAP;MACA;MACA;MACA;MACAA;IACA;IAEA;IACAQ;MACA;MACA;MACA;MACAR;IACA;IAEA;IACAS;MACAT;MACA;IACA;IAEA;IACAU;MACAV;MACA;IACA;IAEA;IACAW;MACAX;MACA;IACA;IAEA;IACAY;MAAA;MACA;MACA;;MAEA;MACAC;QACAC;QACAC;MACA;;MAEA;MACAC;QACAH;QAEA;UACA;UACAI;UACAA;;UAEA;UACAA;UACAA;UACAA;UAEAjB;UACAA;UACAA;UAEAa;YACAC;YACA7B;UACA;QACA;UACA4B;YACAC;YACA7B;UACA;QACA;MACA;IACA;IAEA;IACAiC;MACAlB;MACA;IACA;IAEA;IACAmB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACApB;IACA;IAEAqB;MACArB;MACAa;QACAC;QACA7B;MACA;IACA;IAEAqC;MACA;MACAtB;IACA;IAEAuB;MACAvB;MACA;IACA;IAEAwB;MACA;MACAxB;IACA;IAEAyB;MACAzB;MACA;MACAa;QACAC;QACA7B;MACA;IACA;IAEA;IACAyC;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;UACA3B;QACA;;QAEA;QACA;UACAA;UACA;UACA;QACA;;QAEA;QACA;UACAA;UACA;UACA;QACA;;QAEA;QACA;;QAEA;QACAC;UACA;QACA;MAEA;QACAD;MACA;IACA;IAEA;IACA4B;MACA;QACA;UACA;UACA;UACA;UACA5B;QACA;UACAA;QACA;MACA;IACA;IAEA;IACA6B;MAAA;MACA;MACA;QACA;UACA;QACA;MACA;;MAEA;MACA;QACA;QACA;UACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;MACA;MAEAhC;QACAiC;QACA1C;QACAX;MACA;MAEA;QACAoB;QACAa;UACAC;UACAoB;UACAC;UACAC;UACAC;YACA;UACA;QACA;QACA;MACA;;MAEA;MACA;QACArC;QACAa;UACAC;UACA7B;QACA;QACA;MACA;MAEAe;MACA;MACA;IACA;IAEA;IACAsC;MAAA;MACAtC;;MAEA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;QACAK;QACAkC;QACAC;MACA;;MAEAxC;MACAA;;MAEA;MACA;;MAEA;MACAgB;QACAhB;QACA;UACA;UACA;;UAEA;UACAa;YACAC;YACAC;UACA;;UAEA;UACA;QACA;UACA;QACA;MACA;QACAf;QACA;MACA;IACA;IAEA;IACAyC;MAAA;MACA;MACA;MACA;;MAEA;QACAC;QACA1C;QAEAgB;UACA2B;QACA;UACA3C;UAEA;YACA;YAEA;cACA;cACAa;;cAEA;cACAb;cAEA;cACA;;cAEA;cACA;;cAEA;cACA;;cAEA;cACAa;cAEAA;gBACAC;gBACA7B;cACA;;cAEA;cACAe;YACA;cACA;cACAa;cACA;YACA;cACA;cACA;gBACA;gBACAA;gBACA;cACA;gBACA;gBACAZ;cACA;YACA;cACA;cACAY;cACA;YACA;UACA;YACAA;YACA;UACA;QACA;UACAb;UACAa;UACA;QACA;MACA;;MAEA;MACA+B;IACA;IAEA;IACAC;MAAA;MACAhC;QACAC;QACAoB;QACAG;UACA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAS;MACA9C;;MAEA;MACAa;QACAC;QACAC;MACA;;MAEA;MACA;;MAEA;MACAC;QACAH;QACAb;QAEA;UACA;UACA;UAEA;YACAA;;YAEA;YACA;cACA;;cAEA;cACAa;gBACAkC;gBACAV;kBACArC;gBACA;gBACAgD;kBACAhD;kBACAa;oBACAC;oBACA7B;kBACA;gBACA;cACA;YAUA;cACA;cACA4B;gBACAkC;gBACAV;kBACArC;gBACA;gBACAgD;kBACAhD;kBACAa;oBACAC;oBACA7B;kBACA;gBACA;cACA;YACA;UACA;YACA4B;cACAC;cACA7B;YACA;UACA;QACA;UACA4B;YACAC;YACA7B;UACA;QACA;MACA;QACA4B;QACAb;QACAa;UACAC;UACA7B;QACA;MACA;IACA;IAEA;IACAgE;MACA;MACAjC;QACAkC;QACAC;MACA;QACA;UACAnD;UACA;QACA;UACAA;QACA;MACA;QACAA;MACA;IACA;IAEA;IACAoD;MACA;MACA;IACA;IAEA;IACAC;MACA;MACArC;QACA;UACA;UACA;YACAH;cACAC;cACAoB;cACAC;cACAC;cACAC;gBACAxB;cACA;YACA;UACA;QACA;UACAb;QACA;MACA;QACAA;MACA;IACA;IAEA;IACAsD;MACAzC;IACA;IAEA;IACA0C;MACA;MACA;IACA;IAEAC;MACAxD;IACA;IAEAyD;MACAzD;IACA;IAEA0D;MACA;MACA1D;IACA;IAEA2D;MACA;MACA3D;MACA;IACA;IAEA4D;MAAA;MACA;MACA;QACA/C;UACAC;UACA7B;QACA;QACA;MACA;;MAEA;MACA;QACA4B;UACAC;UACAoB;UACAG;YACA;cACA;YACA;UACA;QACA;QACA;MACA;MAEA;IACA;IAEAwB;MACA;QACA;QACAhD;QACAA;QAEAb;UACAK;UACAC;QACA;QAEAO;UACAC;UACA7B;QACA;QAEA;MACA;QACAe;QACAa;UACAC;UACA7B;QACA;MACA;IACA;IAEA;IACA6E;MAAA;MACAjD;QACAC;QACAoB;QACAG;UACA;YACA;YACAxB;YACAA;YAEAA;cACAC;cACA7B;YACA;YAEA;UACA;QACA;MACA;IACA;IAEA8E;MACA;QACAlD;UACAC;UACA7B;QACA;QACA;MACA;MACA;MACA;IACA;IAEA;IACA+E;MACA;MACA;IACA;IAEA;IACAC;MACA;QACAnC;QACA;MACA;MACA;QACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChrCA;AAAA;AAAA;AAAA;AAAk5B,CAAgB,i4BAAG,EAAC,C;;;;;;;;;;;ACAt6B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesB/dreamark/camera-new.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesB/dreamark/camera-new.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./camera-new.vue?vue&type=template&id=368f168d&scoped=true&\"\nvar renderjs\nimport script from \"./camera-new.vue?vue&type=script&lang=js&\"\nexport * from \"./camera-new.vue?vue&type=script&lang=js&\"\nimport style0 from \"./camera-new.vue?vue&type=style&index=0&id=368f168d&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"368f168d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesB/dreamark/camera-new.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./camera-new.vue?vue&type=template&id=368f168d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.currentStep === \"camera\" ? _vm.t(\"color1\") : null\n  var m1 = _vm.currentStep === \"camera\" ? _vm.t(\"color1\") : null\n  var m2 = _vm.currentStep === \"camera\" ? _vm.t(\"color1\") : null\n  var m3 = _vm.currentStep === \"camera\" ? _vm.t(\"color1\") : null\n  var m4 = _vm.currentStep === \"camera\" ? _vm.t(\"color1\") : null\n  var m5 = _vm.currentStep === \"preview\" ? _vm.t(\"color1\") : null\n  var m6 = _vm.currentStep === \"preview\" ? _vm.t(\"color1rgb\") : null\n  var m7 = _vm.currentStep === \"preview\" ? _vm.t(\"color1\") : null\n  var m8 = _vm.currentStep === \"processing\" ? _vm.t(\"color1\") : null\n  var m9 = _vm.currentStep === \"processing\" ? _vm.t(\"color1rgb\") : null\n  var m10 = _vm.currentStep === \"processing\" ? _vm.t(\"color1\") : null\n  var m11 = _vm.currentStep === \"processing\" ? _vm.t(\"color1\") : null\n  var m12 = _vm.currentStep === \"processing\" ? _vm.t(\"color1\") : null\n  var l0 =\n    _vm.currentStep === \"processing\"\n      ? _vm.__map(_vm.processingSteps, function (step, index) {\n          var $orig = _vm.__get_orig(step)\n          var m13 = _vm.t(\"color1\")\n          var m14 = index <= _vm.currentProcessStep ? _vm.t(\"color1\") : null\n          var m15 = index <= _vm.currentProcessStep ? _vm.t(\"color1\") : null\n          return {\n            $orig: $orig,\n            m13: m13,\n            m14: m14,\n            m15: m15,\n          }\n        })\n      : null\n  var m16 = _vm.currentStep === \"processing\" ? _vm.t(\"color1\") : null\n  var m17 = _vm.currentStep === \"processing\" ? _vm.t(\"color1\") : null\n  var m18 = _vm.currentStep === \"processing\" ? _vm.t(\"color1rgb\") : null\n  var m19 = _vm.currentStep === \"processing\" ? _vm.t(\"color1\") : null\n  var m20 = _vm.currentStep === \"result\" ? _vm.t(\"color1\") : null\n  var m21 = _vm.currentStep === \"result\" ? _vm.t(\"color1rgb\") : null\n  var m22 = _vm.currentStep === \"result\" ? _vm.t(\"color1\") : null\n  var m23 = _vm.currentStep === \"result\" ? _vm.t(\"color1\") : null\n  var m24 = _vm.currentStep === \"result\" ? _vm.t(\"color1\") : null\n  var m25 = _vm.currentStep === \"result\" ? _vm.t(\"color1\") : null\n  var m26 =\n    _vm.currentStep === \"result\" && !_vm.predictedImageLoaded\n      ? _vm.t(\"color1\")\n      : null\n  var m27 = _vm.currentStep === \"result\" ? _vm.t(\"color1\") : null\n  var m28 = _vm.currentStep === \"result\" ? _vm.t(\"color1\") : null\n  var m29 = _vm.currentStep === \"result\" ? _vm.t(\"color1\") : null\n  var m30 = _vm.currentStep === \"result\" ? _vm.t(\"color1\") : null\n  var l1 =\n    _vm.currentStep === \"result\"\n      ? _vm.__map(5, function (i, __i2__) {\n          var $orig = _vm.__get_orig(i)\n          var m31 = _vm.t(\"color1\")\n          return {\n            $orig: $orig,\n            m31: m31,\n          }\n        })\n      : null\n  var m32 = _vm.showConfig ? _vm.t(\"color1\") : null\n  var m33 = _vm.showConfig && _vm.showConfigTip ? _vm.t(\"color1\") : null\n  var m34 = _vm.showConfig ? _vm.t(\"color1\") : null\n  var l2 = _vm.showConfig\n    ? _vm.__map(_vm.genderOptions, function (option, index) {\n        var $orig = _vm.__get_orig(option)\n        var m35 =\n          index > 0 && _vm.genderIndex === index ? _vm.t(\"color1\") : null\n        var m36 =\n          index > 0 && _vm.genderIndex === index ? _vm.t(\"color1rgb\") : null\n        var m37 =\n          index > 0 && _vm.genderIndex === index ? _vm.t(\"color1\") : null\n        return {\n          $orig: $orig,\n          m35: m35,\n          m36: m36,\n          m37: m37,\n        }\n      })\n    : null\n  var m38 = _vm.showConfig ? _vm.t(\"color1\") : null\n  var m39 = _vm.showConfig && _vm.userProfession ? _vm.t(\"color1\") : null\n  var m40 = _vm.showConfig && _vm.userProfession ? _vm.t(\"color1\") : null\n  var m41 = _vm.showConfig && _vm.userProfession ? _vm.t(\"color1\") : null\n  var m42 = _vm.showConfig ? _vm.t(\"color1\") : null\n  var m43 = _vm.showConfig ? _vm.t(\"color1rgb\") : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n        m10: m10,\n        m11: m11,\n        m12: m12,\n        l0: l0,\n        m16: m16,\n        m17: m17,\n        m18: m18,\n        m19: m19,\n        m20: m20,\n        m21: m21,\n        m22: m22,\n        m23: m23,\n        m24: m24,\n        m25: m25,\n        m26: m26,\n        m27: m27,\n        m28: m28,\n        m29: m29,\n        m30: m30,\n        l1: l1,\n        m32: m32,\n        m33: m33,\n        m34: m34,\n        l2: l2,\n        m38: m38,\n        m39: m39,\n        m40: m40,\n        m41: m41,\n        m42: m42,\n        m43: m43,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./camera-new.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./camera-new.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"camera-container\">\n\t\t<!-- 背景装饰 -->\n\t\t<view class=\"bg-decoration\">\n\t\t\t<view class=\"bg-grid\"></view>\n\t\t\t<view class=\"bg-particles\">\n\t\t\t\t<view class=\"particle\" v-for=\"n in 20\" :key=\"n\"></view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 拍摄模式 -->\n\t\t<view v-if=\"currentStep === 'camera'\" class=\"camera-mode\">\n\t\t\t<!-- 相机预览区域 -->\n\t\t\t<!-- #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ || APP-PLUS -->\n\t\t\t<camera\n\t\t\t\tv-if=\"showCamera && !showConfig\"\n\t\t\t\tclass=\"camera-preview\"\n\t\t\t\tdevice-position=\"front\"\n\t\t\t\tflash=\"off\"\n\t\t\t\t@error=\"onCameraError\"\n\t\t\t\t@initdone=\"onCameraReady\">\n\t\t\t</camera>\n\t\t\t<!-- #endif -->\n\n\t\t\t<!-- #ifdef H5 -->\n\t\t\t<!-- H5环境下显示替代预览区域 -->\n\t\t\t<view v-if=\"showCamera && !showConfig\" class=\"camera-preview h5-camera-placeholder\">\n\t\t\t\t<view class=\"h5-camera-content\">\n\t\t\t\t\t<view class=\"camera-icon\">📷</view>\n\t\t\t\t\t<text class=\"h5-camera-text\">相机预览</text>\n\t\t\t\t\t<text class=\"h5-camera-tip\">点击拍照按钮选择图片</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<!-- #endif -->\n\n\t\t\t<!-- 摄像头被暂停时的占位符 -->\n\t\t\t<view v-if=\"showConfig\" class=\"camera-placeholder\">\n\t\t\t\t<view class=\"placeholder-icon\">⏸️</view>\n\t\t\t\t<text class=\"placeholder-text\">摄像头已暂停</text>\n\t\t\t\t<text class=\"placeholder-desc\">完成配置后将自动恢复</text>\n\t\t\t</view>\n\n\t\t\t<!-- 顶部状态栏 -->\n\t\t\t<view class=\"status-bar\">\n\t\t\t\t<view class=\"status-left\">\n\t\t\t\t\t<view class=\"back-btn\" @tap=\"goBack\">\n\t\t\t\t\t\t<text class=\"back-icon\">←</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"status-center\">\n\t\t\t\t\t<text class=\"page-title\">AI变脸预测</text>\n\t\t\t\t\t<text class=\"page-subtitle\">FACE PREDICTION</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"status-right\">\n\t\t\t\t\t<view class=\"settings-btn\" @tap=\"forceShowConfig\">\n\t\t\t\t\t\t<text class=\"settings-icon\">⚙️</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 拍摄指导区域 -->\n\t\t\t<view class=\"guide-overlay\">\n\t\t\t\t<view class=\"guide-frame\">\n\t\t\t\t\t<view class=\"frame-border\">\n\t\t\t\t\t\t<view class=\"frame-corner corner-tl\"></view>\n\t\t\t\t\t\t<view class=\"frame-corner corner-tr\"></view>\n\t\t\t\t\t\t<view class=\"frame-corner corner-bl\"></view>\n\t\t\t\t\t\t<view class=\"frame-corner corner-br\"></view>\n\t\t\t\t\t\t<view class=\"scan-line\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"frame-center\">\n\t\t\t\t\t\t<view class=\"guide-icon\">👤</view>\n\t\t\t\t\t\t<text class=\"guide-text\">请将面部置于框内</text>\n\t\t\t\t\t\t<view class=\"pulse-indicator\">\n\t\t\t\t\t\t\t<view class=\"pulse-dot\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 底部控制区域 -->\n\t\t\t<view class=\"bottom-controls\">\n\t\t\t\t<view class=\"controls-container\">\n\t\t\t\t\t<view class=\"control-item\" @tap=\"chooseImage\">\n\t\t\t\t\t\t<view class=\"control-icon\" :style=\"{borderColor:t('color1')}\">\n\t\t\t\t\t\t\t<text class=\"icon-text\">📁</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"control-text\">相册</text>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"capture-section\">\n\t\t\t\t\t\t<view class=\"capture-button\" @tap=\"capturePhoto\" :class=\"{disabled: !cameraReady}\" :style=\"{borderColor:t('color1')}\">\n\t\t\t\t\t\t\t<view class=\"capture-ring\" :style=\"{borderColor:t('color1')}\">\n\t\t\t\t\t\t\t\t<view class=\"capture-inner\" :style=\"{background:t('color1')}\">\n\t\t\t\t\t\t\t\t\t<view class=\"capture-dot\"></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"capture-text\">拍照</text>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"control-item\" @tap=\"switchCamera\">\n\t\t\t\t\t\t<view class=\"control-icon\" :style=\"{borderColor:t('color1')}\">\n\t\t\t\t\t\t\t<text class=\"icon-text\">🔄</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"control-text\">翻转</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 摄像头状态提示 -->\n\t\t\t<view class=\"camera-status-tip\" v-if=\"!cameraReady\">\n\t\t\t\t<view class=\"status-icon\">⚡</view>\n\t\t\t\t<text class=\"status-tip-text\">{{cameraStatusText}}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 预览模式 -->\n\t\t<view v-if=\"currentStep === 'preview'\" class=\"preview-mode\">\n\t\t\t<view class=\"preview-header\">\n\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t<view class=\"back-btn\" @tap=\"retakePhoto\">\n\t\t\t\t\t\t<text class=\"back-icon\">←</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"header-center\">\n\t\t\t\t\t<text class=\"page-title\">预览照片</text>\n\t\t\t\t\t<text class=\"page-subtitle\">PHOTO PREVIEW</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"header-right\">\n\t\t\t\t\t<view class=\"confirm-btn\" @tap=\"startProcessing\" :style=\"{background:'linear-gradient(45deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">\n\t\t\t\t\t\t<text class=\"confirm-text\">确认</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"preview-content\">\n\t\t\t\t<view class=\"preview-container\">\n\t\t\t\t\t<view class=\"image-frame\" :style=\"{borderColor:t('color1'), background: 'rgba(255,255,255,0.1)'}\">\n\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t:src=\"capturedImageUrl\"\n\t\t\t\t\t\t\tclass=\"preview-image\"\n\t\t\t\t\t\t\tmode=\"aspectFit\"\n\t\t\t\t\t\t\t@error=\"onImageError\"\n\t\t\t\t\t\t\t@load=\"onImageLoad\"\n\t\t\t\t\t\t\t:show-loading=\"true\"\n\t\t\t\t\t\t\t:lazy-load=\"false\"\n\t\t\t\t\t\t\tstyle=\"width: 100%; height: 600rpx; display: block;\">\n\t\t\t\t\t\t</image>\n\t\t\t\t\t\t<view class=\"image-overlay\" v-if=\"imageLoaded\">\n\t\t\t\t\t\t\t<view class=\"scan-effect\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"image-loading\" v-if=\"!imageLoaded\">\n\t\t\t\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"preview-info\">\n\t\t\t\t\t\t<text class=\"info-title\">照片已准备就绪</text>\n\t\t\t\t\t\t<text class=\"info-desc\">点击确认开始AI分析</text>\n\t\t\t\t\t\t<view class=\"image-info\" v-if=\"imageLoaded\">\n\t\t\t\t\t\t\t<text class=\"info-detail\">图片尺寸已优化，准备进行AI分析</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 处理中模式 -->\n\t\t<view v-if=\"currentStep === 'processing'\" class=\"processing-mode\">\n\t\t\t<view class=\"processing-content\">\n\t\t\t\t<view class=\"ai-analysis\">\n\t\t\t\t\t<view class=\"ai-brain\">\n\t\t\t\t\t\t<view class=\"brain-core\" :style=\"{background:'radial-gradient(circle, '+t('color1')+', rgba('+t('color1rgb')+',0.3))'}\"></view>\n\t\t\t\t\t\t<view class=\"neural-waves\" :style=\"{borderColor:t('color1')}\"></view>\n\t\t\t\t\t\t<view class=\"neural-waves wave-2\" :style=\"{borderColor:t('color1')}\"></view>\n\t\t\t\t\t\t<view class=\"neural-waves wave-3\" :style=\"{borderColor:t('color1')}\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"processing-steps\">\n\t\t\t\t\t\t<view class=\"step-item\" v-for=\"(step, index) in processingSteps\" :key=\"index\" :class=\"{active: index <= currentProcessStep}\">\n\t\t\t\t\t\t\t<view class=\"step-icon\" :style=\"{borderColor:t('color1'), background: index <= currentProcessStep ? t('color1') : 'transparent'}\">\n\t\t\t\t\t\t\t\t<text class=\"step-text\">{{step.icon}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"step-label\" :style=\"{color: index <= currentProcessStep ? t('color1') : 'rgba(125, 249, 255, 0.5)'}\">{{step.text}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"processing-info\">\n\t\t\t\t\t<text class=\"processing-title\" :style=\"{color:t('color1')}\">AI正在分析中...</text>\n\t\t\t\t\t<text class=\"processing-desc\">{{processingSteps[currentProcessStep].text}}</text>\n\t\t\t\t\t<view class=\"progress-container\">\n\t\t\t\t\t\t<view class=\"progress-bar\">\n\t\t\t\t\t\t\t<view class=\"progress-fill\" :style=\"{width: progressPercent + '%', background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"progress-text\" :style=\"{color:t('color1')}\">{{progressPercent}}%</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 结果模式 -->\n\t\t<view v-if=\"currentStep === 'result'\" class=\"result-mode\">\n\t\t\t<view class=\"result-header\">\n\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t<view class=\"back-btn\" @tap=\"retakePhoto\">\n\t\t\t\t\t\t<text class=\"back-icon\">←</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"header-center\">\n\t\t\t\t\t<text class=\"page-title\">预测结果</text>\n\t\t\t\t\t<text class=\"page-subtitle\">PREDICTION RESULT</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"header-right\">\n\t\t\t\t\t<view class=\"share-btn\" @tap=\"shareResult\" :style=\"{background:'linear-gradient(45deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">\n\t\t\t\t\t\t<text class=\"share-text\">分享</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"result-content\">\n\t\t\t\t<view class=\"result-comparison\">\n\t\t\t\t\t<view class=\"comparison-container\">\n\t\t\t\t\t\t<view class=\"image-item\">\n\t\t\t\t\t\t\t<view class=\"image-frame current\" :style=\"{borderColor:t('color1'), background: 'rgba(125, 249, 255, 0.1)'}\">\n\t\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\t\t:src=\"capturedImageUrl\"\n\t\t\t\t\t\t\t\t\tclass=\"result-image\"\n\t\t\t\t\t\t\t\t\tmode=\"aspectFit\"\n\t\t\t\t\t\t\t\t\t@error=\"onCurrentImageError\"\n\t\t\t\t\t\t\t\t\t@load=\"onCurrentImageLoad\"\n\t\t\t\t\t\t\t\t\t:show-loading=\"true\"\n\t\t\t\t\t\t\t\t\tstyle=\"width: 100%; height: 400rpx; display: block;\">\n\t\t\t\t\t\t\t\t</image>\n\t\t\t\t\t\t\t\t<view class=\"image-loading\" v-if=\"!currentImageLoaded\">\n\t\t\t\t\t\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"image-label\">\n\t\t\t\t\t\t\t\t\t<text class=\"label-text\">现在的你</text>\n\t\t\t\t\t\t\t\t\t<text class=\"label-year\">2024</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"transform-arrow\">\n\t\t\t\t\t\t\t<view class=\"arrow-line\" :style=\"{background:t('color1')}\"></view>\n\t\t\t\t\t\t\t<view class=\"arrow-head\" :style=\"{borderLeftColor:t('color1')}\"></view>\n\t\t\t\t\t\t\t<text class=\"arrow-text\" :style=\"{color:t('color1')}\">AI预测</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"image-item\">\n\t\t\t\t\t\t\t<view class=\"image-frame future\" :style=\"{borderColor: predictedImageLoaded ? 'rgba(189, 0, 255, 0.8)' : t('color1'), background: 'rgba(189, 0, 255, 0.1)'}\">\n\t\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\t\t:src=\"predictedImageUrl\"\n\t\t\t\t\t\t\t\t\tclass=\"result-image\"\n\t\t\t\t\t\t\t\t\tmode=\"aspectFit\"\n\t\t\t\t\t\t\t\t\t@error=\"onPredictedImageError\"\n\t\t\t\t\t\t\t\t\t@load=\"onPredictedImageLoad\"\n\t\t\t\t\t\t\t\t\t:show-loading=\"true\"\n\t\t\t\t\t\t\t\t\tv-if=\"predictedImageUrl\"\n\t\t\t\t\t\t\t\t\tstyle=\"width: 100%; height: 400rpx; display: block;\">\n\t\t\t\t\t\t\t\t</image>\n\t\t\t\t\t\t\t\t<view class=\"image-loading\" v-if=\"!predictedImageLoaded && predictedImageUrl\">\n\t\t\t\t\t\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t\t\t\t\t\t<text class=\"loading-text\">生成中...</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"image-placeholder\" v-if=\"!predictedImageUrl\">\n\t\t\t\t\t\t\t\t\t<view class=\"placeholder-icon\">🔮</view>\n\t\t\t\t\t\t\t\t\t<text class=\"placeholder-text\">AI正在生成...</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"image-label\">\n\t\t\t\t\t\t\t\t\t<text class=\"label-text\">20年后的你</text>\n\t\t\t\t\t\t\t\t\t<text class=\"label-year\">2044</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"prediction-badge\" v-if=\"predictedImageLoaded\" :style=\"{background:'linear-gradient(45deg, rgba(189, 0, 255, 0.8), rgba(189, 0, 255, 0.6))'}\">\n\t\t\t\t\t\t\t\t\t<text class=\"badge-text\">AI预测</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 底部固定按钮 -->\n\t\t\t<view class=\"result-bottom-bar\">\n\t\t\t\t<view class=\"tech-button-container\">\n\t\t\t\t\t<!-- 科技装饰线条 -->\n\t\t\t\t\t<view class=\"tech-lines\">\n\t\t\t\t\t\t<view class=\"tech-line left\" :style=\"{background:t('color1')}\"></view>\n\t\t\t\t\t\t<view class=\"tech-line right\" :style=\"{background:t('color1')}\"></view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 主按钮 -->\n\t\t\t\t\t<button class=\"bottom-chat-btn\" @tap=\"startFutureChat\">\n\t\t\t\t\t\t<!-- 按钮背景装饰 -->\n\t\t\t\t\t\t<view class=\"btn-bg-decoration\">\n\t\t\t\t\t\t\t<view class=\"bg-grid\"></view>\n\t\t\t\t\t\t\t<view class=\"bg-particles\">\n\t\t\t\t\t\t\t\t<view class=\"particle\" v-for=\"i in 6\" :key=\"i\"></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 按钮内容 -->\n\t\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t\t\t<view class=\"btn-icon-container\">\n\t\t\t\t\t\t\t\t<text class=\"chat-btn-icon\">🚀</text>\n\t\t\t\t\t\t\t\t<view class=\"icon-glow\" :style=\"{boxShadow: '0 0 20rpx ' + t('color1')}\"></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"btn-text-container\">\n\t\t\t\t\t\t\t\t<text class=\"chat-btn-text\">与未来对话</text>\n\t\t\t\t\t\t\t\t<text class=\"chat-btn-subtitle\">FUTURE DIALOGUE</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"btn-arrow\">\n\t\t\t\t\t\t\t\t<text class=\"arrow-symbol\">→</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 扫描线效果 -->\n\t\t\t\t\t\t<view class=\"scan-line\" :style=\"{background:t('color1')}\"></view>\n\t\t\t\t\t</button>\n\n\t\t\t\t\t<!-- 底部装饰 -->\n\t\t\t\t\t<view class=\"tech-decoration\">\n\t\t\t\t\t\t<view class=\"deco-dot\" v-for=\"i in 5\" :key=\"i\" :style=\"{background:t('color1')}\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 用户信息配置模态框 - 移动端优化版 -->\n\t\t<view v-if=\"showConfig\" class=\"config-modal\">\n\t\t\t<view class=\"config-overlay\" @tap=\"hideConfigModal\"></view>\n\t\t\t<view class=\"config-container-mobile\">\n\t\t\t\t<!-- 简化头部 -->\n\t\t\t\t<view class=\"config-header-mobile\">\n\t\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t\t<view class=\"title-icon\" :style=\"{color:t('color1')}\">👤</view>\n\t\t\t\t\t\t<text class=\"config-title-mobile\">个人信息</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"config-close-mobile\" @tap=\"hideConfigModal\">\n\t\t\t\t\t\t<text class=\"close-text\">×</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 可滚动内容 -->\n\t\t\t\t<scroll-view class=\"config-scroll-mobile\" scroll-y=\"true\" :show-scrollbar=\"false\">\n\t\t\t\t\t<!-- 简化提示 -->\n\t\t\t\t\t<view v-if=\"showConfigTip\" class=\"config-tip-mobile\" :style=\"{borderColor:t('color1')}\">\n\t\t\t\t\t\t<text class=\"tip-text-mobile\">设置基本信息，获得更准确的AI预测</text>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 性别选择 - 网格布局 -->\n\t\t\t\t\t<view class=\"config-section-mobile\">\n\t\t\t\t\t\t<text class=\"section-title-mobile\" :style=\"{color:t('color1')}\">性别选择 *</text>\n\t\t\t\t\t\t<view class=\"gender-grid-mobile\">\n\t\t\t\t\t\t\t<view v-for=\"(option, index) in genderOptions\" :key=\"index\"\n\t\t\t\t\t\t\t\t  v-if=\"index > 0\"\n\t\t\t\t\t\t\t\t  class=\"gender-item-mobile\"\n\t\t\t\t\t\t\t\t  :class=\"{active: genderIndex === index}\"\n\t\t\t\t\t\t\t\t  :style=\"{\n\t\t\t\t\t\t\t\t\t  borderColor: genderIndex === index ? t('color1') : 'rgba(125, 249, 255, 0.3)',\n\t\t\t\t\t\t\t\t\t  background: genderIndex === index ? 'rgba('+t('color1rgb')+',0.2)' : 'rgba(125, 249, 255, 0.1)'\n\t\t\t\t\t\t\t\t  }\"\n\t\t\t\t\t\t\t\t  @tap=\"selectGender(index)\">\n\t\t\t\t\t\t\t\t<view class=\"gender-icon-mobile\">{{index === 1 ? '👨' : index === 2 ? '👩' : '🧑'}}</view>\n\t\t\t\t\t\t\t\t<text class=\"gender-text-mobile\" :style=\"{color: genderIndex === index ? t('color1') : 'rgba(125, 249, 255, 0.8)'}\">{{option}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 职业输入 - 紧凑布局 -->\n\t\t\t\t\t<view class=\"config-section-mobile\">\n\t\t\t\t\t\t<text class=\"section-title-mobile\" :style=\"{color:t('color1')}\">职业/梦想</text>\n\t\t\t\t\t\t<view class=\"input-wrapper-mobile\">\n\t\t\t\t\t\t\t<view class=\"input-container-mobile\" :style=\"{borderColor: userProfession ? t('color1') : 'rgba(125, 249, 255, 0.3)'}\">\n\t\t\t\t\t\t\t\t<input\n\t\t\t\t\t\t\t\t\ttype=\"text\"\n\t\t\t\t\t\t\t\t\tclass=\"profession-input-mobile\"\n\t\t\t\t\t\t\t\t\tv-model=\"userProfession\"\n\t\t\t\t\t\t\t\t\tplaceholder=\"如：程序员、医生、教师...\"\n\t\t\t\t\t\t\t\t\tplaceholder-style=\"color: rgba(125, 249, 255, 0.5); font-size: 24rpx;\"\n\t\t\t\t\t\t\t\t\tconfirm-type=\"done\"\n\t\t\t\t\t\t\t\t\tmaxlength=\"20\"\n\t\t\t\t\t\t\t\t\t@focus=\"onInputFocus\"\n\t\t\t\t\t\t\t\t\t@blur=\"onInputBlur\"\n\t\t\t\t\t\t\t\t\t@input=\"onProfessionInput\"\n\t\t\t\t\t\t\t\t\t@confirm=\"onInputConfirm\" />\n\t\t\t\t\t\t\t\t<view class=\"input-icon-mobile\" :style=\"{color: userProfession ? t('color1') : 'rgba(125, 249, 255, 0.5)'}\">💼</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"input-status-mobile\" v-if=\"userProfession\">\n\t\t\t\t\t\t\t\t<text class=\"status-text-mobile\" :style=\"{color:t('color1')}\">✓ {{userProfession}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 底部间距 -->\n\t\t\t\t\t<view class=\"bottom-spacer-mobile\"></view>\n\t\t\t\t</scroll-view>\n\n\t\t\t\t<!-- 固定底部按钮 -->\n\t\t\t\t<view class=\"config-actions-mobile\">\n\t\t\t\t\t<button class=\"action-btn-mobile secondary\" @tap=\"skipConfig\" type=\"default\">\n\t\t\t\t\t\t<text class=\"btn-text-mobile\">跳过</text>\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"action-btn-mobile primary\" @tap=\"saveConfig\" type=\"default\"\n\t\t\t\t\t\t\t:style=\"{background:'linear-gradient(45deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\"\n\t\t\t\t\t\t\t:disabled=\"genderIndex <= 0\">\n\t\t\t\t\t\t<text class=\"btn-icon-mobile\">✓</text>\n\t\t\t\t\t\t<text class=\"btn-text-mobile\">确认</text>\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tcurrentStep: 'camera', // camera, preview, processing, result\n\t\t\tcurrentStatus: '准备拍照',\n\t\t\tshowCamera: false,\n\t\t\tcameraReady: false,\n\t\t\tcameraStatusText: '正在启动摄像头...',\n\t\t\tcapturedImageUrl: '',\n\t\t\tpredictedImageUrl: '',\n\t\t\tprogressPercent: 0,\n\t\t\tcurrentProcessStep: 0,\n\t\t\tprocessingSteps: [\n\t\t\t\t{ icon: '🔍', text: '面部识别' },\n\t\t\t\t{ icon: '🧠', text: 'AI分析' },\n\t\t\t\t{ icon: '✨', text: '预测生成' }\n\t\t\t],\n\t\t\t\n\t\t\t// 配置相关\n\t\t\tshowConfig: false,\n\t\t\tshowConfigTip: false,\n\t\t\tgenderIndex: -1,\n\t\t\tgenderOptions: ['请选择性别', '男', '女', '其他'],\n\t\t\tuserProfession: '',\n\n\t\t\t// 图片加载状态\n\t\t\timageLoaded: false,\n\t\t\tcurrentImageLoaded: false,\n\t\t\tpredictedImageLoaded: false,\n\n\t\t\t// 定时器\n\t\t\tprocessingTimer: null,\n\t\t\tprogressTimer: null,\n\n\t\t\t// 音频相关\n\t\t\tpre_url: '', // 云资源域名前缀\n\t\t\tprocessingAudio: null // 处理中音频对象\n\t\t}\n\t},\n\t\n\tonLoad() {\n\t\tconsole.log('=== 摄像头页面加载开始 ===');\n\n\t\t// 初始化云资源前缀URL\n\t\tconst app = getApp();\n\t\tthis.pre_url = app.globalData.pre_url || '';\n\n\t\tsetTimeout(() => {\n\t\t\tthis.loadUserConfig();\n\t\t\tthis.initCamera();\n\t\t\tthis.checkDreamInspirationSettings();\n\t\t}, 500);\n\t},\n\t\n\tonUnload() {\n\t\tthis.clearTimers();\n\t\tthis.stopProcessingAudio();\n\t},\n\t\n\tmethods: {\n\t\t// 加载用户配置\n\t\tloadUserConfig() {\n\t\t\tconsole.log('=== 开始加载用户配置 ===');\n\n\t\t\t// 尝试加载已保存的配置\n\t\t\ttry {\n\t\t\t\tconst savedGender = uni.getStorageSync('user_gender');\n\t\t\t\tconst savedProfession = uni.getStorageSync('user_profession');\n\n\t\t\t\tif (savedGender) {\n\t\t\t\t\tconst genderIndex = this.genderOptions.indexOf(savedGender);\n\t\t\t\t\tif (genderIndex > 0) {\n\t\t\t\t\t\tthis.genderIndex = genderIndex;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (savedProfession) {\n\t\t\t\t\tthis.userProfession = savedProfession;\n\t\t\t\t}\n\n\t\t\t\tconsole.log('加载已保存配置:', {\n\t\t\t\t\tgender: savedGender,\n\t\t\t\t\tprofession: savedProfession,\n\t\t\t\t\tgenderIndex: this.genderIndex\n\t\t\t\t});\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('加载配置失败:', e);\n\t\t\t}\n\n\t\t\tthis.showConfigTip = true;\n\t\t\tthis.showConfig = true;\n\t\t\tconsole.log('显示配置弹窗');\n\t\t},\n\t\t\n\t\t// 初始化摄像头\n\t\tinitCamera() {\n\t\t\tconsole.log('=== 开始初始化摄像头 ===');\n\t\t\tthis.showCamera = true;\n\t\t\tthis.cameraReady = true;\n\t\t\tthis.cameraStatusText = '摄像头已就绪';\n\t\t\tconsole.log('摄像头状态设置完成');\n\t\t},\n\t\t\n\t\t// 摄像头就绪\n\t\tonCameraReady() {\n\t\t\tthis.cameraReady = true;\n\t\t\tthis.cameraStatusText = '摄像头已就绪';\n\t\t\tthis.currentStatus = '准备拍照';\n\t\t\tconsole.log('摄像头初始化完成');\n\t\t},\n\n\t\t// 摄像头错误\n\t\tonCameraError(e) {\n\t\t\tconsole.error('摄像头错误:', e);\n\t\t\tthis.cameraStatusText = '摄像头启动失败';\n\t\t},\n\t\t\n\t\t// 拍照\n\t\tcapturePhoto() {\n\t\t\tconsole.log('开始拍照');\n\t\t\tthis.uploadImage('camera');\n\t\t},\n\n\t\t// 选择图片\n\t\tchooseImage() {\n\t\t\tconsole.log('选择图片');\n\t\t\tthis.uploadImage('album');\n\t\t},\n\n\t\t// 统一的图片上传方法\n\t\tuploadImage(sourceType = 'album') {\n\t\t\tconst that = this;\n\t\t\tconst app = getApp();\n\n\t\t\t// 显示上传提示\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '正在上传图片...',\n\t\t\t\tmask: true\n\t\t\t});\n\n\t\t\t// 使用统一的图片上传接口\n\t\t\tapp.chooseImage(function (urls) {\n\t\t\t\tuni.hideLoading();\n\n\t\t\t\tif (urls && urls.length > 0) {\n\t\t\t\t\t// 获取上传后的图片URL\n\t\t\t\t\tthat.capturedImageUrl = urls[0];\n\t\t\t\t\tthat.currentStep = 'preview';\n\n\t\t\t\t\t// 重置图片加载状态\n\t\t\t\t\tthat.imageLoaded = false;\n\t\t\t\t\tthat.currentImageLoaded = false;\n\t\t\t\t\tthat.predictedImageLoaded = false;\n\n\t\t\t\t\tconsole.log('图片上传成功:', urls[0]);\n\t\t\t\t\tconsole.log('切换到预览模式，当前步骤:', that.currentStep);\n\t\t\t\t\tconsole.log('图片URL设置为:', that.capturedImageUrl);\n\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '图片上传成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '图片上传失败',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}, 1, sourceType === 'camera' ? ['camera'] : ['album', 'camera']);\n\t\t},\n\t\t\n\t\t// 切换摄像头\n\t\tswitchCamera() {\n\t\t\tconsole.log('切换摄像头');\n\t\t\t// 这里可以实现前后摄像头切换逻辑\n\t\t},\n\t\t\n\t\t// 重新拍照\n\t\tretakePhoto() {\n\t\t\tthis.currentStep = 'camera';\n\t\t\tthis.capturedImageUrl = '';\n\t\t\tthis.predictedImageUrl = '';\n\t\t\t// 重置图片加载状态\n\t\t\tthis.imageLoaded = false;\n\t\t\tthis.currentImageLoaded = false;\n\t\t\tthis.predictedImageLoaded = false;\n\t\t},\n\n\t\t// 图片加载事件处理\n\t\tonImageLoad() {\n\t\t\tthis.imageLoaded = true;\n\t\t\tconsole.log('预览图片加载完成');\n\t\t},\n\n\t\tonImageError(e) {\n\t\t\tconsole.error('预览图片加载失败:', e);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '图片加载失败',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\n\t\tonCurrentImageLoad() {\n\t\t\tthis.currentImageLoaded = true;\n\t\t\tconsole.log('当前图片加载完成');\n\t\t},\n\n\t\tonCurrentImageError(e) {\n\t\t\tconsole.error('当前图片加载失败:', e);\n\t\t\tthis.currentImageLoaded = false;\n\t\t},\n\n\t\tonPredictedImageLoad() {\n\t\t\tthis.predictedImageLoaded = true;\n\t\t\tconsole.log('预测图片加载完成');\n\t\t},\n\n\t\tonPredictedImageError(e) {\n\t\t\tconsole.error('预测图片加载失败:', e);\n\t\t\tthis.predictedImageLoaded = false;\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '预测图片加载失败',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 开始处理\n\t\tstartProcessing() {\n\t\t\tthis.currentStep = 'processing';\n\t\t\tthis.progressPercent = 0;\n\t\t\tthis.currentProcessStep = 0;\n\n\t\t\t// 播放处理中音频\n\t\t\tthis.playProcessingAudio();\n\n\t\t\t// 模拟AI处理过程\n\t\t\tthis.simulateAIProcessing();\n\t\t},\n\n\t\t// 播放处理中音频\n\t\tplayProcessingAudio() {\n\t\t\ttry {\n\t\t\t\t// 停止之前的音频\n\t\t\t\tthis.stopProcessingAudio();\n\n\t\t\t\t// 创建音频上下文\n\t\t\t\tthis.processingAudio = uni.createInnerAudioContext();\n\n\t\t\t\t// 设置音频源（使用云端资源）\n\t\t\t\tthis.processingAudio.src = this.pre_url + '/static/MP3/耐心等待20秒.mp3';\n\n\t\t\t\t// 音频播放事件\n\t\t\t\tthis.processingAudio.onPlay(() => {\n\t\t\t\t\tconsole.log('开始播放处理中音频');\n\t\t\t\t});\n\n\t\t\t\t// 音频播放完成事件\n\t\t\t\tthis.processingAudio.onEnded(() => {\n\t\t\t\t\tconsole.log('处理中音频播放完成');\n\t\t\t\t\tthis.processingAudio.destroy();\n\t\t\t\t\tthis.processingAudio = null;\n\t\t\t\t});\n\n\t\t\t\t// 音频播放错误事件\n\t\t\t\tthis.processingAudio.onError((res) => {\n\t\t\t\t\tconsole.error('处理中音频播放错误:', res);\n\t\t\t\t\tthis.processingAudio.destroy();\n\t\t\t\t\tthis.processingAudio = null;\n\t\t\t\t});\n\n\t\t\t\t// 开始播放\n\t\t\t\tthis.processingAudio.play();\n\n\t\t\t\t// 设置定时器，10-15秒后停止播放\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.stopProcessingAudio();\n\t\t\t\t}, 12000); // 12秒后停止\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('播放处理中音频时发生错误:', error);\n\t\t\t}\n\t\t},\n\n\t\t// 停止处理中音频\n\t\tstopProcessingAudio() {\n\t\t\tif (this.processingAudio) {\n\t\t\t\ttry {\n\t\t\t\t\tthis.processingAudio.stop();\n\t\t\t\t\tthis.processingAudio.destroy();\n\t\t\t\t\tthis.processingAudio = null;\n\t\t\t\t\tconsole.log('处理中音频已停止');\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('停止处理中音频时发生错误:', error);\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\t// 模拟AI处理过程\n\t\tsimulateAIProcessing() {\n\t\t\t// 处理步骤动画\n\t\t\tconst stepInterval = setInterval(() => {\n\t\t\t\tif (this.currentProcessStep < this.processingSteps.length - 1) {\n\t\t\t\t\tthis.currentProcessStep++;\n\t\t\t\t}\n\t\t\t}, 1500);\n\n\t\t\t// 进度条动画\n\t\t\tthis.processingTimer = setInterval(() => {\n\t\t\t\tthis.progressPercent += 1;\n\t\t\t\tif (this.progressPercent >= 100) {\n\t\t\t\t\tclearInterval(stepInterval);\n\t\t\t\t\tthis.completeProcessing();\n\t\t\t\t}\n\t\t\t}, 50);\n\t\t},\n\n\t\t// 完成处理\n\t\tcompleteProcessing() {\n\t\t\tthis.clearTimers();\n\n\t\t\t// 停止处理中音频\n\t\t\tthis.stopProcessingAudio();\n\n\t\t\t// 调用AI接口生成20年后的图片\n\t\t\tthis.generateFutureImage();\n\t\t},\n\n\t\t// 生成梦想启蒙图片\n\t\tgenerateFutureImage() {\n\t\t\t// 重置预测图片加载状态\n\t\t\tthis.predictedImageLoaded = false;\n\n\t\t\t// 检查是否已配置性别和职业\n\t\t\tconst userGender = uni.getStorageSync('user_gender');\n\t\t\tconst userProfession = uni.getStorageSync('user_profession');\n\n\t\t\tconsole.log('检查用户配置:', {\n\t\t\t\tuserGender: userGender,\n\t\t\t\tuserProfession: userProfession,\n\t\t\t\tcapturedImageUrl: this.capturedImageUrl\n\t\t\t});\n\n\t\t\tif (!userGender || !userProfession || userGender === '未设置' || userProfession === '未设置') {\n\t\t\t\tconsole.log('用户配置不完整，显示配置提醒');\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '配置提醒',\n\t\t\t\t\tcontent: '请先完成性别和职业配置，以便生成个性化的梦想图片',\n\t\t\t\t\tshowCancel: false,\n\t\t\t\t\tconfirmText: '去配置',\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tthis.showConfig = true;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 检查是否有图片URL\n\t\t\tif (!this.capturedImageUrl) {\n\t\t\t\tconsole.log('没有图片URL，无法调用API');\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先上传照片',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconsole.log('配置检查通过，开始调用梦想启蒙API');\n\t\t\t// 调用真实的梦想启蒙API\n\t\t\tthis.callAIAPI();\n\t\t},\n\n\t\t// 调用梦想启蒙AI接口\n\t\tcallAIAPI() {\n\t\t\tconsole.log('=== 开始调用梦想启蒙API ===');\n\n\t\t\t// 获取用户配置信息\n\t\t\tconst userGender = uni.getStorageSync('user_gender') || '未设置';\n\t\t\tconst userProfession = uni.getStorageSync('user_profession') || '未设置';\n\n\t\t\t// 构建梦想内容\n\t\t\tconst dreamContent = `我是一个${userGender}，职业是${userProfession}，希望在未来能够实现自己的梦想，变得更加优秀和成功。`;\n\n\t\t\t// 准备请求参数\n\t\t\tconst requestData = {\n\t\t\t\tgender: userGender,\n\t\t\t\tdream_content: dreamContent,\n\t\t\t\timage_url: this.capturedImageUrl // 通过统一上传接口获取的图片URL\n\t\t\t};\n\n\t\t\tconsole.log('API请求参数:', requestData);\n\t\t\tconsole.log('即将调用接口: ApiDreamInspiration/generateImage');\n\n\t\t\t// 获取全局app对象\n\t\t\tconst app = getApp();\n\n\t\t\t// 调用梦想启蒙API\n\t\t\tapp.post('ApiDreamInspiration/generateImage', requestData, (res) => {\n\t\t\t\tconsole.log('API调用成功，返回结果:', res);\n\t\t\t\tif (res.code == 1) {\n\t\t\t\t\t// 生成成功，获取记录ID\n\t\t\t\t\tconst recordId = res.data.record_id;\n\n\t\t\t\t\t// 显示生成中状态\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '正在生成梦想图片...',\n\t\t\t\t\t\tmask: true\n\t\t\t\t\t});\n\n\t\t\t\t\t// 轮询检查生成状态\n\t\t\t\t\tthis.checkGenerationStatus(recordId);\n\t\t\t\t} else {\n\t\t\t\t\tthis.handleAPIError(res.msg || '生成请求失败');\n\t\t\t\t}\n\t\t\t}, (error) => {\n\t\t\t\tconsole.error('API调用失败:', error);\n\t\t\t\tthis.handleAPIError('网络请求失败，请检查网络连接');\n\t\t\t});\n\t\t},\n\n\t\t// 检查图片生成状态\n\t\tcheckGenerationStatus(recordId) {\n\t\t\tconst app = getApp();\n\t\t\tlet pollCount = 0;\n\t\t\tconst maxPolls = 60; // 最多轮询60次（约2分钟）\n\n\t\t\tconst checkStatus = () => {\n\t\t\t\tpollCount++;\n\t\t\t\tconsole.log(`轮询查询状态 - 第${pollCount}次，记录ID: ${recordId}`);\n\n\t\t\t\tapp.post('ApiDreamInspiration/checkGenerationStatus', {\n\t\t\t\t\trecord_id: recordId\n\t\t\t\t}, (res) => {\n\t\t\t\t\tconsole.log('状态查询结果:', res);\n\n\t\t\t\t\tif (res.code == 1) {\n\t\t\t\t\t\tconst record = res.data;\n\n\t\t\t\t\t\tif (record.status == 1) {\n\t\t\t\t\t\t\t// 生成成功 (状态1表示已生成)\n\t\t\t\t\t\t\tuni.hideLoading();\n\n\t\t\t\t\t\t\t// 调试信息：打印获取到的图片URL\n\t\t\t\t\t\t\tconsole.log('梦想图片生成成功，图片URL:', record.result_image);\n\n\t\t\t\t\t\t\tthis.predictedImageUrl = record.result_image;\n\t\t\t\t\t\t\tthis.currentStep = 'result';\n\n\t\t\t\t\t\t\t// 重置图片加载状态，确保图片能正常显示\n\t\t\t\t\t\t\tthis.predictedImageLoaded = false;\n\n\t\t\t\t\t\t\t// 强制触发页面更新\n\t\t\t\t\t\t\tthis.$forceUpdate();\n\n\t\t\t\t\t\t\t// 保存记录ID用于后续查看\n\t\t\t\t\t\t\tuni.setStorageSync('current_dream_record_id', recordId);\n\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '梦想图片生成完成！',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\t// 调试信息：确认状态设置\n\t\t\t\t\t\t\tconsole.log('页面切换到结果展示，预测图片URL:', this.predictedImageUrl);\n\t\t\t\t\t\t} else if (record.status == 2) {\n\t\t\t\t\t\t\t// 生成失败 (状态2表示生成失败)\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tthis.handleAPIError(record.error_msg || '图片生成失败');\n\t\t\t\t\t\t} else if (record.status == 0) {\n\t\t\t\t\t\t\t// 仍在生成中 (状态0表示生成中)\n\t\t\t\t\t\t\tif (pollCount >= maxPolls) {\n\t\t\t\t\t\t\t\t// 超过最大轮询次数\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tthis.handleAPIError('生成超时，请稍后查看记录');\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// 继续轮询，每3秒查询一次\n\t\t\t\t\t\t\t\tsetTimeout(checkStatus, 3000);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 未知状态\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tthis.handleAPIError('未知的生成状态');\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tthis.handleAPIError(res.msg || '获取生成状态失败');\n\t\t\t\t\t}\n\t\t\t\t}, (error) => {\n\t\t\t\t\tconsole.error('状态查询失败:', error);\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthis.handleAPIError('检查生成状态失败');\n\t\t\t\t});\n\t\t\t};\n\n\t\t\t// 开始检查状态\n\t\t\tcheckStatus();\n\t\t},\n\n\t\t// 处理API错误\n\t\thandleAPIError(message) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '预测失败',\n\t\t\t\tcontent: message + '，是否重试？',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tthis.startProcessing();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.currentStep = 'preview';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 与未来对话\n\t\tstartFutureChat() {\n\t\t\tconsole.log('开始与未来对话');\n\n\t\t\t// 显示加载状态\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '正在获取对话链接...',\n\t\t\t\tmask: true\n\t\t\t});\n\n\t\t\t// 获取全局app对象\n\t\t\tconst app = getApp();\n\n\t\t\t// 调用API获取与未来对话的链接\n\t\t\tapp.post('ApiDreamInspiration/getFutureTalkUrl', {}, (res) => {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tconsole.log('获取与未来对话链接结果:', res);\n\n\t\t\t\tif (res.code == 1) {\n\t\t\t\t\tconst data = res.data;\n\t\t\t\t\tconst futureTalkUrl = data.url;\n\n\t\t\t\t\tif (futureTalkUrl) {\n\t\t\t\t\t\tconsole.log('准备跳转到链接:', futureTalkUrl);\n\n\t\t\t\t\t\t// 判断是否为外部链接\n\t\t\t\t\t\tif (futureTalkUrl.startsWith('http://') || futureTalkUrl.startsWith('https://')) {\n\t\t\t\t\t\t\t// 外部链接，使用webview打开\n\t\t\t\t\t\t\t// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ\n\t\t\t\t\t\t\tconst webviewUrl = '/pages/index/webView3?url=' + encodeURIComponent(futureTalkUrl);\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: webviewUrl,\n\t\t\t\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\t\t\t\tconsole.log('跳转到WebView成功');\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\t\t\t\tconsole.error('跳转到WebView失败', err);\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '跳转失败，请稍后再试',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t// #endif\n\n\t\t\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\t\t\tplus.runtime.openURL(futureTalkUrl);\n\t\t\t\t\t\t\t// #endif\n\n\t\t\t\t\t\t\t// #ifdef H5\n\t\t\t\t\t\t\twindow.open(futureTalkUrl, '_blank');\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 内部页面链接\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: futureTalkUrl,\n\t\t\t\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\t\t\t\tconsole.log('跳转到内部页面成功');\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\t\t\t\tconsole.error('跳转到内部页面失败', err);\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '跳转失败，请稍后再试',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '未配置对话链接',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg || '获取对话链接失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}, (error) => {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tconsole.error('获取与未来对话链接失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络请求失败，请检查网络连接',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\n\t\t// 获取我的梦想记录\n\t\tgetMyDreamRecords() {\n\t\t\tconst app = getApp();\n\t\t\tapp.post('ApiDreamInspiration/getMyRecords', {\n\t\t\t\tpage: 1,\n\t\t\t\tlimit: 10\n\t\t\t}, (res) => {\n\t\t\t\tif (res.code == 1) {\n\t\t\t\t\tconsole.log('我的梦想记录:', res.data);\n\t\t\t\t\t// 可以在这里处理历史记录显示\n\t\t\t\t} else {\n\t\t\t\t\tconsole.error('获取记录失败:', res.msg);\n\t\t\t\t}\n\t\t\t}, (error) => {\n\t\t\t\tconsole.error('获取记录失败:', error);\n\t\t\t});\n\t\t},\n\n\t\t// 查看历史记录\n\t\tviewHistory() {\n\t\t\t// 可以跳转到历史记录页面或显示历史记录弹窗\n\t\t\tthis.getMyDreamRecords();\n\t\t},\n\n\t\t// 检查梦想启蒙设置\n\t\tcheckDreamInspirationSettings() {\n\t\t\tconst app = getApp();\n\t\t\tapp.post('ApiDreamInspiration/getSetting', {}, (res) => {\n\t\t\t\tif (res.code == 1) {\n\t\t\t\t\tconst settings = res.data;\n\t\t\t\t\tif (!settings.is_enabled) {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '功能未开启',\n\t\t\t\t\t\t\tcontent: '梦想启蒙功能暂未开启，请联系管理员',\n\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\tconfirmText: '知道了',\n\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tconsole.error('获取设置失败:', res.msg);\n\t\t\t\t}\n\t\t\t}, (error) => {\n\t\t\t\tconsole.error('检查设置失败:', error);\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 返回\n\t\tgoBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\t\n\t\t// 配置相关方法\n\t\tselectGender(index) {\n\t\t\tif (index === 0) return;\n\t\t\tthis.genderIndex = index;\n\t\t},\n\t\t\n\t\tonInputFocus() {\n\t\t\tconsole.log('职业输入框获得焦点');\n\t\t},\n\n\t\tonInputBlur() {\n\t\t\tconsole.log('职业输入框失去焦点');\n\t\t},\n\n\t\tonProfessionInput(e) {\n\t\t\tthis.userProfession = e.detail.value;\n\t\t\tconsole.log('职业输入内容:', this.userProfession);\n\t\t},\n\n\t\tonInputConfirm(e) {\n\t\t\tthis.userProfession = e.detail.value;\n\t\t\tconsole.log('职业输入确认:', this.userProfession);\n\t\t\t// 可以在这里添加输入验证逻辑\n\t\t},\n\t\t\n\t\tsaveConfig() {\n\t\t\t// 验证性别选择\n\t\t\tif (this.genderIndex <= 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择性别',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 验证职业输入（可选，但建议填写）\n\t\t\tif (!this.userProfession || this.userProfession.trim() === '') {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '建议填写职业信息以获得更准确的预测结果，是否继续？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthis.doSaveConfig();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.doSaveConfig();\n\t\t},\n\n\t\tdoSaveConfig() {\n\t\t\ttry {\n\t\t\t\tconst profession = this.userProfession.trim();\n\t\t\t\tuni.setStorageSync('user_gender', this.genderOptions[this.genderIndex]);\n\t\t\t\tuni.setStorageSync('user_profession', profession);\n\n\t\t\t\tconsole.log('保存配置:', {\n\t\t\t\t\tgender: this.genderOptions[this.genderIndex],\n\t\t\t\t\tprofession: profession\n\t\t\t\t});\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '配置已保存',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\n\t\t\t\tthis.hideConfigModal();\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('保存配置失败:', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '保存失败，请重试',\n\t\t\t\t\ticon: 'error'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 跳过配置\n\t\tskipConfig() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '跳过配置',\n\t\t\t\tcontent: '跳过配置可能影响AI预测准确性，确定要跳过吗？',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t// 设置默认值\n\t\t\t\t\t\tuni.setStorageSync('user_gender', '未设置');\n\t\t\t\t\t\tuni.setStorageSync('user_profession', '未设置');\n\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '已跳过配置',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tthis.hideConfigModal();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\thideConfigModal() {\n\t\t\tif (this.showConfigTip && (this.genderIndex <= 0)) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先选择性别信息',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.showConfig = false;\n\t\t\tthis.showConfigTip = false;\n\t\t},\n\t\t\n\t\t// 强制显示配置\n\t\tforceShowConfig() {\n\t\t\tthis.showConfig = true;\n\t\t\tthis.showConfigTip = true;\n\t\t},\n\t\t\n\t\t// 清除定时器\n\t\tclearTimers() {\n\t\t\tif (this.processingTimer) {\n\t\t\t\tclearInterval(this.processingTimer);\n\t\t\t\tthis.processingTimer = null;\n\t\t\t}\n\t\t\tif (this.progressTimer) {\n\t\t\t\tclearInterval(this.progressTimer);\n\t\t\t\tthis.progressTimer = null;\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n/* 基础容器 */\n.camera-container {\n\twidth: 100vw;\n\theight: 100vh;\n\tposition: relative;\n\toverflow: hidden;\n\tbackground: linear-gradient(135deg, #0a0a2a 0%, #1a1a3a 50%, #2a2a4a 100%);\n\tfont-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n/* 背景装饰 */\n.bg-decoration {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tz-index: 0;\n\tpointer-events: none;\n}\n\n.bg-grid {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tbackground-image:\n\t\tlinear-gradient(rgba(125, 249, 255, 0.1) 1px, transparent 1px),\n\t\tlinear-gradient(90deg, rgba(125, 249, 255, 0.1) 1px, transparent 1px);\n\tbackground-size: 50rpx 50rpx;\n\tanimation: gridMove 20s linear infinite;\n}\n\n@keyframes gridMove {\n\t0% { transform: translate(0, 0); }\n\t100% { transform: translate(50rpx, 50rpx); }\n}\n\n.bg-particles {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n}\n\n.particle {\n\tposition: absolute;\n\twidth: 4rpx;\n\theight: 4rpx;\n\tbackground: rgba(125, 249, 255, 0.6);\n\tborder-radius: 50%;\n\tanimation: particleFloat 8s infinite linear;\n}\n\n.particle:nth-child(odd) {\n\tanimation-delay: -2s;\n}\n\n.particle:nth-child(3n) {\n\tanimation-delay: -4s;\n}\n\n@keyframes particleFloat {\n\t0% {\n\t\ttransform: translateY(100vh) scale(0);\n\t\topacity: 0;\n\t}\n\t10% {\n\t\topacity: 1;\n\t}\n\t90% {\n\t\topacity: 1;\n\t}\n\t100% {\n\t\ttransform: translateY(-100rpx) scale(1);\n\t\topacity: 0;\n\t}\n}\n\n/* 模式容器 */\n.camera-mode,\n.preview-mode,\n.processing-mode,\n.result-mode {\n\twidth: 100%;\n\theight: 100%;\n\tposition: relative;\n\tz-index: 1;\n}\n\n/* 相机预览区域 */\n.camera-preview {\n\twidth: 100%;\n\theight: 100%;\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tz-index: 1;\n\tborder-radius: 0;\n}\n\n/* H5摄像头占位符 */\n.h5-camera-placeholder {\n\tbackground: linear-gradient(135deg, rgba(26, 26, 58, 0.8) 0%, rgba(42, 42, 74, 0.8) 100%);\n\tbackdrop-filter: blur(20rpx);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.h5-camera-content {\n\ttext-align: center;\n\tpadding: 60rpx;\n}\n\n.camera-icon {\n\tfont-size: 120rpx;\n\tmargin-bottom: 30rpx;\n\tdisplay: block;\n\topacity: 0.8;\n}\n\n.h5-camera-text {\n\tfont-size: 36rpx;\n\tcolor: #7df9ff;\n\tfont-weight: bold;\n\tdisplay: block;\n\tmargin-bottom: 20rpx;\n}\n\n.h5-camera-tip {\n\tfont-size: 26rpx;\n\tcolor: rgba(125, 249, 255, 0.7);\n\tdisplay: block;\n\tline-height: 1.4;\n}\n\n/* 摄像头占位符 */\n.camera-placeholder {\n\twidth: 100%;\n\theight: 100%;\n\tbackground: rgba(0, 0, 0, 0.8);\n\tbackdrop-filter: blur(20rpx);\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tgap: 30rpx;\n}\n\n.placeholder-icon {\n\tfont-size: 80rpx;\n\topacity: 0.8;\n\tmargin-bottom: 20rpx;\n}\n\n.placeholder-text {\n\tfont-size: 36rpx;\n\tcolor: #7df9ff;\n\tfont-weight: bold;\n}\n\n.placeholder-desc {\n\tfont-size: 26rpx;\n\tcolor: rgba(125, 249, 255, 0.7);\n\ttext-align: center;\n\tline-height: 1.4;\n}\n\n/* 顶部状态栏 */\n.status-bar {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\theight: 140rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 50rpx 40rpx 20rpx;\n\tbackground: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 70%, transparent 100%);\n\tbackdrop-filter: blur(20rpx);\n\tz-index: 999;\n}\n\n.status-left,\n.status-right {\n\twidth: 80rpx;\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.status-center {\n\tflex: 1;\n\ttext-align: center;\n}\n\n.back-btn,\n.settings-btn {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tbackground: rgba(125, 249, 255, 0.1);\n\tborder: 2rpx solid rgba(125, 249, 255, 0.3);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\ttransition: all 0.3s ease;\n\tbackdrop-filter: blur(10rpx);\n}\n\n.back-btn:active,\n.settings-btn:active {\n\ttransform: scale(0.9);\n\tbackground: rgba(125, 249, 255, 0.2);\n\tborder-color: rgba(125, 249, 255, 0.5);\n}\n\n.back-icon,\n.settings-icon {\n\tfont-size: 32rpx;\n\tcolor: #7df9ff;\n\tfont-weight: bold;\n}\n\n.page-title {\n\tfont-size: 36rpx;\n\tcolor: #fff;\n\tfont-weight: bold;\n\tmargin-bottom: 5rpx;\n\tdisplay: block;\n}\n\n.page-subtitle {\n\tfont-size: 22rpx;\n\tcolor: rgba(125, 249, 255, 0.7);\n\tfont-weight: normal;\n\tletter-spacing: 2rpx;\n\tdisplay: block;\n}\n\n/* 拍摄指导区域 */\n.guide-overlay {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\tz-index: 100;\n}\n\n.guide-frame {\n\twidth: 500rpx;\n\theight: 500rpx;\n\tposition: relative;\n}\n\n.frame-border {\n\twidth: 100%;\n\theight: 100%;\n\tposition: relative;\n}\n\n.frame-corner {\n\tposition: absolute;\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder: 4rpx solid #00f7ff;\n\tborder-radius: 8rpx;\n\tanimation: cornerGlow 2s infinite alternate;\n}\n\n@keyframes cornerGlow {\n\t0% {\n\t\tborder-color: #00f7ff;\n\t\tbox-shadow: 0 0 10rpx rgba(0, 247, 255, 0.3);\n\t}\n\t100% {\n\t\tborder-color: rgba(0, 247, 255, 0.8);\n\t\tbox-shadow: 0 0 20rpx rgba(0, 247, 255, 0.6);\n\t}\n}\n\n.corner-tl {\n\ttop: 0;\n\tleft: 0;\n\tborder-right: none;\n\tborder-bottom: none;\n}\n\n.corner-tr {\n\ttop: 0;\n\tright: 0;\n\tborder-left: none;\n\tborder-bottom: none;\n}\n\n.corner-bl {\n\tbottom: 0;\n\tleft: 0;\n\tborder-right: none;\n\tborder-top: none;\n}\n\n.corner-br {\n\tbottom: 0;\n\tright: 0;\n\tborder-left: none;\n\tborder-top: none;\n}\n\n.scan-line {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 4rpx;\n\tbackground: linear-gradient(90deg, transparent 0%, #00f7ff 50%, transparent 100%);\n\tanimation: scanMove 3s infinite;\n}\n\n@keyframes scanMove {\n\t0% { transform: translateY(0); opacity: 1; }\n\t50% { opacity: 0.5; }\n\t100% { transform: translateY(500rpx); opacity: 1; }\n}\n\n.frame-center {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\ttext-align: center;\n}\n\n.guide-icon {\n\tfont-size: 60rpx;\n\tmargin-bottom: 20rpx;\n\tdisplay: block;\n\topacity: 0.8;\n}\n\n.guide-text {\n\tfont-size: 28rpx;\n\tcolor: #00f7ff;\n\tfont-weight: bold;\n\tdisplay: block;\n\tmargin-bottom: 30rpx;\n\ttext-shadow: 0 0 10rpx rgba(0, 247, 255, 0.5);\n}\n\n.pulse-indicator {\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.pulse-dot {\n\twidth: 20rpx;\n\theight: 20rpx;\n\tbackground: #00f7ff;\n\tborder-radius: 50%;\n\tanimation: pulse 2s infinite;\n\tbox-shadow: 0 0 20rpx rgba(0, 247, 255, 0.8);\n}\n\n@keyframes pulse {\n\t0%, 100% {\n\t\topacity: 1;\n\t\ttransform: scale(1);\n\t\tbox-shadow: 0 0 20rpx rgba(0, 247, 255, 0.8);\n\t}\n\t50% {\n\t\topacity: 0.5;\n\t\ttransform: scale(1.5);\n\t\tbox-shadow: 0 0 30rpx rgba(0, 247, 255, 1);\n\t}\n}\n\n/* 底部控制区域 */\n.bottom-controls {\n\tposition: absolute;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\theight: 240rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 40rpx;\n\tbackground: linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 70%, transparent 100%);\n\tbackdrop-filter: blur(20rpx);\n\tz-index: 999;\n}\n\n.controls-container {\n\twidth: 100%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmax-width: 600rpx;\n}\n\n.control-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 15rpx;\n}\n\n.control-icon {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tbackground: rgba(125, 249, 255, 0.1);\n\tborder: 2rpx solid rgba(125, 249, 255, 0.3);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\ttransition: all 0.3s ease;\n\tbackdrop-filter: blur(10rpx);\n}\n\n.control-item:active .control-icon {\n\ttransform: scale(0.9);\n\tbackground: rgba(125, 249, 255, 0.2);\n\tborder-color: rgba(125, 249, 255, 0.5);\n}\n\n.icon-text {\n\tfont-size: 32rpx;\n}\n\n.control-text {\n\tfont-size: 24rpx;\n\tcolor: rgba(125, 249, 255, 0.8);\n\tfont-weight: 500;\n}\n\n/* 拍照区域 */\n.capture-section {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 15rpx;\n}\n\n.capture-button {\n\twidth: 140rpx;\n\theight: 140rpx;\n\tbackground: rgba(125, 249, 255, 0.1);\n\tborder: 3rpx solid rgba(125, 249, 255, 0.3);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\ttransition: all 0.3s ease;\n\tbackdrop-filter: blur(10rpx);\n\tposition: relative;\n}\n\n.capture-button:active {\n\ttransform: scale(0.95);\n}\n\n.capture-button.disabled {\n\topacity: 0.5;\n}\n\n.capture-ring {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder: 2rpx solid rgba(125, 249, 255, 0.5);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tanimation: ringRotate 3s linear infinite;\n}\n\n@keyframes ringRotate {\n\t0% { transform: rotate(0deg); }\n\t100% { transform: rotate(360deg); }\n}\n\n.capture-inner {\n\twidth: 100rpx;\n\theight: 100rpx;\n\tbackground: rgba(125, 249, 255, 0.8);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 0 20rpx rgba(125, 249, 255, 0.5);\n}\n\n.capture-dot {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tbackground: #fff;\n\tborder-radius: 50%;\n\tbox-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);\n}\n\n.capture-text {\n\tfont-size: 24rpx;\n\tcolor: rgba(125, 249, 255, 0.8);\n\tfont-weight: 500;\n}\n\n/* 摄像头状态提示 */\n.camera-status-tip {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\tbackground: rgba(0, 0, 0, 0.8);\n\tbackdrop-filter: blur(20rpx);\n\tpadding: 30rpx 50rpx;\n\tborder-radius: 50rpx;\n\tborder: 2rpx solid rgba(125, 249, 255, 0.3);\n\tz-index: 100;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 20rpx;\n}\n\n.status-icon {\n\tfont-size: 32rpx;\n\tanimation: statusPulse 1.5s infinite;\n}\n\n@keyframes statusPulse {\n\t0%, 100% { opacity: 1; }\n\t50% { opacity: 0.5; }\n}\n\n.status-tip-text {\n\tfont-size: 28rpx;\n\tcolor: #7df9ff;\n\ttext-align: center;\n\tfont-weight: 500;\n}\n\n/* 预览模式和结果模式头部 */\n.preview-header,\n.result-header {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\theight: 140rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 50rpx 40rpx 20rpx;\n\tbackground: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 70%, transparent 100%);\n\tbackdrop-filter: blur(20rpx);\n\tz-index: 999;\n}\n\n.header-left,\n.header-right {\n\twidth: 120rpx;\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.header-center {\n\tflex: 1;\n\ttext-align: center;\n}\n\n.confirm-btn,\n.share-btn {\n\tbackground: linear-gradient(45deg, #00f7ff, rgba(0, 247, 255, 0.8));\n\tcolor: #fff;\n\tpadding: 20rpx 35rpx;\n\tborder-radius: 50rpx;\n\tfont-size: 26rpx;\n\tfont-weight: bold;\n\tborder: none;\n\tbox-shadow: 0 4rpx 15rpx rgba(0, 247, 255, 0.3);\n\ttransition: all 0.3s ease;\n}\n\n.confirm-btn:active,\n.share-btn:active {\n\ttransform: scale(0.95);\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 247, 255, 0.5);\n}\n\n.confirm-text,\n.share-text {\n\tcolor: #fff;\n\tfont-weight: bold;\n}\n\n/* 预览内容 */\n.preview-content {\n\twidth: 100%;\n\theight: 100%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 140rpx 40rpx 40rpx;\n}\n\n.preview-container {\n\twidth: 100%;\n\tmax-width: 600rpx;\n\ttext-align: center;\n}\n\n.image-frame {\n\tposition: relative;\n\tborder: 3rpx solid rgba(125, 249, 255, 0.5);\n\tborder-radius: 30rpx;\n\toverflow: hidden;\n\tmargin-bottom: 40rpx;\n\tbox-shadow: 0 10rpx 30rpx rgba(0, 247, 255, 0.2);\n\tmin-height: 600rpx;\n}\n\n.preview-image {\n\twidth: 100%;\n\theight: 600rpx;\n\tdisplay: block;\n}\n\n.image-overlay {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tpointer-events: none;\n}\n\n.scan-effect {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 4rpx;\n\tbackground: linear-gradient(90deg, transparent 0%, #00f7ff 50%, transparent 100%);\n\tanimation: scanPreview 2s infinite;\n}\n\n@keyframes scanPreview {\n\t0% { transform: translateY(0); opacity: 1; }\n\t100% { transform: translateY(500rpx); opacity: 0; }\n}\n\n.preview-info {\n\ttext-align: center;\n}\n\n.info-title {\n\tfont-size: 32rpx;\n\tcolor: #7df9ff;\n\tfont-weight: bold;\n\tmargin-bottom: 15rpx;\n\tdisplay: block;\n}\n\n.info-desc {\n\tfont-size: 26rpx;\n\tcolor: rgba(125, 249, 255, 0.7);\n\tdisplay: block;\n\tmargin-bottom: 20rpx;\n}\n\n.info-detail {\n\tfont-size: 22rpx;\n\tcolor: rgba(125, 249, 255, 0.6);\n\tdisplay: block;\n}\n\n/* 图片加载状态 */\n.image-loading {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground: rgba(0, 0, 0, 0.3);\n\tbackdrop-filter: blur(5rpx);\n}\n\n.loading-spinner {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tborder: 4rpx solid rgba(125, 249, 255, 0.3);\n\tborder-top: 4rpx solid #7df9ff;\n\tborder-radius: 50%;\n\tanimation: spin 1s linear infinite;\n\tmargin-bottom: 20rpx;\n}\n\n@keyframes spin {\n\t0% { transform: rotate(0deg); }\n\t100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n\tfont-size: 24rpx;\n\tcolor: #7df9ff;\n\ttext-align: center;\n}\n\n/* 图片占位符 */\n.image-placeholder {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground: rgba(0, 0, 0, 0.5);\n\tbackdrop-filter: blur(10rpx);\n}\n\n.placeholder-icon {\n\tfont-size: 80rpx;\n\tmargin-bottom: 20rpx;\n\topacity: 0.8;\n\tanimation: float 3s ease-in-out infinite;\n}\n\n@keyframes float {\n\t0%, 100% { transform: translateY(0); }\n\t50% { transform: translateY(-10rpx); }\n}\n\n.placeholder-text {\n\tfont-size: 26rpx;\n\tcolor: rgba(125, 249, 255, 0.8);\n\ttext-align: center;\n}\n\n/* 处理中模式 */\n.processing-content {\n\twidth: 100%;\n\theight: 100%;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 60rpx 40rpx;\n}\n\n.ai-analysis {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tmargin-bottom: 80rpx;\n}\n\n.ai-brain {\n\twidth: 200rpx;\n\theight: 200rpx;\n\tposition: relative;\n\tmargin-bottom: 60rpx;\n}\n\n.brain-core {\n\twidth: 100%;\n\theight: 100%;\n\tbackground: radial-gradient(circle, #00f7ff, rgba(0, 247, 255, 0.3));\n\tborder-radius: 50%;\n\tanimation: brainPulse 2s infinite;\n\tbox-shadow: 0 0 40rpx rgba(0, 247, 255, 0.5);\n}\n\n.neural-waves {\n\tposition: absolute;\n\ttop: -20rpx;\n\tleft: -20rpx;\n\tright: -20rpx;\n\tbottom: -20rpx;\n\tborder: 2rpx solid rgba(125, 249, 255, 0.6);\n\tborder-radius: 50%;\n\tanimation: waveExpand 3s infinite;\n}\n\n.wave-2 {\n\ttop: -40rpx;\n\tleft: -40rpx;\n\tright: -40rpx;\n\tbottom: -40rpx;\n\tanimation-delay: -1s;\n}\n\n.wave-3 {\n\ttop: -60rpx;\n\tleft: -60rpx;\n\tright: -60rpx;\n\tbottom: -60rpx;\n\tanimation-delay: -2s;\n}\n\n@keyframes brainPulse {\n\t0%, 100% {\n\t\ttransform: scale(1);\n\t\topacity: 1;\n\t\tbox-shadow: 0 0 40rpx rgba(0, 247, 255, 0.5);\n\t}\n\t50% {\n\t\ttransform: scale(1.1);\n\t\topacity: 0.8;\n\t\tbox-shadow: 0 0 60rpx rgba(0, 247, 255, 0.8);\n\t}\n}\n\n@keyframes waveExpand {\n\t0% {\n\t\ttransform: scale(1);\n\t\topacity: 1;\n\t}\n\t100% {\n\t\ttransform: scale(1.5);\n\t\topacity: 0;\n\t}\n}\n\n.processing-steps {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tgap: 40rpx;\n\tmargin-bottom: 40rpx;\n\tflex-wrap: nowrap;\n\twidth: 100%;\n\tmax-width: 600rpx;\n}\n\n.step-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 15rpx;\n\tflex: 1;\n\tmin-width: 120rpx;\n\tmax-width: 150rpx;\n}\n\n.step-icon {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tborder: 2rpx solid rgba(125, 249, 255, 0.3);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\ttransition: all 0.3s ease;\n}\n\n.step-item.active .step-icon {\n\tborder-color: #00f7ff;\n\tbackground: rgba(0, 247, 255, 0.2);\n\tbox-shadow: 0 0 20rpx rgba(0, 247, 255, 0.5);\n}\n\n.step-text {\n\tfont-size: 24rpx;\n}\n\n.step-label {\n\tfont-size: 22rpx;\n\ttext-align: center;\n\ttransition: color 0.3s ease;\n\tcolor: #ffffff;\n\ttext-shadow: 0 0 10rpx rgba(255, 255, 255, 0.8);\n\tfont-weight: 500;\n}\n\n.step-item.active .step-label {\n\tcolor: #00f7ff;\n\ttext-shadow: 0 0 15rpx rgba(0, 247, 255, 0.8);\n\tfont-weight: bold;\n}\n\n.processing-info {\n\ttext-align: center;\n\twidth: 100%;\n}\n\n.processing-title {\n\tfont-size: 36rpx;\n\tcolor: #00f7ff;\n\tfont-weight: bold;\n\tmargin-bottom: 20rpx;\n\ttext-shadow: 0 0 20rpx rgba(0, 247, 255, 0.5);\n}\n\n.processing-desc {\n\tfont-size: 28rpx;\n\tcolor: rgba(125, 249, 255, 0.8);\n\tmargin-bottom: 50rpx;\n}\n\n.progress-container {\n\twidth: 100%;\n\tmax-width: 500rpx;\n\tmargin: 0 auto;\n}\n\n.progress-bar {\n\twidth: 100%;\n\theight: 12rpx;\n\tbackground: rgba(125, 249, 255, 0.2);\n\tborder-radius: 6rpx;\n\toverflow: hidden;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: inset 0 0 10rpx rgba(0, 0, 0, 0.3);\n}\n\n.progress-fill {\n\theight: 100%;\n\tbackground: linear-gradient(90deg, #00f7ff, rgba(0, 247, 255, 0.8));\n\tborder-radius: 6rpx;\n\ttransition: width 0.3s ease;\n\tbox-shadow: 0 0 20rpx rgba(0, 247, 255, 0.5);\n}\n\n.progress-text {\n\tfont-size: 24rpx;\n\tcolor: #00f7ff;\n\tfont-weight: bold;\n\ttext-align: center;\n}\n\n/* 结果模式内容 */\n.result-content {\n\twidth: 100%;\n\theight: 100%;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 140rpx 40rpx 200rpx;\n}\n\n.result-comparison {\n\twidth: 100%;\n\tmargin-bottom: 60rpx;\n}\n\n.comparison-container {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tgap: 20rpx;\n\tflex-wrap: nowrap;\n\twidth: 100%;\n\tmax-width: 700rpx;\n\tmargin: 0 auto;\n}\n\n.image-item {\n\ttext-align: center;\n\tflex: 1;\n\tmin-width: 250rpx;\n\tmax-width: 300rpx;\n}\n\n.image-frame {\n\tposition: relative;\n\tborder: 3rpx solid rgba(125, 249, 255, 0.5);\n\tborder-radius: 30rpx;\n\toverflow: hidden;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 10rpx 30rpx rgba(0, 247, 255, 0.2);\n\ttransition: all 0.3s ease;\n\tmin-height: 400rpx;\n}\n\n.image-frame.current {\n\tborder-color: rgba(125, 249, 255, 0.8);\n\tbox-shadow: 0 15rpx 40rpx rgba(0, 247, 255, 0.3);\n}\n\n.image-frame.future {\n\tborder-color: rgba(189, 0, 255, 0.8);\n\tbox-shadow: 0 15rpx 40rpx rgba(189, 0, 255, 0.3);\n}\n\n.result-image {\n\twidth: 100%;\n\theight: 400rpx;\n\tdisplay: block;\n}\n\n.image-label {\n\tbackground: rgba(0, 0, 0, 0.8);\n\tbackdrop-filter: blur(10rpx);\n\tpadding: 15rpx 25rpx;\n\tborder-radius: 25rpx;\n\tmargin: 0 20rpx 20rpx;\n}\n\n.label-text {\n\tfont-size: 26rpx;\n\tcolor: #7df9ff;\n\tfont-weight: bold;\n\tdisplay: block;\n\tmargin-bottom: 5rpx;\n}\n\n.label-year {\n\tfont-size: 22rpx;\n\tcolor: rgba(125, 249, 255, 0.7);\n\tdisplay: block;\n}\n\n.transform-arrow {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 10rpx;\n\tmargin: 0 20rpx;\n\tposition: relative;\n}\n\n.arrow-line {\n\twidth: 60rpx;\n\theight: 4rpx;\n\tbackground: linear-gradient(90deg, #00f7ff, #bd00ff);\n\tborder-radius: 2rpx;\n}\n\n.arrow-head {\n\twidth: 0;\n\theight: 0;\n\tborder-left: 15rpx solid #bd00ff;\n\tborder-top: 10rpx solid transparent;\n\tborder-bottom: 10rpx solid transparent;\n}\n\n.arrow-text {\n\tfont-size: 22rpx;\n\tcolor: #ffffff;\n\tfont-weight: bold;\n\tletter-spacing: 1rpx;\n\ttext-shadow: 0 0 15rpx rgba(0, 247, 255, 0.8);\n\tbackground: rgba(0, 0, 0, 0.6);\n\tpadding: 8rpx 15rpx;\n\tborder-radius: 20rpx;\n\tbackdrop-filter: blur(10rpx);\n\tborder: 1rpx solid rgba(0, 247, 255, 0.3);\n}\n\n/* 底部固定按钮区域 */\n.result-bottom-bar {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tpadding: 30rpx 40rpx;\n\tpadding-bottom: calc(30rpx + env(safe-area-inset-bottom));\n\tbackground: linear-gradient(180deg, rgba(10, 10, 42, 0.8) 0%, rgba(5, 5, 25, 0.95) 100%);\n\tbackdrop-filter: blur(25rpx);\n\tborder-top: 2rpx solid rgba(125, 249, 255, 0.3);\n\tz-index: 1000;\n\toverflow: hidden;\n}\n\n.result-bottom-bar::before {\n\tcontent: '';\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\theight: 2rpx;\n\tbackground: linear-gradient(90deg, transparent 0%, rgba(0, 247, 255, 0.8) 50%, transparent 100%);\n\tanimation: topGlow 3s ease-in-out infinite;\n}\n\n@keyframes topGlow {\n\t0%, 100% { opacity: 0.3; }\n\t50% { opacity: 1; }\n}\n\n/* 科技按钮容器 */\n.tech-button-container {\n\tposition: relative;\n\twidth: 100%;\n}\n\n/* 科技装饰线条 */\n.tech-lines {\n\tposition: absolute;\n\ttop: -15rpx;\n\tleft: 0;\n\tright: 0;\n\theight: 2rpx;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tz-index: 1;\n}\n\n.tech-line {\n\twidth: 30%;\n\theight: 2rpx;\n\topacity: 0.8;\n\tanimation: techLinePulse 2s ease-in-out infinite;\n}\n\n.tech-line.left {\n\tanimation-delay: 0s;\n}\n\n.tech-line.right {\n\tanimation-delay: 1s;\n}\n\n@keyframes techLinePulse {\n\t0%, 100% {\n\t\topacity: 0.3;\n\t\ttransform: scaleX(0.8);\n\t}\n\t50% {\n\t\topacity: 1;\n\t\ttransform: scaleX(1);\n\t}\n}\n\n/* 主按钮 */\n.bottom-chat-btn {\n\tposition: relative;\n\twidth: 100%;\n\tpadding: 0 !important;\n\tborder-radius: 15rpx !important;\n\tborder: 2rpx solid rgba(0, 247, 255, 0.4) !important;\n\tbackground: linear-gradient(135deg, rgba(0, 247, 255, 0.1) 0%, rgba(0, 150, 255, 0.2) 100%) !important;\n\tbackdrop-filter: blur(15rpx);\n\toverflow: hidden;\n\ttransition: all 0.4s ease;\n\tbox-shadow:\n\t\t0 8rpx 32rpx rgba(0, 247, 255, 0.3),\n\t\tinset 0 1rpx 0 rgba(255, 255, 255, 0.1);\n}\n\n/* 按钮背景装饰 */\n.btn-bg-decoration {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\toverflow: hidden;\n\tpointer-events: none;\n}\n\n.bg-grid {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground-image:\n\t\tlinear-gradient(rgba(0, 247, 255, 0.1) 1rpx, transparent 1rpx),\n\t\tlinear-gradient(90deg, rgba(0, 247, 255, 0.1) 1rpx, transparent 1rpx);\n\tbackground-size: 20rpx 20rpx;\n\tanimation: gridMove 4s linear infinite;\n}\n\n.bg-particles {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n}\n\n.particle {\n\tposition: absolute;\n\twidth: 4rpx;\n\theight: 4rpx;\n\tbackground: rgba(0, 247, 255, 0.6);\n\tborder-radius: 50%;\n\tanimation: particleFloat 3s ease-in-out infinite;\n}\n\n.particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }\n.particle:nth-child(2) { top: 60%; left: 20%; animation-delay: 0.5s; }\n.particle:nth-child(3) { top: 30%; left: 70%; animation-delay: 1s; }\n.particle:nth-child(4) { top: 80%; left: 60%; animation-delay: 1.5s; }\n.particle:nth-child(5) { top: 40%; left: 90%; animation-delay: 2s; }\n.particle:nth-child(6) { top: 70%; left: 40%; animation-delay: 2.5s; }\n\n@keyframes particleFloat {\n\t0%, 100% {\n\t\topacity: 0.3;\n\t\ttransform: translateY(0) scale(1);\n\t}\n\t50% {\n\t\topacity: 1;\n\t\ttransform: translateY(-10rpx) scale(1.2);\n\t}\n}\n\n/* 按钮内容 */\n.btn-content {\n\tposition: relative;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 25rpx 40rpx;\n\tz-index: 2;\n}\n\n.btn-icon-container {\n\tposition: relative;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.chat-btn-icon {\n\tfont-size: 28rpx;\n\tz-index: 2;\n}\n\n.icon-glow {\n\tposition: absolute;\n\twidth: 50rpx;\n\theight: 50rpx;\n\tborder-radius: 50%;\n\tanimation: iconGlow 2s ease-in-out infinite;\n}\n\n@keyframes iconGlow {\n\t0%, 100% {\n\t\topacity: 0.3;\n\t\ttransform: scale(0.8);\n\t}\n\t50% {\n\t\topacity: 0.8;\n\t\ttransform: scale(1.2);\n\t}\n}\n\n.btn-text-container {\n\tflex: 1;\n\ttext-align: center;\n\tmargin: 0 20rpx;\n}\n\n.chat-btn-text {\n\tdisplay: block;\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #ffffff;\n\ttext-shadow: 0 0 10rpx rgba(0, 247, 255, 0.8);\n\tmargin-bottom: 3rpx;\n}\n\n.chat-btn-subtitle {\n\tdisplay: block;\n\tfont-size: 18rpx;\n\tcolor: rgba(0, 247, 255, 0.8);\n\tfont-weight: 300;\n\tletter-spacing: 2rpx;\n}\n\n.btn-arrow {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.arrow-symbol {\n\tfont-size: 24rpx;\n\tcolor: rgba(0, 247, 255, 0.9);\n\tanimation: arrowMove 1.5s ease-in-out infinite;\n}\n\n@keyframes arrowMove {\n\t0%, 100% { transform: translateX(0); }\n\t50% { transform: translateX(8rpx); }\n}\n\n/* 扫描线效果 */\n.scan-line {\n\tposition: absolute;\n\ttop: 0;\n\tleft: -100%;\n\twidth: 100%;\n\theight: 100%;\n\tbackground: linear-gradient(90deg, transparent 0%, rgba(0, 247, 255, 0.3) 50%, transparent 100%);\n\tanimation: scanMove 3s ease-in-out infinite;\n\tz-index: 1;\n}\n\n@keyframes scanMove {\n\t0% { left: -100%; }\n\t50% { left: 100%; }\n\t100% { left: -100%; }\n}\n\n/* 底部装饰 */\n.tech-decoration {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tgap: 15rpx;\n\tmargin-top: 15rpx;\n}\n\n.deco-dot {\n\twidth: 8rpx;\n\theight: 8rpx;\n\tborder-radius: 50%;\n\tanimation: dotPulse 2s ease-in-out infinite;\n}\n\n.deco-dot:nth-child(1) { animation-delay: 0s; }\n.deco-dot:nth-child(2) { animation-delay: 0.2s; }\n.deco-dot:nth-child(3) { animation-delay: 0.4s; }\n.deco-dot:nth-child(4) { animation-delay: 0.6s; }\n.deco-dot:nth-child(5) { animation-delay: 0.8s; }\n\n@keyframes dotPulse {\n\t0%, 100% {\n\t\topacity: 0.3;\n\t\ttransform: scale(0.8);\n\t}\n\t50% {\n\t\topacity: 1;\n\t\ttransform: scale(1.2);\n\t}\n}\n\n/* 按钮交互效果 */\n.bottom-chat-btn::after {\n\tborder: none !important;\n}\n\n.bottom-chat-btn:active {\n\ttransform: scale(0.98);\n\tbox-shadow:\n\t\t0 4rpx 16rpx rgba(0, 247, 255, 0.4),\n\t\tinset 0 1rpx 0 rgba(255, 255, 255, 0.2);\n}\n\n.bottom-chat-btn:hover {\n\tborder-color: rgba(0, 247, 255, 0.8);\n\tbox-shadow:\n\t\t0 12rpx 40rpx rgba(0, 247, 255, 0.5),\n\t\tinset 0 1rpx 0 rgba(255, 255, 255, 0.2);\n}\n\n.action-btn::after {\n\tborder: none !important;\n}\n\n.action-btn:active {\n\ttransform: scale(0.95);\n}\n\n.action-btn.primary {\n\tbackground: linear-gradient(45deg, #00f7ff, rgba(0, 247, 255, 0.8)) !important;\n\tcolor: #fff !important;\n\tbox-shadow: 0 8rpx 25rpx rgba(0, 247, 255, 0.4);\n}\n\n.action-btn.secondary {\n\tbackground: rgba(125, 249, 255, 0.1) !important;\n\tcolor: rgba(125, 249, 255, 0.9) !important;\n\tborder: 2rpx solid rgba(125, 249, 255, 0.3) !important;\n\tbackdrop-filter: blur(10rpx);\n}\n\n.btn-icon {\n\tfont-size: 24rpx;\n}\n\n.btn-text {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n}\n\n/* 配置模态框 - 移动端优化 */\n.config-modal {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tz-index: 999999;\n\tdisplay: flex;\n\talign-items: flex-end;\n\tjustify-content: center;\n}\n\n.config-overlay {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tbackground: rgba(0, 0, 0, 0.7);\n\tbackdrop-filter: blur(10rpx);\n}\n\n/* 移动端配置容器 */\n.config-container-mobile {\n\tposition: relative;\n\tbackground: linear-gradient(135deg, rgba(10, 10, 42, 0.98) 0%, rgba(26, 26, 58, 0.98) 50%, rgba(42, 42, 74, 0.98) 100%);\n\tbackdrop-filter: blur(30rpx);\n\twidth: 100%;\n\tmax-height: 85vh;\n\tborder-radius: 30rpx 30rpx 0 0;\n\toverflow: hidden;\n\tdisplay: flex;\n\tflex-direction: column;\n\tborder: 2rpx solid rgba(125, 249, 255, 0.2);\n\tborder-bottom: none;\n\tbox-shadow: 0 -10rpx 40rpx rgba(0, 0, 0, 0.5);\n\tanimation: slideUp 0.3s ease-out;\n}\n\n@keyframes slideUp {\n\tfrom {\n\t\ttransform: translateY(100%);\n\t\topacity: 0;\n\t}\n\tto {\n\t\ttransform: translateY(0);\n\t\topacity: 1;\n\t}\n}\n\n/* 移动端头部 */\n.config-header-mobile {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 30rpx 30rpx 20rpx;\n\tborder-bottom: 1rpx solid rgba(125, 249, 255, 0.2);\n\tmin-height: 80rpx;\n}\n\n.header-left {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 15rpx;\n}\n\n.title-icon {\n\tfont-size: 32rpx;\n}\n\n.config-title-mobile {\n\tfont-size: 28rpx;\n\tcolor: #00f7ff;\n\tfont-weight: bold;\n}\n\n.config-close-mobile {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tbackground: rgba(255, 68, 68, 0.2);\n\tborder: 1rpx solid rgba(255, 68, 68, 0.3);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\ttransition: all 0.3s ease;\n}\n\n.config-close-mobile:active {\n\ttransform: scale(0.9);\n\tbackground: rgba(255, 68, 68, 0.3);\n}\n\n.close-text {\n\tfont-size: 28rpx;\n\tcolor: #ff6666;\n\tfont-weight: bold;\n}\n\n/* 移动端滚动内容 */\n.config-scroll-mobile {\n\tflex: 1;\n\tpadding: 20rpx 30rpx 0;\n}\n\n.config-tip-mobile {\n\tbackground: rgba(125, 249, 255, 0.1);\n\tborder: 1rpx solid rgba(125, 249, 255, 0.3);\n\tborder-radius: 20rpx;\n\tpadding: 20rpx;\n\tmargin-bottom: 30rpx;\n\tbackdrop-filter: blur(10rpx);\n\ttext-align: center;\n}\n\n.tip-text-mobile {\n\tfont-size: 24rpx;\n\tcolor: rgba(125, 249, 255, 0.9);\n\tline-height: 1.5;\n}\n\n/* 移动端配置区块 */\n.config-section-mobile {\n\tmargin-bottom: 40rpx;\n}\n\n.section-title-mobile {\n\tfont-size: 26rpx;\n\tcolor: #00f7ff;\n\tfont-weight: bold;\n\tmargin-bottom: 20rpx;\n\tdisplay: block;\n}\n\n/* 移动端性别选择网格 */\n.gender-grid-mobile {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(3, 1fr);\n\tgap: 15rpx;\n}\n\n.gender-item-mobile {\n\tbackground: rgba(125, 249, 255, 0.1);\n\tborder: 2rpx solid rgba(125, 249, 255, 0.3);\n\tborder-radius: 20rpx;\n\tpadding: 20rpx 15rpx;\n\ttransition: all 0.3s ease;\n\tbackdrop-filter: blur(10rpx);\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 8rpx;\n\tmin-height: 100rpx;\n}\n\n.gender-item-mobile:active {\n\ttransform: scale(0.95);\n}\n\n.gender-item-mobile.active {\n\tbackground: rgba(125, 249, 255, 0.2);\n\tborder-color: #00f7ff;\n\tbox-shadow: 0 0 15rpx rgba(0, 247, 255, 0.4);\n}\n\n.gender-icon-mobile {\n\tfont-size: 32rpx;\n\tmargin-bottom: 5rpx;\n}\n\n.gender-text-mobile {\n\tfont-size: 22rpx;\n\tcolor: rgba(125, 249, 255, 0.9);\n\tfont-weight: 500;\n\ttext-align: center;\n}\n\n/* 移动端输入框 */\n.input-wrapper-mobile {\n\twidth: 100%;\n}\n\n.input-container-mobile {\n\tposition: relative;\n\tbackground: rgba(125, 249, 255, 0.1);\n\tborder: 2rpx solid rgba(125, 249, 255, 0.3);\n\tborder-radius: 20rpx;\n\toverflow: hidden;\n\tbackdrop-filter: blur(10rpx);\n\ttransition: all 0.3s ease;\n}\n\n.input-container-mobile:focus-within {\n\tborder-color: rgba(125, 249, 255, 0.8);\n\tbackground: rgba(125, 249, 255, 0.15);\n\tbox-shadow: 0 0 15rpx rgba(125, 249, 255, 0.3);\n}\n\n.profession-input-mobile {\n\twidth: 100%;\n\tbackground: transparent;\n\tborder: none;\n\toutline: none;\n\tpadding: 25rpx 70rpx 25rpx 25rpx;\n\tfont-size: 24rpx;\n\tcolor: #7df9ff;\n\tline-height: 1.4;\n\tmin-height: 70rpx;\n\tbox-sizing: border-box;\n}\n\n/* 移动端输入框placeholder */\n.profession-input-mobile::-webkit-input-placeholder {\n\tcolor: rgba(125, 249, 255, 0.5);\n\tfont-size: 24rpx;\n}\n\n.input-icon-mobile {\n\tposition: absolute;\n\tright: 25rpx;\n\ttop: 50%;\n\ttransform: translateY(-50%);\n\tfont-size: 24rpx;\n\ttransition: all 0.3s ease;\n\tpointer-events: none;\n}\n\n.input-status-mobile {\n\tmargin-top: 10rpx;\n\tpadding: 8rpx 15rpx;\n\tbackground: rgba(125, 249, 255, 0.05);\n\tborder-radius: 15rpx;\n\tborder: 1rpx solid rgba(125, 249, 255, 0.2);\n}\n\n.status-text-mobile {\n\tfont-size: 20rpx;\n\tcolor: rgba(125, 249, 255, 0.8);\n}\n\n.input-tip {\n\tmargin-top: 15rpx;\n\tpadding: 10rpx 20rpx;\n\tbackground: rgba(125, 249, 255, 0.05);\n\tborder-radius: 15rpx;\n\tborder: 1rpx solid rgba(125, 249, 255, 0.2);\n}\n\n.input-tip .tip-text {\n\tfont-size: 22rpx;\n\tcolor: rgba(125, 249, 255, 0.8);\n}\n\n.input-debug {\n\tmargin-top: 15rpx;\n\tpadding: 10rpx 20rpx;\n\tbackground: rgba(255, 165, 0, 0.1);\n\tborder-radius: 15rpx;\n\tborder: 1rpx solid rgba(255, 165, 0, 0.3);\n}\n\n.input-debug .debug-text {\n\tfont-size: 22rpx;\n\tcolor: rgba(255, 165, 0, 0.8);\n}\n\n/* 移动端底部操作区 */\n.config-actions-mobile {\n\tpadding: 20rpx 30rpx;\n\tborder-top: 1rpx solid rgba(125, 249, 255, 0.2);\n\tdisplay: flex;\n\tgap: 15rpx;\n\tbackground: rgba(10, 10, 42, 0.8);\n\tbackdrop-filter: blur(20rpx);\n}\n\n.action-btn-mobile {\n\tflex: 1;\n\tpadding: 25rpx 20rpx !important;\n\tborder-radius: 25rpx !important;\n\tdisplay: flex !important;\n\talign-items: center !important;\n\tjustify-content: center !important;\n\tgap: 8rpx !important;\n\tfont-size: 24rpx !important;\n\tfont-weight: bold;\n\tborder: none !important;\n\ttransition: all 0.3s ease;\n\tmin-height: 80rpx;\n}\n\n.action-btn-mobile::after {\n\tborder: none !important;\n}\n\n.action-btn-mobile:active {\n\ttransform: scale(0.95);\n}\n\n.action-btn-mobile.secondary {\n\tbackground: rgba(125, 249, 255, 0.1) !important;\n\tcolor: rgba(125, 249, 255, 0.8) !important;\n\tborder: 1rpx solid rgba(125, 249, 255, 0.3) !important;\n}\n\n.action-btn-mobile.primary {\n\tbackground: linear-gradient(45deg, #00f7ff, rgba(0, 247, 255, 0.8)) !important;\n\tcolor: #fff !important;\n\tbox-shadow: 0 5rpx 20rpx rgba(0, 247, 255, 0.3);\n}\n\n.action-btn-mobile.primary:disabled {\n\topacity: 0.5;\n\tpointer-events: none;\n}\n\n.btn-icon-mobile {\n\tfont-size: 20rpx;\n}\n\n.btn-text-mobile {\n\tfont-size: 24rpx;\n\tfont-weight: bold;\n}\n\n.bottom-spacer-mobile {\n\theight: 40rpx;\n}\n\n/* 响应式设计 */\n@media screen and (max-width: 750rpx) {\n\t.comparison-container {\n\t\tflex-direction: row;\n\t\tgap: 15rpx;\n\t\tmax-width: 100%;\n\t}\n\n\t.image-item {\n\t\tmin-width: 200rpx;\n\t\tmax-width: 250rpx;\n\t}\n\n\t.transform-arrow {\n\t\tmargin: 0 10rpx;\n\t\tflex-shrink: 0;\n\t}\n\n\t.arrow-text {\n\t\tfont-size: 18rpx;\n\t\tpadding: 6rpx 10rpx;\n\t}\n\n\t.result-bottom-bar {\n\t\tpadding: 25rpx 30rpx;\n\t\tpadding-bottom: calc(25rpx + env(safe-area-inset-bottom));\n\t}\n\n\t.btn-content {\n\t\tpadding: 22rpx 35rpx;\n\t}\n\n\t.chat-btn-icon {\n\t\tfont-size: 28rpx;\n\t}\n\n\t.chat-btn-text {\n\t\tfont-size: 28rpx;\n\t}\n\n\t.chat-btn-subtitle {\n\t\tfont-size: 18rpx;\n\t}\n\n\t.arrow-symbol {\n\t\tfont-size: 24rpx;\n\t}\n\n\t.tech-decoration {\n\t\tmargin-top: 15rpx;\n\t\tgap: 12rpx;\n\t}\n\n\t.deco-dot {\n\t\twidth: 6rpx;\n\t\theight: 6rpx;\n\t}\n\n\t.processing-steps {\n\t\tgap: 25rpx;\n\t\tflex-wrap: nowrap;\n\t}\n\n\t.step-item {\n\t\tmin-width: 80rpx;\n\t\tmax-width: 120rpx;\n\t}\n\n\t.step-icon {\n\t\twidth: 50rpx;\n\t\theight: 50rpx;\n\t}\n\n\t.step-label {\n\t\tfont-size: 20rpx;\n\t}\n}\n\n/* 粒子动画随机位置 */\n.particle:nth-child(1) { left: 10%; animation-duration: 6s; }\n.particle:nth-child(2) { left: 20%; animation-duration: 8s; }\n.particle:nth-child(3) { left: 30%; animation-duration: 7s; }\n.particle:nth-child(4) { left: 40%; animation-duration: 9s; }\n.particle:nth-child(5) { left: 50%; animation-duration: 6s; }\n.particle:nth-child(6) { left: 60%; animation-duration: 8s; }\n.particle:nth-child(7) { left: 70%; animation-duration: 7s; }\n.particle:nth-child(8) { left: 80%; animation-duration: 9s; }\n.particle:nth-child(9) { left: 90%; animation-duration: 6s; }\n.particle:nth-child(10) { left: 15%; animation-duration: 8s; }\n.particle:nth-child(11) { left: 25%; animation-duration: 7s; }\n.particle:nth-child(12) { left: 35%; animation-duration: 9s; }\n.particle:nth-child(13) { left: 45%; animation-duration: 6s; }\n.particle:nth-child(14) { left: 55%; animation-duration: 8s; }\n.particle:nth-child(15) { left: 65%; animation-duration: 7s; }\n.particle:nth-child(16) { left: 75%; animation-duration: 9s; }\n.particle:nth-child(17) { left: 85%; animation-duration: 6s; }\n.particle:nth-child(18) { left: 95%; animation-duration: 8s; }\n.particle:nth-child(19) { left: 5%; animation-duration: 7s; }\n.particle:nth-child(20) { left: 95%; animation-duration: 9s; }\n\n/* 预测徽章 */\n.prediction-badge {\n\tposition: absolute;\n\ttop: 20rpx;\n\tright: 20rpx;\n\tbackground: linear-gradient(45deg, rgba(189, 0, 255, 0.8), rgba(189, 0, 255, 0.6));\n\tpadding: 8rpx 20rpx;\n\tborder-radius: 20rpx;\n\tbackdrop-filter: blur(10rpx);\n\tborder: 1rpx solid rgba(189, 0, 255, 0.5);\n\tbox-shadow: 0 4rpx 15rpx rgba(189, 0, 255, 0.3);\n}\n\n.badge-text {\n\tfont-size: 20rpx;\n\tcolor: #fff;\n\tfont-weight: bold;\n\tletter-spacing: 1rpx;\n}\n\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./camera-new.vue?vue&type=style&index=0&id=368f168d&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./camera-new.vue?vue&type=style&index=0&id=368f168d&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754021869650\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}